<script lang="ts" setup>
import TreeCom from './components/tree.vue'
import TableCom from './components/table.vue'
import { useAccountContextInformation } from './hook/use-account-context-information'

import { formatTreeData } from '@/utils'

import type { Tree } from '@/types/tree'
import { useLoadingHook } from '@/hooks/useLoading'
import { appMessage } from '@/hooks/useNaiveApi'
import { ROUTER_PATH } from '@/router/router-path'
import { getOrgsForAddOrUpdateApi, getSysManagerByPageApi, deleteSysManagerApi } from '@/api/system/admin-manage.api'
import type {
  OrgsForAddOrUpdateModel,
  GetSysManagerByPageInput,
  GetSysManagerByPageOutput,
  ManagerByPageItemModel,
} from '@/api/dto/system/admin-manage.dto'
import { useEditPasswordHook } from '@/hooks/useEditPassword'
import type { OperaRow } from '@/api/dto/index.dto'

const tableComRef = ref<null | InstanceType<typeof TableCom>>(null)

const treeData = ref<Tree<OrgsForAddOrUpdateModel>[]>([])
const { loading: treeLoading, loadingFunc: treeLoadingFunc } = useLoadingHook()

/**获取机构tree */
const getOrgsForAddOrUpdate = async () => {
  try {
    const data = await getOrgsForAddOrUpdateApi({ loading: treeLoadingFunc, showNoData: false })
    treeData.value = formatTreeData<OrgsForAddOrUpdateModel>(data, 'organizationName', 'organizationId')
  } catch (error) {
    treeData.value = []
  }
}

onMounted(() => {
  getOrgsForAddOrUpdate()
  tableComRef.value?.getData()
})

const organizationId = ref('')

const handleSelectAll = () => {
  organizationId.value = ''
}

const handleSelect = (target: Tree<OrgsForAddOrUpdateModel>) => {
  organizationId.value = target.extData.organizationId
}

const tableData = ref<GetSysManagerByPageOutput>([])
const total = ref(1)
const { loading: tableLoading, loadingFunc: tableLoadingFunc } = useLoadingHook()

/**获取机构信息列表 */
const getSysManagerByPage = async (param: GetSysManagerByPageInput) => {
  try {
    const { data, recordCount } = await getSysManagerByPageApi(param, {
      loading: tableLoadingFunc,
      retonly: false,
      showNoData: false,
    })
    tableData.value = data
    total.value = recordCount!
  } catch (error) {
    tableData.value = []
    total.value = 1
  }
}

const router = useRouter()
/**新增机构 */
const handleNewAccount = async () => {
  if (!treeData.value.length) {
    try {
      await ElMessageBox.confirm('暂没有组织机构，请先添加', '提示', {
        type: 'warning',
        confirmButtonText: '去添加',
        cancelButtonText: '稍后',
      })
      router.push(ROUTER_PATH.ORGANIZATION)
    } finally {
      return
    }
  }
  router.push({ path: ROUTER_PATH.ADMIN_MANAGE_DETAILS })
}
/**修改机构 */
const handleEditInfo = async (target: OperaRow<ManagerByPageItemModel>) => {
  const { managerId } = target
  router.push({ path: ROUTER_PATH.ADMIN_MANAGE_DETAILS, query: { managerId } })
}

/**查看关联信息 */
const handleViewBind = async (target: OperaRow<ManagerByPageItemModel>) => {
  const { realName, managerId } = target
  useAccountContextInformation({ accountName: realName, managerId })
}

/**修改密码 */
const handleResetPassword = async (target: OperaRow<ManagerByPageItemModel>) => {
  try {
    const { managerId } = target
    await useEditPasswordHook({ managerId })
  } catch (error) {}
}

/**删除账号 */
const handleDeleteAccount = async (target: OperaRow<ManagerByPageItemModel>) => {
  const { realName, managerId } = target
  try {
    await ElMessageBox.confirm(`确定要删除账号【${realName}】吗?`, '警告', { type: 'warning' })
    await deleteSysManagerApi({ id: managerId }, { loading: (flag) => (target.loading = flag) })
    appMessage.success('删除成功')
    tableComRef.value!.getData()
  } catch (error) {}
}
</script>

<template>
  <div class="admin_manage-container flex">
    <TreeCom v-loading="treeLoading" :tree-data="treeData" @select-all="handleSelectAll" @select="handleSelect" />

    <TableCom
      ref="tableComRef"
      v-loading="tableLoading"
      :organization-id="organizationId"
      :table-data="tableData"
      :total="total"
      @search="getSysManagerByPage"
      @new-account="handleNewAccount"
      @edit-info="handleEditInfo"
      @view-bind="handleViewBind"
      @reset-password="handleResetPassword"
      @delete-account="handleDeleteAccount"
    />
  </div>
</template>

<style lang="scss" scoped>
.admin_manage-container {
  height: calc(100vh - 90px);
  background-color: var(--el-bg-color-page);
}
</style>
