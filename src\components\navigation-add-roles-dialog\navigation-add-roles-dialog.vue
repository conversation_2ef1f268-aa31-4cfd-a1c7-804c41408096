<script lang="ts" setup>
import type {
  // AddSysFunctionauthInput,
  GetSysFunctionauthForUpdateOutput
  // UpdateSysFunctionauthInput,
} from '@/api/dto/system/navigation.dto'
import {
  addSysFunctionauthApi,
  getSysFunctionauthForUpdateApi,
  updateSysFunctionauthApi
} from '@/api/system/navigation.api'
import ElConfig from '@/components/el-config'
import { useElDialogHook } from '@/hooks/useDialog'
import { useFormHook } from '@/hooks/useForm'
import { useLoadingHook } from '@/hooks/useLoading'
import { appMessage } from '@/hooks/useNaiveApi'
import type { FormRules } from 'element-plus'

export interface OpenParams {
  navigationName: string
  navigationId: string
  functionAuthId?: string
}

export type DialogData = {} & OpenParams & GetSysFunctionauthForUpdateOutput

const { dialogVisible, __open, __close, promiseFunc } = useElDialogHook<boolean>()

const { formRef, validate, resetForm } = useFormHook()

const dialogData = reactive<DialogData>({
  navigationName: '',
  navigationId: '',
  functionAuthId: '',

  functionAuthName: '',
  encode: '',
  action: '',
  area: '',
  controller: '',
  enabledMark: true,
  isSys: false
})
/**表单验证规则 */
const rules = reactive<FormRules<DialogData>>({
  functionAuthName: [{ required: true, message: '请选择菜单类型', trigger: 'blur' }],
  encode: [{ required: true, message: '请输入菜单名字', trigger: 'blur' }]
})

const dialogTitle = computed(() => {
  if (dialogData.functionAuthId) {
    return `${dialogData.navigationName} - 按钮权限`
  } else {
    return `${dialogData.navigationName} - 按钮权限新增`
  }
})

const resetDialogData = () => {
  dialogData.navigationName = ''
  dialogData.navigationId = ''
  dialogData.functionAuthId = ''
}

const { loading, loadingFunc } = useLoadingHook()

const addSysFunctionauth = async () => {
  await addSysFunctionauthApi(
    {
      navigationId: dialogData.navigationId,
      functionAuthName: dialogData.functionAuthName,
      encode: dialogData.encode,
      area: dialogData.area,
      controller: dialogData.controller,
      action: dialogData.action,
      enabledMark: dialogData.enabledMark,
      isSys: dialogData.isSys,
      elementscript: ''
    },
    { loading: loadingFunc }
  )
}

const updateSysFunctionauth = async () => {
  await updateSysFunctionauthApi(
    {
      navigationId: dialogData.navigationId,
      functionAuthId: dialogData.functionAuthId,
      functionAuthName: dialogData.functionAuthName,
      encode: dialogData.encode,
      area: dialogData.area,
      controller: dialogData.controller,
      action: dialogData.action,
      enabledMark: dialogData.enabledMark,
      isSys: dialogData.isSys,
      elementscript: ''
    },
    { loading: loadingFunc }
  )
}

const confrim = async () => {
  try {
    await validate()

    if (dialogData.functionAuthId) {
      // 修改
      await updateSysFunctionauth()
      appMessage.success('修改成功')
    } else {
      // 添加
      await addSysFunctionauth()
      appMessage.success('添加成功')
    }

    promiseFunc.resolve?.(true)
    __close()
  } catch (error) {}
}

/**获取菜单按钮权限 */
const getSysFunctionauthForUpdate = async () => {
  try {
    const data = await getSysFunctionauthForUpdateApi({ functionAuthId: dialogData.functionAuthId })
    dialogData.functionAuthName = data.functionAuthName
    dialogData.encode = data.encode
    dialogData.action = data.action
    dialogData.area = data.area
    dialogData.controller = data.controller
    dialogData.enabledMark = data.enabledMark
    dialogData.isSys = data.isSys
  } catch (error) {
    __close()
    promiseFunc.reject?.('获取按钮权限详情失败，请稍后重试')
  }
}

const handleOpenDialog = async (openParams: OpenParams) => {
  setTimeout(() => {
    dialogData.navigationName = openParams.navigationName
    dialogData.navigationId = openParams.navigationId
    if (openParams.functionAuthId) {
      dialogData.functionAuthId = openParams.functionAuthId
      getSysFunctionauthForUpdate()
    }
  })

  return await __open()
}

const handleCloseDialog = () => {
  __close()
  promiseFunc.reject?.('取消')
}

const handleClosed = () => {
  resetForm()
  resetDialogData()
}

defineExpose({
  __open: handleOpenDialog,
  __close: handleCloseDialog
})
</script>

<template>
  <ElConfig>
    <el-dialog v-model="dialogVisible" width="600" class="navigation_add_roles_dialog-container" @closed="handleClosed">
      <template #header>
        <div class="title">{{ dialogTitle }}</div>
      </template>

      <el-form ref="formRef" :model="dialogData" :rules="rules" label-width="120">
        <el-form-item prop="navigationType" label="类型："> 按钮 </el-form-item>

        <el-form-item prop="functionAuthName" label="名称：">
          <el-input v-model="dialogData.functionAuthName" placeholder="请输入名称"></el-input>
        </el-form-item>

        <el-form-item prop="encode" label="内部编码：">
          <el-input v-model="dialogData.encode" placeholder="请输入内部编码"></el-input>
        </el-form-item>

        <el-form-item prop="area" label="区域地址：">
          <el-input v-model="dialogData.area" placeholder="请输入区域地址"></el-input>
        </el-form-item>

        <el-form-item prop="controller" label="控制器地址：">
          <el-input v-model="dialogData.controller" placeholder="请输入控制器地址"></el-input>
        </el-form-item>

        <el-form-item prop="action" label="行为地址：">
          <el-input v-model="dialogData.action" placeholder="请输入行为地址"></el-input>
        </el-form-item>

        <el-form-item label="是否启用：" prop="enabledMark">
          <el-switch v-model="dialogData.enabledMark" active-text="启用后权限才会生效"> </el-switch>
        </el-form-item>

        <el-form-item label="系统管理：" prop="isSys">
          <el-switch v-model="dialogData.isSys" active-text="开启后无法删除"> </el-switch>
        </el-form-item>

        <!-- 服务端没返回 -->
        <!-- <el-form-item prop="sortCode" label="排序：">
          <el-input-number v-model="dialogData.sortCode" :min="0"></el-input-number>
        </el-form-item>

        <el-form-item prop="description" label="业务描述：">
          <el-input v-model="dialogData.description" type="textarea" placeholder="请输入业务描述"></el-input>
        </el-form-item> -->
      </el-form>

      <template #footer>
        <el-button :disabled="loading" @click="handleCloseDialog">取 消</el-button>
        <el-button :loading="loading" type="primary" @click="confrim">确 定</el-button>
      </template>
    </el-dialog>
  </ElConfig>
</template>

<style lang="scss" scoped>
.navigation_add_roles_dialog-container {
  .title {
    height: 44px;
    padding: 10px 16px;
    font-size: 18px;
    color: var(--el-text-color-regular);
  }
}
</style>
