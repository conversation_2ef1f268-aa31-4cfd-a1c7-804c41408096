<script setup lang="ts">
import { WarningFilled } from '@element-plus/icons-vue'
import {
  requestSettlementInvoiceAutoFillInfo,
  requestSettlementInvoiceAutoFillUpdate
} from '@/api/discharge-settlement.api'

/**
 * 结算设置
 */

const isUseElectronicInvoice = ref<string>()

let _settingId = ''

onMounted(async () => {
  await handleGetSettlementInvoiceAutoFillInfo()
})

// 获取发票自动填充启用情况
async function handleGetSettlementInvoiceAutoFillInfo() {
  const res = await requestSettlementInvoiceAutoFillInfo()
  isUseElectronicInvoice.value = res.enabledMark ? '1' : '0'
  _settingId = res.settingId
}

// 修改发票自动填充启用情况
async function handleUpdateSettlementInvoiceAutoFillInfo() {
  if (!_settingId) {
    ElMessage.error('设置ID不能为空')
    return
  }
  if (isUseElectronicInvoice.value === undefined) {
    ElMessage.error('请选择是否使用电子发票')
    return
  }
  await requestSettlementInvoiceAutoFillUpdate({
    settingId: _settingId,
    enabledMark: isUseElectronicInvoice.value === '1'
  })
  ElMessage.success('修改成功')
  await handleGetSettlementInvoiceAutoFillInfo()
}
</script>

<template>
  <div class="page-container">
    <div class="header">
      <div class="header-tips">
        <el-icon class="header-tips-icon"><WarningFilled /></el-icon>
        <span>出院结算功能设置模块，请在设置完点击确定保存按钮，即可生效</span>
      </div>
      <el-button type="primary" class="header-button" @click="handleUpdateSettlementInvoiceAutoFillInfo">
        确定保存
      </el-button>
    </div>

    <div class="content">
      <span>是否使用电子发票：</span>
      <el-select v-model="isUseElectronicInvoice" placeholder="请选择" class="content-select">
        <el-option label="是" value="1" />
        <el-option label="否" value="0" />
      </el-select>
      <span class="content-tips">注：设置使用电子发票后，出院清账与结算退费的发票印刷号则自动填充</span>
    </div>
  </div>
</template>

<style scoped lang="scss">
.page-container {
  padding: 30px 20px;
}

.header {
  display: flex;
  gap: 12px;

  .header-tips {
    flex: 1;
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 0 12px;
    border-radius: 4px;
    background: #ecf5ff;
    border: 1px solid #d9ecff;
    color: #1890ff;
  }

  .header-tips-icon {
    font-size: 20px;
  }

  .header-button {
    width: 96px;
    height: 40px;
  }
}

.content {
  margin-top: 30px;

  .content-select {
    width: 400px;
    height: 40px;
  }

  .content-tips {
    color: #909399;
    margin-left: 12px;
  }
}
</style>
