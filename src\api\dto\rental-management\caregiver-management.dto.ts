/**
 * 陪护人租赁管理 - 陪护人管理
 */

/**
 * 陪护人管理 - 分页获取列表 - 请求参数
 */
export interface ReqGetInpatientCaregiverPersonByPage {
  /** 陪护人名称 */
  name?: string
  /** 是否陪护中 true是 false否 */
  isCaregiver?: boolean
  /** 状态 true启用 false禁用 */
  status?: boolean
  /** 当前页 */
  page: number
  /** 每页的记录数 */
  rows: number
}

/**
 * 陪护人管理 - 分页获取列表 - 列表项
 */
export interface ResInpatientCaregiverPersonItem {
  /** ID */
  inpatientCaregiverPersonId: string
  /** 陪护人名称 */
  name: string
  /** 性别，1男 2女 0未知 */
  gender: string
  /** 年龄 */
  age: number
  /** 身份证号 */
  idCard: string
  /** 联系电话 */
  phone: string
  /** 是否陪护中 true是 false否 */
  isCaregiver: boolean
  /** 状态 true启用 false禁用 */
  status: boolean
  /** 备注 */
  remark?: string
  /** 更新时间 */
  modifyTime: string
}

/**
 * 陪护人管理 - 分页获取列表 - 响应数据
 */
export interface ResGetInpatientCaregiverPersonByPage {
  /** 实体列表 */
  data: ResInpatientCaregiverPersonItem[]
  /** 当前页 */
  pageIndex: number
  /** 每页记录数 */
  pageSize: number
  /** 总记录数 */
  recordCount: number
  /** 总页数 */
  pageCount: number
}

/**
 * 陪护人管理 - 新增 - 请求参数
 */
export interface ReqAddInpatientCaregiverPerson {
  /** 陪护人名称 */
  name: string
  /** 性别 */
  gender: string
  /** 年龄 */
  age: number
  /** 身份证号 */
  idCard: string
  /** 联系电话 */
  phone: string
  /** 状态 true启用 false禁用 */
  status: boolean
  /** 备注 */
  remark?: string
}

/**
 * 陪护人管理 - 修改 - 请求参数
 */
export interface ReqUpdateInpatientCaregiverPerson {
  /** ID */
  inpatientCaregiverPersonId: string
  /** 陪护人名称 */
  name: string
  /** 性别 */
  gender: string
  /** 年龄 */
  age: number
  /** 身份证号 */
  idCard: string
  /** 联系电话 */
  phone: string
  /** 状态 true启用 false禁用 */
  status: boolean
  /** 备注 */
  remark?: string
}

/**
 * 陪护人管理 - 启用/禁用/删除 - 请求参数
 */
export interface ReqInpatientCaregiverPersonId {
  /** ID */
  inpatientCaregiverPersonId: string
}

/**
 * 陪护人管理 - 公共响应信息
 */
export interface ResInpatientCaregiverPersonMsg {
  /** 消息 */
  msg: string
}
