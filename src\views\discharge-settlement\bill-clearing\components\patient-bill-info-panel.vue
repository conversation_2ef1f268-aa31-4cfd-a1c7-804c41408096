<script setup lang="ts">
import PatientInfoItem from '../../components/patient-info-item.vue'
import { useMountDialog } from '@/hooks/useMountDialog.ts'
import type { PatientBillList } from '@/api/dto/discharge-settlement/bill-clearing.dto'
import type {
  ResCheckTheDepositAmountDTO,
  ResClearingAmountOfInvoiceNumberDTO
} from '@/api/dto/discharge-settlement/bill-clearing.dto'

import SelectPeriodBillingDialog from './select-period-billing-dialog.vue'
import StructureCostListDialog from './structure-cost-list-dialog.vue'
import PreReceiptBillDialog from './pre-receipt-bill-dialog.vue'
import ClearAmountDialog from './clear-amount-dialog.vue'
import FinalClearAmountDialog from './final-clear-amount-dialog.vue'
import SelectDetailedBillingDialog from './select-detailed-billing-dialog.vue'
import {
  requestPatientExpenseInfoOfAll,
  requestDepositPaymentInfo,
  requestSettleAnAccountByPhaseOfCostStructure,
  requestSettleAnAccountByAllOfCostStructure,
  requestSettleAnAccountByDetailsOfCostStructure,
  requestCheckTheDepositAmount,
  requestClearingAmountOfInvoiceNumber
} from '@/api/discharge-settlement.api'
import type {
  ResDepositPaymentInfoDTO,
  ResExpenseInfoOfAllOrClassificationDTO
} from '@/api/dto/discharge-settlement/patient-accounts.dto'
import type { ResSettleAnAccountOfCostStructureDTO } from '@/api/dto/discharge-settlement/bill-clearing.dto'

/**
 * 费用信息面板
 *
 * 流程：
 * 1、获取全部期数信息展示
 * 2、结算操作
 * --- 选择明细清账：先获取全部账务明细，然后选择明细，再获取明细的费用结构
 * --- 按期数清账：先获取按期清账数据，然后选择期数，再获取按期清账的费用结构
 * --- 全部清账：先获取全部账务明细，然后获取全部的费用结构
 * 3、上面三种方式最后都进入费用结构选择弹窗，选择费用结构后，进入预收单选择弹窗，最后确定清账金额
 */

const { open: openSelectPeriodBillingDialog } = useMountDialog(SelectPeriodBillingDialog)
const { open: openStructureCostListDialog } = useMountDialog(StructureCostListDialog)
const { open: openPreReceiptBillDialog } = useMountDialog(PreReceiptBillDialog)
const { open: openClearAmountDialog } = useMountDialog(ClearAmountDialog)
const { open: openFinalClearAmountDialog } = useMountDialog(FinalClearAmountDialog)
const { open: openSelectDetailedBillingDialog } = useMountDialog(SelectDetailedBillingDialog)

const props = defineProps<{
  // 记账号
  inpno: string
  // 住院号
  patno: string
  // 病人账务信息
  patientBillInfo: PatientBillList
}>()

// 选择明细清账
async function handleSelectDetailedBilling() {
  const { resExpenseInfoList, totalAmount } = await requestPatientExpenseInfoOfAll({
    inpno: props.inpno
  })

  if (resExpenseInfoList && resExpenseInfoList.length > 0) {
    openSelectDetailedBillingDialog({
      inpno: props.inpno,
      accountDetailList: resExpenseInfoList,
      totalAmount,
      confirmCallback: async (data: {
        selectedData: ResExpenseInfoOfAllOrClassificationDTO[]
        isSelectAllSelfPay: boolean
      }) => {
        console.log('选择明细清账', data)
        const detailsnList = data.selectedData.map((item) => item.detailsn!!)
        if (detailsnList && detailsnList.length > 0) {
          const costStructure = await requestSettleAnAccountByDetailsOfCostStructure({
            inpno: props.inpno,
            detailsnList
          })
          handleOpenStructureCostListDialog(costStructure)
        } else {
          ElMessage.warning('没有选择账务明细')
        }
      }
    })
  } else {
    ElMessage.warning('没有账务明细')
  }
}

// 按期数清账
function handleSelectPeriodBilling() {
  console.log('按期数清账')
  const maxPeriod = props.patientBillInfo.accountsInfoList?.length ?? 0
  if (maxPeriod === 0) {
    ElMessage.warning('当前费用信息为空，无法按期数清账')
    return
  }
  openSelectPeriodBillingDialog({
    maxPeriod,
    confirmCallback: async (period: number) => {
      console.log('按期数清账输入：', period)
      // 从费用信息列表里面取前 period 条数据的 billdate
      const billDateList = props.patientBillInfo.accountsInfoList?.slice(0, period).map((item) => item.billdate)
      console.log('按期数清账，传入的期数', period, '，按期数清账数据', billDateList)
      if (billDateList && billDateList.length > 0) {
        const costStructure = await requestSettleAnAccountByPhaseOfCostStructure({
          inpno: props.inpno,
          billdateList: billDateList
        })
        handleOpenStructureCostListDialog(costStructure)
      } else {
        ElMessage.warning('没有按期清账数据')
      }
    }
  })
}

// 全部清账
async function handleSelectAllBilling() {
  console.log('全部清账')
  const costStructure = await requestSettleAnAccountByAllOfCostStructure({
    inpno: props.inpno
  })
  handleOpenStructureCostListDialog(costStructure)
}

/**
 * 打开结构费用列表弹窗
 */
function handleOpenStructureCostListDialog(costStructure: ResSettleAnAccountOfCostStructureDTO) {
  openStructureCostListDialog({
    costStructure,
    confirmCallback: async () => {
      console.log('结构费用选择')

      // 获取预收单数据
      const res = await requestDepositPaymentInfo({
        inpno: props.inpno
      })
      if (res && res.length > 0) {
        handleOpenPreReceiptBillDialog(res, costStructure)
      } else {
        ElMessage.warning('没有预收单数据')
      }
    }
  })
}

/**
 * 打开预收单选择弹窗
 */
async function handleOpenPreReceiptBillDialog(
  depositPaymentInfoList: ResDepositPaymentInfoDTO[],
  costStructure: ResSettleAnAccountOfCostStructureDTO
) {
  console.log('打开预收单选择弹窗')
  openPreReceiptBillDialog({
    depositPaymentInfoList,
    confirmCallback: async (data: ResDepositPaymentInfoDTO[]) => {
      console.log('预收单选择：', data)
      // 预收单号列表
      const prepayidList = data.map((item) => item.prepayid)
      const preReceiptData = await requestCheckTheDepositAmount({
        inpno: props.inpno,
        patno: props.patno,
        prepayidList
      })

      // 费用明细 id 列表
      const details = costStructure.expenseInfoList
        .map((item) => item.detailsn)
        .filter((detailsn) => detailsn !== undefined && detailsn !== null)

      const invoiceNumberData = await requestClearingAmountOfInvoiceNumber({
        inpno: props.inpno,
        details: details.length > 0 ? details : undefined
      })
      handleOpenClearAmountDialog(preReceiptData, invoiceNumberData)
    }
  })
}

/**
 * 打开清账金额弹窗
 * @param data 预收单
 */
function handleOpenClearAmountDialog(
  preReceiptData: ResCheckTheDepositAmountDTO,
  invoiceNumberData: ResClearingAmountOfInvoiceNumberDTO
) {
  console.log('打开清账金额弹窗，传入预收单', preReceiptData, '，发票号数据', invoiceNumberData)
  openClearAmountDialog({
    preReceiptData,
    invoiceNumberData,
    confirmCallback: async (data: any) => {
      console.log('清账金额选择：', data)
      handleOpenFinalClearAmountDialog(data)
    }
  })
}

/**
 * 打开最终清账金额弹窗
 * @param data 清账金额
 */
function handleOpenFinalClearAmountDialog(data: any) {
  console.log('打开最终清账金额弹窗，传入清账金额', data)
  openFinalClearAmountDialog({
    confirmCallback: async (data: any) => {
      console.log('最终清账金额选择：', data)
    }
  })
}
</script>

<template>
  <div class="patient-fee-panel">
    <CommonHighlightTable
      class="patient-fee-table"
      :data="patientBillInfo.accountsInfoList ?? []"
      :max-height="420"
      show-finger-icon
    >
      <el-table-column prop="datenum" label="期数" width="120" align="center" />
      <el-table-column prop="billdate" label="结算日期" width="250" align="center" />
      <el-table-column prop="ward" label="病区名称" min-width="250" />
      <el-table-column prop="totalamount" label="本期费用" width="250" align="right" />
      <el-table-column prop="totalamountadd" label="累计费用" width="250" align="right" />
    </CommonHighlightTable>

    <div class="fee-operation-container">
      <div class="common-panel-title">结算操作</div>
      <PatientInfoItem label="开始日期" :value="patientBillInfo.startDate || ''" />
      <PatientInfoItem label="结束日期" :value="patientBillInfo.endDate || ''" />
      <PatientInfoItem label="待清金额" :value="patientBillInfo?.pendingAmount || ''" suffix="元" />
      <div class="fee-operation-button" @click="handleSelectDetailedBilling">选择明细清账</div>
      <div class="fee-operation-button" @click="handleSelectPeriodBilling">按期数清账</div>
      <div class="fee-operation-button" @click="handleSelectAllBilling">全部清账</div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.patient-fee-panel {
  display: flex;
  gap: 20px;
}

.patient-fee-table {
  flex: 1;
  width: 1px;
}

.fee-operation-container {
  display: flex;
  gap: 20px;
  flex-direction: column;
  min-width: 320px;

  .fee-operation-button {
    height: 40px;
    width: 320px;
    line-height: 40px;
    text-align: center;
    border-radius: 4px;
    background: #fff;
    border: 1px solid var(--el-border-color-primary);
    color: var(--el-color-primary);
    cursor: pointer;
  }
}
</style>
