import InpatientFormNotificationTemplate from '@/assets/print-template/inpatient-form-template-notification.html?raw'
import InpatientFormRegistrationTemplate from '@/assets/print-template/inpatient-form-template-registration.html?raw'
import { getWristbandPrintingTemplate } from '@/assets/print-template/wristband-printing-template.ts'
import { PrintMode, usePrinterStore } from '@/stores/printer.store.ts'
import * as hiPrintUtils from '@/utils/hi-print-plugin-utils.ts'
import { storeToRefs } from 'pinia'
import IdCardTemplate from '@/assets/print-template/id-card-template.html?raw'

/**
 * 封装住院登记涉及的 手腕带打印、入院卡打印，使上级使用更加便捷，不用顾及打印模板相关
 */

/**
 * 手腕带 打印模板数据
 */
export interface WristbandData {
  // 患者名
  patientName: string
  // 年龄
  age: string | number
  // 性别
  gender: string
  // 科别
  department: string
  // 住院号
  hospitalizationNumber: string
  // 记帐号
  billingAccount: string
  // 记帐号二维码值
  billingAccountBar: string
}

/**
 * 身份证打印模板数据
 */
export interface IdCardData {
  // 身份证头像页
  idCardImgurlFront: string
  // 身份证国徽页
  idCardImgurlBack: string
}

// 打印手腕带
export function useWristbandPrinter() {
  const printerStore = usePrinterStore()
  const { silentPrinterConfig, printMode } = storeToRefs(printerStore)
  const wristbandPrintError = ref<string | null>(null)

  const printWristband = async (wristbandData: WristbandData) => {
    if (printMode.value === PrintMode.SILENT && !silentPrinterConfig.value.wristbandPrinter) {
      wristbandPrintError.value = '请先配置手腕带打印机'
      return
    }

    try {
      await hiPrintUtils.handleHiPrintTemplate(
        getWristbandPrintingTemplate(),
        wristbandData,
        silentPrinterConfig.value.wristbandPrinter,
        printMode.value || PrintMode.PREVIEW
      )
      wristbandPrintError.value = null
    } catch (e: Error | any) {
      wristbandPrintError.value = `手腕带打印失败：${e.message || e}`
    }
  }

  // 使用方通过 wristbandPrintError 获取错误信息，不为 null 时 表示打印失败
  return {
    printWristband,
    wristbandPrintError
  }
}

// 打印身份证
export function useIdCardPrinter() {
  const printerStore = usePrinterStore()
  const { silentPrinterConfig, printMode } = storeToRefs(printerStore)
  const idCardPrintError = ref<string | null>(null)

  const printIdCard = async (idCardData: IdCardData) => {
    if (printMode.value === PrintMode.SILENT && !silentPrinterConfig.value.inpatientFormPrinter) {
      idCardPrintError.value = '请先配置打印机'
      return
    }

    try {
      await hiPrintUtils.handleHiPrintHtml(
        [
          {
            html: IdCardTemplate,
            data: idCardData
          }
        ],
        silentPrinterConfig.value.inpatientFormPrinter,
        printMode.value || PrintMode.PREVIEW
      )
      idCardPrintError.value = null
    } catch (e: Error | any) {
      idCardPrintError.value = `身份证打印失败：${e.message || e}`
    }
  }

  return {
    printIdCard,
    idCardPrintError
  }
}

// 打印入院卡
export function useInpatientFormPrinter() {
  const printerStore = usePrinterStore()
  const { silentPrinterConfig, printMode } = storeToRefs(printerStore)
  const inpatientFormPrintError = ref<string | null>(null)

  // 婚姻状态映射
  const maritalMap = {
    // 未婚
    '10': 'marital_10',
    // 已婚
    '20': 'marital_20',
    // 丧偶
    '30': 'marital_30',
    // 离婚
    '40': 'marital_40',
    // 其他
    '21': 'marital_other',
    '22': 'marital_other',
    '23': 'marital_other',
    '90': 'marital_other'
  }
  // 性别映射
  const genderMap = { '1': 'gender_1', '2': 'gender_2' }
  // 职业映射
  const careerMap = {
    '11': 'career_11',
    '13': 'career_13',
    '17': 'career_17',
    '21': 'career_21',
    '24': 'career_24',
    '27': 'career_27',
    '31': 'career_31',
    '37': 'career_37',
    '51': 'career_51',
    '54': 'career_54',
    '70': 'career_70',
    '80': 'career_80',
    '90': 'career_90'
  }
  // 联系人关系映射
  const contactRelationshipMap = {
    '0': 'contactRelationship_0',
    '1': 'contactRelationship_1',
    '2': 'contactRelationship_2',
    '3': 'contactRelationship_3',
    '4': 'contactRelationship_4',
    '5': 'contactRelationship_5',
    '6': 'contactRelationship_6',
    '7': 'contactRelationship_7',
    '8': 'contactRelationship_8',
    '9': 'contactRelationship_9'
  }
  // 入院途径
  const admwayMap = { '1': 'admway_1', '2': 'admway_2', '3': 'admway_3', '9': 'admway_9' }

  // 保险类型映射配置
  const insuranceMap = {
    职工基本医疗保险: ['佛山医保', '省内异地医保', '跨省异地医保'],
    城乡居民基本医疗保险: ['佛山医保', '省内异地医保', '跨省异地医保'],
    工伤: ['佛山工伤', '省内异地工伤', '跨省异地工伤', '工伤康复'],
    佛山离休: ['市直', '禅城', '南海', '高明', '三水'],
    自费: ['全自费', '由于交通事故、工伤、其他意外不报基本医疗保险'],
    其他: ['省公医', '贫困救助', '商业医保', '全公费', '其他社保']
  }

  // 预处理保险类型数据
  const processInsuranceType = (data) => {
    // 初始化所有保险变量为空
    Object.entries(insuranceMap).forEach(([type, items]) => {
      items.forEach((item) => {
        const key = `insurance_${type}_${item}`
        data[key] = ''
      })
    })

    // 设置当前选中项
    if (data.insuranceType) {
      const [typePart, detailPart] = data.insuranceType.split('-')
      const validType = insuranceMap[typePart]
      if (validType && validType.includes(detailPart)) {
        const currentKey = `insurance_${typePart}_${detailPart}`
        data[currentKey] = '√' // 使用√或特殊符号□✓
      }
    }
  }

  /**
   * 打印住院登记入院卡
   * inpatientFormData 为 住院登记入院卡模板所需要的数据，字段直接和打印模板的字段一一对应
   * @param inpatientFormData
   */
  const printInpatientForm = async (inpatientFormData: any) => {
    if (printMode.value === PrintMode.SILENT && !silentPrinterConfig.value.inpatientFormPrinter) {
      inpatientFormPrintError.value = '请先配置入院卡打印机'
      return
    }

    // 将生日拆分到年、月、日字段中
    if (inpatientFormData.birthday) {
      const birthday = inpatientFormData.birthday.split('-')
      inpatientFormData.yearOfBirth = birthday[0] ?? ''
      inpatientFormData.monthOfBirth = birthday[1] ?? ''
      inpatientFormData.dayOfBirth = birthday[2] ?? ''
    }

    // 婚姻状态处理
    if (inpatientFormData.maritalStatus) {
      // 预处理：根据当前状态生成勾选变量
      Object.values(maritalMap).forEach((key) => (inpatientFormData[key] = '')) // 初始化为空
      const currentMaritalKey = maritalMap[inpatientFormData.maritalStatus]
      if (currentMaritalKey) {
        inpatientFormData[currentMaritalKey] = '√'
      }
    }

    // 入院途径状态处理
    if (inpatientFormData.admway) {
      Object.values(admwayMap).forEach((key) => (inpatientFormData[key] = '')) // 初始化为空
      const currentAdmwayKey = admwayMap[inpatientFormData.admway]
      if (currentAdmwayKey) {
        inpatientFormData[currentAdmwayKey] = '√'
      }
    }

    // 联系人关系状态处理
    if (inpatientFormData.contactRelationship) {
      Object.values(contactRelationshipMap).forEach((key) => (inpatientFormData[key] = ''))
      const currentContactRelationshipKey = contactRelationshipMap[inpatientFormData.contactRelationship]
      if (currentContactRelationshipKey) inpatientFormData[currentContactRelationshipKey] = '√'
    }

    // 职业状态处理
    if (inpatientFormData.career) {
      Object.values(careerMap).forEach((key) => (inpatientFormData[key] = ''))
      const currentCareerKey = careerMap[inpatientFormData.career]
      if (currentCareerKey) inpatientFormData[currentCareerKey] = '√'
    }

    // 性别状态处理
    if (inpatientFormData.genderType) {
      Object.values(genderMap).forEach((key) => (inpatientFormData[key] = ''))
      const currentGenderKey = genderMap[inpatientFormData.genderType]
      if (currentGenderKey) inpatientFormData[currentGenderKey] = '√'
    }


    // 拆分医保类型和细项
    processInsuranceType(inpatientFormData)

    try {
      await hiPrintUtils.handleHiPrintHtml(
        [
          {
            html: InpatientFormRegistrationTemplate,
            data: inpatientFormData
          },
          {
            // 住院须知不用填充内容
            html: InpatientFormNotificationTemplate
          }
        ],
        silentPrinterConfig.value.inpatientFormPrinter,
        printMode.value || PrintMode.PREVIEW
      )
      inpatientFormPrintError.value = null
    } catch (e: Error | any) {
      inpatientFormPrintError.value = `入院卡打印失败：${e.message || e}`
    }
  }

  // 使用方通过 inpatientFormPrintError 获取错误信息，不为 null 时 表示打印失败
  return {
    printInpatientForm,
    inpatientFormPrintError
  }
}
