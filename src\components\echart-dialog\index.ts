import EchartDialog, { type EchartDialogData } from './echart-dialog.vue'

export type EchartDialogType = InstanceType<typeof EchartDialog>
export type EchartDialogProps = EchartDialogType['$props']

let overlayComponent: EchartDialogType | null = null

function createdOverlay(options?: EchartDialogProps) {
  const overlayElement = document.createElement('div')
  document.body.appendChild(overlayElement)

  const OverlayInstance = createApp(EchartDialog, options)
  overlayComponent = OverlayInstance.mount(overlayElement) as EchartDialogType
}

/**打开用户创建弹窗 */
export const useEchartDialogHook = () => {
  const open = async (openOpts: EchartDialogData, dialogOpts?: EchartDialogProps) => {
    if (!overlayComponent) {
      createdOverlay(dialogOpts)
    }
    return await overlayComponent!.__open.call(overlayComponent, openOpts)
  }

  const close = () => {
    overlayComponent?.__close.call(overlayComponent)
  }

  return { open, close }
}

export default EchartDialog
