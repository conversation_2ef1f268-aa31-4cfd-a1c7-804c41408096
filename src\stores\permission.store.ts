import type { NavigationMenuItemExtraData } from '@/api/dto/navigation.dto'
import { defineStore } from 'pinia'

export type PermissionItem = Record<string, NavigationMenuItemExtraData>

export interface usePermissionStoreState {
  permissionMap: Record<string, PermissionItem>
}

export const usePermissionStore = defineStore('permissionStore', {
  state(): usePermissionStoreState {
    return {
      permissionMap: {},
    }
  },

  actions: {
    /**获取权限 */
    // getPermission(id: string) {},

    /**设置权限 */
    setPermission(id: string, permissions: PermissionItem) {
      this.permissionMap[id] = permissions
    },

    /**删除权限 */
    clearPermission(id?: string) {
      if (id) {
        delete this.permissionMap[id]
      } else {
        this.permissionMap = {}
      }
    },
  },
})
