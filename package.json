{"name": "admin-java-vue3", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "tsc": "vue-tsc -b", "build:analyze": "cross-env VITE_ANALYZE=true vite build"}, "dependencies": {"@amap/amap-jsapi-loader": "^1.0.1", "@animxyz/core": "^0.6.6", "@animxyz/vue3": "^0.6.7", "@element-plus/icons-vue": "^2.3.1", "@types/json-bigint": "^1.0.4", "@vueuse/core": "^12.0.0", "@wangeditor/editor": "^5.1.23", "@wangeditor/editor-for-vue": "^5.1.12", "axios": "^1.7.9", "css-render": "^0.15.14", "echarts": "^5.5.1", "element-plus": "^2.9.0", "js-sha256": "^0.11.0", "json-bigint": "^1.0.0", "jwt-decode": "^4.0.0", "lodash-es": "^4.17.21", "nprogress": "^0.2.0", "pinia": "^2.3.0", "pinia-plugin-persistedstate": "^4.1.3", "screenfull": "^6.0.2", "vue": "^3.5.13", "vue-plugin-hiprint": "^0.0.58-fix", "vue-router": "^4.5.0"}, "devDependencies": {"@types/node": "^22.10.1", "@types/nprogress": "^0.2.3", "@vitejs/plugin-vue": "^5.2.1", "@vitejs/plugin-vue-jsx": "^4.1.1", "@vue/tsconfig": "^0.7.0", "async-validator": "^4.2.5", "cross-env": "^7.0.3", "naive-ui": "^2.40.3", "npm-run-all": "^4.1.5", "prettier": "^3.4.2", "rollup-plugin-visualizer": "^5.14.0", "sass": "^1.82.0", "typescript": "~5.6.3", "unplugin-auto-import": "^0.18.6", "unplugin-svg-component": "^0.12.1", "unplugin-vue-components": "^0.27.5", "unplugin-vue-router": "^0.10.9", "vite": "^6.0.1", "vite-plugin-vue-devtools": "^7.6.7", "vue-tsc": "^2.1.10"}}