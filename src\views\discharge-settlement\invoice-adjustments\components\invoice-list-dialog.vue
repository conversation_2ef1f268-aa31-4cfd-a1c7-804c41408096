<script setup lang="ts">
import type { InvoiceListItem } from '@/api/dto/discharge-settlement/invoice-adjustments.dto'
import CommonHighlightTable from '@/components/common-highlight-table/common-highlight-table.vue'
import { formatPrice } from '@/utils'
/**
 * 选择发票 弹窗
 */

const props = defineProps<{
  /* 点击确认回调 */
  confirmCallback: (invoiceNo: string) => Promise<void>
}>()

const dialogVisible = defineModel({ default: false })

watch(dialogVisible, (val) => {
  if (val) {
    reset()
  }
})

// 选择
const selectedInvoiceNo = ref<string>()

// 发票列表
const invoiceList = ref<InvoiceListItem[]>([
  {
    invoicePrintNo: '1',
    invoiceNo: '1',
    invoicePeriod: '1',
    invoiceDate: '1',
    invoiceAmount: 100
  },
  {
    invoicePrintNo: '2',
    invoiceNo: '2',
    invoicePeriod: '2',
    invoiceDate: '2',
    invoiceAmount: 200
  }
])

const tableRef = ref<InstanceType<typeof CommonHighlightTable>>()

// 弹窗 - 点击确认
async function handleConfirm(done: (flag?: boolean) => void) {
  if (!selectedInvoiceNo.value) return
  try {
    // 提交
    await props.confirmCallback(JSON.parse(JSON.stringify(selectedInvoiceNo.value)))
    done(false)
  } catch (e) {
    done(true)
  }
}

// 重置
function reset() {
  console.log('重置选择发票弹窗')
  selectedInvoiceNo.value = undefined
  tableRef.value?.clearTableSelection()
}

// 当前行改变
function handleCurrentChange(row: InvoiceListItem) {
  selectedInvoiceNo.value = row?.invoiceNo
}

// 双击发票行
async function handleRowDblclick(row: InvoiceListItem) {
  selectedInvoiceNo.value = row?.invoiceNo
  await nextTick()
  await props.confirmCallback(JSON.parse(JSON.stringify(selectedInvoiceNo.value)))
  dialogVisible.value = false
}

// 金额格式化
function customFormatPrice(row: InvoiceListItem) {
  return formatPrice(row.invoiceAmount)
}

defineExpose({
  reset
})
</script>

<template>
  <CommonFormDialog
    v-model="dialogVisible"
    title="选择发票"
    width="872px"
    show-loading
    :disabled-confirm="!selectedInvoiceNo"
    @confirm="handleConfirm"
  >
    <CommonHighlightTable
      ref="tableRef"
      :data="invoiceList"
      :height="518"
      width="840px"
      @row-dblclick="handleRowDblclick"
      @current-change="handleCurrentChange"
    >
      <el-table-column prop="invoicePrintNo" label="发票印刷号" width="200" />
      <el-table-column prop="invoiceNo" label="发票号" width="200" />
      <el-table-column prop="invoicePeriod" label="期数" width="80" align="center" />
      <el-table-column prop="invoiceDate" label="日期" min-width="200" />
      <el-table-column prop="invoiceAmount" label="金额" width="120" align="right" :formatter="customFormatPrice" />
    </CommonHighlightTable>
  </CommonFormDialog>
</template>

<style scoped lang="scss"></style>
