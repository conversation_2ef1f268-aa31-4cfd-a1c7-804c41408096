export interface GetSysOperationLogForPageInput {
  account: string
  businessmodule: string
  operResult: string
  operUrl: string
  operationType: string
  page: number
  rows: number
  status: string
  operTimeBegin: string
  operTimeEnd: string
}

export interface OperationLogModel {
  accessMode: string
  account: string
  browser: string
  businessmodule: string
  createTime: string
  createUserName: string
  hostIp: string
  operParams: string
  operResult: string
  operTime: string
  operUrl: string
  operationLogId: string
  operationSystem: string
  operationType: string
  responseTime: string
  resultCode: string
  status: string
}

export type GetSysOperationLogForPageOutput = OperationLogModel[]
