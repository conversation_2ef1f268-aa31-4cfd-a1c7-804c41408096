<script setup lang="ts">
import { FormInstance, FormRules } from 'element-plus'

interface RuleForm {
  selfLiftingCode: string
}

const props = defineProps<{
  name: string
  hospitalizationNumber: string
  confirmCallback: (selfLiftingCode: string) => Promise<void>
}>()

const inputFormRef = ref<FormInstance>()

// 确定服务
const inputForm = reactive({
  selfLiftingCode: ''
})

const dialogVisible = defineModel({ default: false })

const inputFormRules = reactive<FormRules<RuleForm>>({
  selfLiftingCode: [{ required: true, message: '请输入自提码', trigger: 'blur' }]
})

// 确定服务弹窗 - 点击确认
async function handleConfirm(done: (flag?: boolean) => void, formEl: FormInstance | undefined) {
  if (!formEl) return
  await formEl.validate(async (valid, fields) => {
    if (valid) {
      try {
        // 提交
        await props.confirmCallback(inputForm.selfLiftingCode)
        done(false)
        setTimeout(() => {
          formEl.resetFields()
        })
      } catch (e) {
        done(true)
      }
    } else {
      console.log('提交 确定服务 表单 失败 表单校验不通过：', fields)
    }
  })
}
</script>

<template>
  <CommonFormDialog
    v-model="dialogVisible"
    title="确定服务"
    width="600px"
    show-loading
    :disabled-confirm="!inputForm.selfLiftingCode"
    @confirm="handleConfirm($event, inputFormRef)"
  >
    <div class="patient-info">住院人姓名：{{ name }} 丨 住院号：{{ hospitalizationNumber }}</div>
    <el-form ref="inputFormRef" class="mt-20" size="large" :model="inputForm" :rules="inputFormRules">
      <el-form-item label="自提码：" prop="selfLiftingCode">
        <el-input
          v-model="inputForm.selfLiftingCode"
          type="textarea"
          clearable
          placeholder="请输入"
          :autosize="{ minRows: 1, maxRows: 10 }"
        />
      </el-form-item>
    </el-form>
  </CommonFormDialog>
</template>

<style scoped lang="scss">
.patient-info {
  height: 40px;
  border-radius: 4px;
  background: #ecf5ff;
  border: 1px solid #d9ecff;
  color: #1890ef;
  line-height: 40px;
  padding: 0 16px;
}

.mt-20 {
  margin-top: 20px;
}
</style>
