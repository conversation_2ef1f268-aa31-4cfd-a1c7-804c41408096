import 'vue-router'
import { ROUTER_PATH } from './router-path'
import type { NavigationMenuItemExtraData } from '@/api/dto/navigation.dto'
import type { SvgName } from '~virtual/svg-component'

declare module 'vue-router' {
  export interface AppRouter extends Router {
    options: RouterOptions
  }

  export interface RouteLocationNormalizedGeneric {
    path: ROUTER_PATH
  }

  export interface _RouteRecordBase {
    /**是否在导航栏中显示 */
    hidden?: boolean
  }

  export interface RouteMeta {
    /**路由标题 */
    title?: string
    /**权限id */
    mainID?: string
    /**路由图标 */
    icon?: any
    /**操作权限 */
    extraData?: NavigationMenuItemExtraData[]
    /**是否无法访问 */
    noVisit?: boolean
    /**是否持久化 */
    noCache?: boolean
    /**是否不在菜单显示 */
    hidden?: boolean
    /**排序 */
    sortCode?: number
    /**是否在tabs中显示 */
    useTab?: boolean
    /**是否禁止关闭tab */
    affix?: boolean
    /**是否使用面包屑 */
    useBreadcrumb?: boolean
    /**页面实际激活的路径 */
    activeMenu?: string
    /**离开的时候是否关闭tab */
    leaveOff?: boolean
    /**导航id */
    navigationId?: string
    /**所属权限 */
    permissionFrom?: string
    /**父级id */
    parentId?: number | string
    /**是否为父级 */
    isParent?: boolean
  }
}
