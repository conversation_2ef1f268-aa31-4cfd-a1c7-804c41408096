import { ROUTER_PATH } from '../router-path'
import type { RouteRecordRaw } from 'vue-router'

const Layout = () => import('@/layout')

export const publicEdArticleManagement: RouteRecordRaw = {
  path: ROUTER_PATH.PUBLIC_ED_ARTICLE_MANAGEMENT,
  name: 'PublicEdArticleManagement',
  component: Layout,
  redirect: ROUTER_PATH.PUBLIC_ED_ARTICLE_LIST,
  meta: { title: '宣教资料管理', icon: 'el-icon-home-filled' },
  children: [
    {
      path: ROUTER_PATH.PUBLIC_ED_ARTICLE_LIST,
      name: 'PublicEdArticleList',
      component: () => import('@/views/public-ed-article-management/public-ed-article-list/public-ed-article-list.vue'),
      meta: {
        title: '住院宣教信息',
        leaveOff: false
      }
    },

    {
      path: ROUTER_PATH.PUBLIC_ED_ARTICLE_DETAIL,
      name: 'PublicEdArticleDetail',
      hidden: true,
      component: () =>
        import('@/views/public-ed-article-management/public-ed-article-detail/public-ed-article-detail.vue'),
      meta: {
        title: '住院宣教信息',
        leaveOff: true,
        permissionFrom: 'PublicEdArticleManagement',
        activeMenu: ROUTER_PATH.PUBLIC_ED_ARTICLE_LIST,
        noCache: true
      }
    }
  ]
}
