import router from '@/router'
import { DEVICE, useLayoutStore } from '@/stores/layout.store'

const { body } = document

const WIDTH = 992 // refer to Bootstrap's responsive design

/**监听视窗变化 */
export const resizeHandlerHook = () => {
  const layoutStore = useLayoutStore()

  const $_isMobile = () => {
    const rect = body.getBoundingClientRect()
    return rect.width - 1 < WIDTH
  }

  const $_resizeHandler = () => {
    if (!document.hidden) {
      const isMobile = $_isMobile()
      layoutStore.device = isMobile ? DEVICE.MOBILE : DEVICE.DESKTOP
      layoutStore.menuIsCollapse = isMobile
    }
  }

  onBeforeMount(() => {
    window.addEventListener('resize', $_resizeHandler)
  })

  onBeforeUnmount(() => {
    window.removeEventListener('resize', $_resizeHandler)
  })

  onMounted(() => {
    const isMobile = $_isMobile()
    if (isMobile) {
      layoutStore.device = DEVICE.MOBILE
      layoutStore.menuIsCollapse = true
    }
  })

  watch(router.currentRoute, () => {
    $_isMobile() && (layoutStore.menuIsCollapse = true)
  })
}
