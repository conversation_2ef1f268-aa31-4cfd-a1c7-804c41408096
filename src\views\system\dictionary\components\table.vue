<script lang="ts" setup>
import type { OperaRow } from '@/api/dto/index.dto'
import type { GetSysDictionaryInfoForPageInput, SysDictionaryInfoModel } from '@/api/dto/system/dictionary.dto'
import BasePagination from '@/components/base-pagination'
import { useFormHook } from '@/hooks/useForm'
import { usePaginationHook } from '@/hooks/usePagination'

export type SearchData = Omit<GetSysDictionaryInfoForPageInput, 'page' | 'rows' | 'parentId'>

const props = defineProps<{
  tableData: any[]
  parentId: number
  total: number
}>()

const { formRef, resetForm } = useFormHook()
const { currentPage, pageSize } = usePaginationHook()
const formData = reactive<SearchData>({
  dictionaryInfoName: '',
  enCode: '',
  enabledMark: '',
})

const emit = defineEmits<{
  search: [payload: GetSysDictionaryInfoForPageInput]
  newDictionary: []
  editInfo: [payload: OperaRow<SysDictionaryInfoModel>]
  editVal: [payload: OperaRow<SysDictionaryInfoModel>]
  deleteRow: [payload: OperaRow<SysDictionaryInfoModel>]
}>()

const searchFunc = () => {
  emit('search', {
    ...formData,
    parentId: props.parentId,
    page: currentPage.value,
    rows: pageSize.value,
  })
}

const handleSearch = () => {
  currentPage.value = 1
  searchFunc()
}

watch([currentPage, pageSize], () => searchFunc())

watch(
  () => props.parentId,
  () => searchFunc(),
  { immediate: true }
)

const handleReset = () => {
  resetForm()
  handleSearch()
}

const handleNewDictionary = () => {
  emit('newDictionary')
}

const handleEditInfo = (target: OperaRow<SysDictionaryInfoModel>) => {
  emit('editInfo', target)
}
const handleEditVal = (target: OperaRow<SysDictionaryInfoModel>) => {
  emit('editVal', target)
}

const handleDeleteRow = (target: OperaRow<SysDictionaryInfoModel>) => {
  emit('deleteRow', target)
}

defineExpose({ getData: searchFunc })
</script>

<template>
  <div class="table-container ml-10">
    <el-form ref="formRef" :model="formData" inline>
      <el-form-item prop="dictionaryInfoName" label="名称：">
        <el-input v-model="formData.dictionaryInfoName"></el-input>
      </el-form-item>

      <el-form-item prop="enCode" label="编号标识：">
        <el-input v-model="formData.enCode"></el-input>
      </el-form-item>

      <el-form-item prop="enabledMark" label="是否启用：">
        <el-select v-model="formData.enabledMark">
          <el-option label="全部" value=""></el-option>
          <el-option label="已启用" :value="true"></el-option>
          <el-option label="未启用" :value="false"></el-option>
        </el-select>
      </el-form-item>

      <el-form-item>
        <el-button type="primary" @click="handleSearch">搜索</el-button>
        <el-button @click="handleReset">重置</el-button>
        <el-button type="success" @click="handleNewDictionary">新增字典类型</el-button>
      </el-form-item>
    </el-form>

    <BaseTable :data="tableData" border height="660">
      <el-table-column prop="dictionaryInfoName" label="名称" align="center"></el-table-column>
      <el-table-column prop="enCode" label="编号标识" align="center"></el-table-column>
      <el-table-column prop="sortCode" label="排序" align="center" width="60"></el-table-column>
      <el-table-column label="是否启用" align="center" width="90">
        <template #default="{ row }: { row: SysDictionaryInfoModel }">
          <el-tag :type="row.enabledMark ? 'success' : 'danger'">{{ row.enabledMark ? '启用' : '停用' }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="description" label="备注"></el-table-column>
      <el-table-column label="操作" min-width="140">
        <template #default="{ row }: { row: OperaRow<SysDictionaryInfoModel> }">
          <el-button v-auth="'getSysDictionaryInfoForUpdate'" @click="handleEditInfo(row)"> 编辑 </el-button>
          <el-button v-auth="'getSysDictionaryDetailForPage'" type="success" @click="handleEditVal(row)">
            值内容
          </el-button>
          <el-button
            v-auth="'deleteSysDictionaryInfo'"
            :loading="row.loading"
            type="danger"
            @click="handleDeleteRow(row)"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </BaseTable>

    <BasePagination v-model:current-page="currentPage" v-model:page-size="pageSize" :total="total" />
  </div>
</template>

<style lang="scss" scoped>
.table-container {
  flex-grow: 1;
  padding: 20px;
  background-color: var(--el-bg-color-overlay);
}
</style>
