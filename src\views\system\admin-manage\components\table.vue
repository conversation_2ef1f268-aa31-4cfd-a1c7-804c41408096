<script lang="ts" setup>
import type { OperaRow } from '@/api/dto/index.dto'
import type { GetSysManagerByPageInput, ManagerByPageItemModel } from '@/api/dto/system/admin-manage.dto'
import BasePagination from '@/components/base-pagination'
import { useFormHook } from '@/hooks/useForm'
import { usePaginationHook } from '@/hooks/usePagination'

export type SearchData = Omit<GetSysManagerByPageInput, 'page' | 'rows' | 'organizationId'>

const props = defineProps<{
  tableData: any[]
  organizationId: string
  total: number
}>()

const { formRef, resetForm } = useFormHook()
const { currentPage, pageSize } = usePaginationHook()
const formData = reactive<SearchData>({
  account: '',
  realName: '',
  enabledMark: '',
})

const emit = defineEmits<{
  search: [payload: GetSysManagerByPageInput]
  newAccount: []
  editInfo: [payload: OperaRow<ManagerByPageItemModel>]
  viewBind: [payload: OperaRow<ManagerByPageItemModel>]
  resetPassword: [payload: OperaRow<ManagerByPageItemModel>]
  deleteAccount: [payload: OperaRow<ManagerByPageItemModel>]
}>()

const searchFunc = () => {
  emit('search', {
    ...formData,
    organizationId: props.organizationId,
    page: currentPage.value,
    rows: pageSize.value,
  })
}

const handleSearch = () => {
  currentPage.value = 1
  searchFunc()
}

watch([currentPage, pageSize], () => searchFunc())

watch(
  () => props.organizationId,
  () => searchFunc()
)

const handleReset = () => {
  resetForm()
  handleSearch()
}

const handleNewAccount = () => {
  emit('newAccount')
}

const handleEditInfo = (target: OperaRow<ManagerByPageItemModel>) => {
  emit('editInfo', target)
}

const handleViewBind = (target: OperaRow<ManagerByPageItemModel>) => {
  emit('viewBind', target)
}

const handleResetPassword = (target: OperaRow<ManagerByPageItemModel>) => {
  emit('resetPassword', target)
}

const handleDeleteAccount = (target: OperaRow<ManagerByPageItemModel>) => {
  emit('deleteAccount', target)
}

defineExpose({ getData: searchFunc })
</script>

<template>
  <div class="table-container ml-10">
    <el-form ref="formRef" :model="formData" inline>
      <el-form-item prop="account" label="账号名：">
        <el-input v-model="formData.account"></el-input>
      </el-form-item>

      <el-form-item prop="realName" label="用户名：">
        <el-input v-model="formData.realName"></el-input>
      </el-form-item>

      <el-form-item prop="enabledMark" label="是否启用：">
        <el-select v-model="formData.enabledMark">
          <el-option label="全部" value=""></el-option>
          <el-option label="已启用" :value="true"></el-option>
          <el-option label="未启用" :value="false"></el-option>
        </el-select>
      </el-form-item>

      <el-form-item>
        <el-button v-auth.disabled="'getSysManagerByPage'" type="primary" @click="handleSearch">搜索</el-button>
        <el-button v-auth.disabled="'getSysManagerByPage'" @click="handleReset">重置</el-button>
        <el-button v-auth.disabled="'addSysManager'" type="success" @click="handleNewAccount">新增账号</el-button>
      </el-form-item>
    </el-form>

    <BaseTable :data="tableData" border height="660">
      <el-table-column prop="account" label="账号"></el-table-column>
      <el-table-column prop="realName" label="用户名"></el-table-column>
      <el-table-column label="是否启用" align="center" width="90">
        <template #default="{ row }: { row: ManagerByPageItemModel }">
          <el-tag :type="row.enabledMark ? 'success' : 'danger'">{{ row.enabledMark ? '启用' : '停用' }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="createTime" label="创建时间" width="200"></el-table-column>
      <el-table-column prop="modifyTime" label="更新时间" width="200"></el-table-column>
      <el-table-column label="操作" min-width="320">
        <template #default="{ row }: { row: OperaRow<ManagerByPageItemModel> }">
          <el-button v-auth="['getSysManagerForEdit']" @click="handleEditInfo(row)"> 编辑 </el-button>

          <el-button
            v-auth="[
              'getSysOrganizationsByManagerIdByPage',
              'getRolesByManagerIdByPage',
              'getSysManagerGroupsByManagerIdByPage',
            ]"
            type="primary"
            @click="handleViewBind(row)"
          >
            查看
          </el-button>

          <el-button v-auth="['updatePassword']" type="success" @click="handleResetPassword(row)"> 重置密码 </el-button>

          <el-button v-auth="'deleteSysManager'" :loading="row.loading" type="danger" @click="handleDeleteAccount(row)">
            删除
          </el-button>
        </template>
      </el-table-column>
    </BaseTable>

    <BasePagination v-model:current-page="currentPage" v-model:page-size="pageSize" :total="total" />
  </div>
</template>

<style lang="scss" scoped>
.table-container {
  flex-grow: 1;
  padding: 20px;
  background-color: var(--el-bg-color-overlay);
}
</style>
