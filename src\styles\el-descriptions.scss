.el-descriptions {
  .image-list-nav {
    padding: 8px 15px !important;
    .image-list-bar {
      display: flex;
      align-items: center;
      flex-wrap: wrap;
      margin-top: -9px;
    }
  }

  .el-descriptions__table,.is-bordered {
    // 将表格的 table-layout 设置为 fixed，这样单元格的宽度将基于显式的宽度设置，而不是内容
    table-layout: fixed;
  }

  .el-descriptions__body .el-descriptions__table .el-descriptions__cell {
    padding: 6px 10px;
    line-height: 36px;
    word-break: break-all;
  }

  .el-descriptions__content.el-descriptions__cell.is-bordered-content {
    color: #606266;
  }

  .el-descriptions__label.el-descriptions__cell.is-bordered-label {
    color: #909399;
    background: #FAFAFA;
    width: 200px;
  }

  @media screen and (max-width: 1000px) {
    .el-descriptions__label.el-descriptions__cell.is-bordered-label {
      width: 100px;
    }
  }
}
