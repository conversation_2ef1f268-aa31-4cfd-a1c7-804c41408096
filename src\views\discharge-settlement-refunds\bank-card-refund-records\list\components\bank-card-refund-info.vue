<script setup lang="ts">
/**
 * 出院结算退费 - 银行卡退费记录
 */
import {
  BankCardRefundRecordsItem,
  ReqGetBankCardRefundRecordsByPage,
  BankCardRefundAuditStatusOptions,
  BankCardRefundAuditStatusConfig
} from '@/api/dto/discharge-settlement-refunds/bank-card-refund-records.dto.ts'

type ListDataType = BankCardRefundRecordsItem[]

const emits = defineEmits<{
  (e: 'search', data: { currentPage: number; pageSize: number; searchData: ReqGetBankCardRefundRecordsByPage }): void
  (e: 'viewDetail', data: BankCardRefundRecordsItem): void
  (e: 'audit', data: BankCardRefundRecordsItem): void
}>()

defineProps<{
  listData: ListDataType
  total: number
}>()

// 搜索表单
const searchFormData = reactive<ReqGetBankCardRefundRecordsByPage>({} as ReqGetBankCardRefundRecordsByPage)

// 分页
const paginationData = reactive({
  currentPage: 1,
  pageSize: 10
})

onMounted(() => {
  // 默认加载页面时搜索
  emitSearch()
})

// 搜索
function handleSearch() {
  paginationData.currentPage = 1
  emitSearch()
}

// 重置
function handleReset() {
  searchFormData.patientNo = ''
  searchFormData.patientName = ''
  searchFormData.status = undefined
  emitSearch()
}

function emitSearch() {
  emits('search', {
    currentPage: paginationData.currentPage,
    pageSize: paginationData.pageSize,
    searchData: toRaw(searchFormData)
  })
}

function handleViewDetail(row: BankCardRefundRecordsItem) {
  emits('viewDetail', row)
}

function handleSizeChange() {
  // 切换每页条数时把当前页重置回 1
  paginationData.currentPage = 1
  emitSearch()
}

function handleCurrentChange() {
  emitSearch()
}

// 结算审核
function handleAudit(row: BankCardRefundRecordsItem) {
  emits('audit', row)
}
</script>

<template>
  <div class="page-container">
    <el-form inline :model="searchFormData">
      <el-form-item label="住院人姓名：">
        <el-input
          v-model="searchFormData.patientName"
          class="input-container"
          placeholder="请输入"
          size="large"
          clearable
        />
      </el-form-item>
      <el-form-item label="住院号：">
        <el-input
          v-model="searchFormData.patientNo"
          class="input-container"
          placeholder="请输入"
          size="large"
          clearable
        />
      </el-form-item>
      <el-form-item label="状态：">
        <el-select v-model="searchFormData.status" class="input-container" placeholder="请选择" size="large" clearable>
          <el-option
            v-for="item in BankCardRefundAuditStatusOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button size="large" type="primary" @click="handleSearch"> 搜索 </el-button>
        <el-button size="large" @click="handleReset"> 重置 </el-button>
      </el-form-item>
    </el-form>

    <BaseTable class="table-container" :data="listData" border width="100%" height="580">
      <el-table-column prop="patientName" label="住院人姓名" width="190" />
      <el-table-column prop="patientNo" label="住院号" width="190" />
      <el-table-column prop="patientDept" label="住院科室" width="190" />
      <el-table-column prop="patientInTime" label="入院时间" width="190" />
      <el-table-column prop="patientOutTime" label="出院时间" width="190" />
      <el-table-column prop="totalAmount" label="费用总金额（元）" width="190" />
      <el-table-column prop="medicalInsuranceAmount" label="医保金额（元）" width="190" />
      <el-table-column prop="depositAmount" label="已交押金金额（元）" width="190" />
      <el-table-column prop="refundAmount" label="结算退费金额（元）" width="190" />
      <el-table-column prop="accountName" label="开户名" width="190" />
      <el-table-column prop="accountBank" label="开户行" width="190" />
      <el-table-column prop="accountCardNo" label="银行卡号" width="190" />
      <el-table-column prop="accountPhone" label="预留号码" width="190" />
      <el-table-column prop="status" label="状态" width="200">
        <template #default="scope">
          <el-tag
            :type="
              BankCardRefundAuditStatusConfig[scope.row.status]?.tagType ||
              BankCardRefundAuditStatusConfig.default.tagType
            "
          >
            {{
              BankCardRefundAuditStatusConfig[scope.row.status]?.label || BankCardRefundAuditStatusConfig.default.label
            }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" fixed="right" min-width="220">
        <template #default="scope">
          <el-button size="small" @click="handleViewDetail(scope.row)"> 详情 </el-button>
          <el-button type="primary" size="small" @click="handleAudit(scope.row)"> 结算审核 </el-button>
        </template>
      </el-table-column>
    </BaseTable>

    <base-pagination
      v-model:current-page="paginationData.currentPage"
      v-model:page-size="paginationData.pageSize"
      :total="total"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />
  </div>
</template>

<style scoped lang="scss">
.page-container {
  padding: 20px 0;
}

.input-container {
  width: 200px;
}

.table-container {
  margin: 12px 0 30px;
}
</style>
