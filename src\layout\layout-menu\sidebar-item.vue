<script lang="tsx" setup>
import AppLink from './app-link.vue'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'
import type { RouteRecordRaw } from 'vue-router'
import { toKebabCase } from '@/utils'
import SvgIcon from '~virtual/svg-component'

const props = defineProps<{
  item: RouteRecordRaw
}>()

/**只有一个子菜单的父级 */
const onlyOneChild = ref<RouteRecordRaw>()

/**是否不显示子菜单 */
const noShowingChildren = ref(false)

/**判断是否只有一个子菜单 */
const hasOneShowingChild = computed(() => {
  onlyOneChild.value = props.item

  const showingChildren = (props.item.children || []).filter((item) => {
    if (item.hidden) {
      return false
    } else {
      // onlyOneChild.value = item
      return true
    }
  })

  if (showingChildren.length === 1) {
    return true
  }

  if (showingChildren.length === 0) {
    noShowingChildren.value = true
    return true
  }

  return false
})

/**是否为applink */
const isLink = computed(() => hasOneShowingChild.value && (!onlyOneChild.value?.children || noShowingChildren.value))

const elSvgIcon = Object.entries(ElementPlusIconsVue).map(([key, value]) => ({
  name: `el-icon${toKebabCase(key)}`,
  value,
}))

const SidebarItemIcon = ({ name }: { name: string | undefined }) => {
  if (name?.startsWith('svg')) {
    return <SvgIcon name={name as any} class="menu_item-icon"></SvgIcon>
  } else if (name?.startsWith('el')) {
    const targetIcon = elSvgIcon.find((item) => item.name === name)?.value
    if (targetIcon) return <targetIcon class="menu_item-icon el-icon" />
  }
}
</script>

<template>
  <template v-if="isLink">
    <AppLink :to="onlyOneChild!.path" :item="onlyOneChild">
      <el-menu-item :index="onlyOneChild!.path">
        <SidebarItemIcon :name="onlyOneChild?.meta?.icon" />
        <template #title>{{ onlyOneChild!.meta?.title || item.path }}</template>
      </el-menu-item>
    </AppLink>
  </template>

  <el-sub-menu v-else :index="item.path">
    <template #title>
      <SidebarItemIcon :name="item.meta?.icon" />
      <span>{{ item.meta?.title || item.path }}</span>
    </template>

    <template v-for="child in item.children">
      <sidebar-item v-if="!child.hidden && !noShowingChildren" :key="child.path" :item="child"></sidebar-item>
    </template>
  </el-sub-menu>
</template>

<style lang="scss" scoped>
.el-sub-menu {
  :deep(.el-menu) {
    background-color: $subMenuBg;
    .el-menu-item {
      &:hover {
        background-color: $subMenuHover !important;
      }
    }
  }
}

.menu_item-icon {
  flex-shrink: 0;
  width: 1em;
  height: 1em;
  margin-right: 12px;
  font-size: 14px;
  &.el-icon {
    margin-right: 7.5px;
    font-size: 18.5px;
    transform: translate(-2px);
  }
}
</style>
