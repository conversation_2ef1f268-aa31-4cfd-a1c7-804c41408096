import type { UserConfig } from 'vite'

import { visualizer } from 'rollup-plugin-visualizer'
import VueRouter from 'unplugin-vue-router/vite'
import vue from '@vitejs/plugin-vue'
import vueJsx from '@vitejs/plugin-vue-jsx'
import vueDevTools from 'vite-plugin-vue-devtools'

import ui from './ui'
import svgIcons from './svg-icon'

export default (env: Record<string, string>): UserConfig['plugins'] => {
  const vitePlugins = [VueRouter(), vue(), vueJsx(), vueDevTools()]

  vitePlugins.push(...ui())
  vitePlugins.push(svgIcons())
  vitePlugins.push(
    // 只在 VITE_ANALYZE 环境变量为 true 时才启用 visualizer
    env.VITE_ANALYZE === 'true' &&
      visualizer({
        open: true,
        filename: 'dist/stats.html',
        gzipSize: true,
        brotliSize: true
      })
  )

  return vitePlugins
}
