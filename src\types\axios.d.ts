import { AxiosRequestConfig } from 'axios'
import type { PanGuResponse } from '.'
import type { LoadingOptions } from 'element-plus'

declare module 'axios' {
  /**统一入参 */
  export type BaseEntry = {}

  /**返回模式 */
  export type RESPONSE_DATA_TYPE = 'promise' | 'array'

  /**自定义的请求属性 */
  export interface CustomAxiosRequestConfig {
    /**触发路由信息,用于401重发判断，若重发触发页面与当前页面不相符，则取消重发 */
    triggerRoute?: string
    /**是否检查token */
    checkAuthToken?: boolean
    /**是否显示loading */
    loading?: boolean | ((flag: boolean) => void)
    /**loading样式 */
    loadingOptions?: LoadingOptions
    /**是否自动关闭loading */
    autoCloseLoading?: boolean
    /**是否只返回data数据 */
    retonly?: boolean
    /**接口错误时不弹出信息由调用方负责处理 */
    mute?: boolean
    /**是否提示无数据 */
    showNoData?: boolean
    /**是否添加全局入参 */
    injectBaseParams?: boolean

    /**设置响应头 */
    setHeadersDataCallback?: (config: InternalAxiosRequestConfig) => void
    /**处理响应头数据回调函数 */
    handleHeadersDataCallback?: (headers: AxiosResponse) => void

    /**服务器500是否跳转500页面 */
    jump500?: boolean
    /**返回模式 */
    responseStructure?: RESPONSE_DATA_TYPE

    /**.net特点入参-日志收集 */
    functionName?: string
    /**.net特定入参-日志收集 */
    operationType?: string
  }

  export interface AxiosRequestConfig extends CustomAxiosRequestConfig {}

  export type ApiFuncOptions = Omit<CustomAxiosRequestConfig, 'triggerRoute' | 'checkAuthToken' | 'functionName'>

  export interface ApiFunc<Input, Output> {
    <T extends ApiFuncOptions>(...args: HandleApiFuncParam<Input, T>): ApiFuncReturn<
      T,
      Output extends undefined ? PanGuResponse : Output
    >
  }

  type HandleApiFuncParam<Input, T> = Input extends undefined ? [T?] : [Input, T?]

  type ApiFuncReturn<T extends ApiFuncOptions, Output> = T['retonly'] extends false
    ? Promise<HadnleResponseType<T, PanGuResponse<Output>>>
    : Promise<HadnleResponseType<T, Output>>

  type HadnleResponseType<T extends ApiFuncOptions, Output> = T['responseStructure'] extends 'array'
    ? [Output, error: PanGuResponse['resultMsg']]
    : Output
}
