<script lang="ts" setup>
import type { RouteLocationMatched } from 'vue-router'
import { isSymbol } from 'lodash-es'

const route = useRoute()
const router = useRouter()

/**是否为控件 */
const isControl = computed(() => !!route.meta.activeMenu)

const breadcrumbList = ref<RouteLocationMatched[]>([])
watch(
  route,
  (target) => {
    const list = target.matched.filter((item) => item.meta.useBreadcrumb ?? true)

    if (isControl.value) {
      const hasParent = list.some((item) => item.path === route.meta.activeMenu)
      if (!hasParent) {
        const targetRoute = router.getRoutes().find((item) => item.path === target.meta.activeMenu)
        targetRoute && list.splice(list.length - 1, 0, targetRoute)
      }
    }

    breadcrumbList.value = list
  },
  { immediate: true }
)

const getBreadCrumbItemPath = (target: RouteLocationMatched) => {
  if (target.children.length) {
    return ''
  } else {
    return target.path
  }
}

/**获取面包屑标题 */
const getBreadCrumbItemName = (target: RouteLocationMatched) => {
  if (target.meta.title) {
    return target.meta.title
  } else if (isSymbol(target.name)) {
    return target.name.description
  } else {
    return target.name
  }
}
</script>

<template>
  <el-breadcrumb class="layout_header_breadcrumb-container">
    <TransitionGroup name="breadcrumb">
      <el-breadcrumb-item v-for="item in breadcrumbList" :key="item.path" :to="getBreadCrumbItemPath(item)">
        {{ getBreadCrumbItemName(item) }}
      </el-breadcrumb-item>
    </TransitionGroup>
  </el-breadcrumb>
</template>

<style lang="scss" scoped>
.layout_header_breadcrumb-container {
  position: relative;
  min-height: 1em;

  :deep(.el-breadcrumb__inner) {
    flex-shrink: 0;
  }
}
</style>
