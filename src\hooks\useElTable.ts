import type { TableInstance } from 'element-plus'
import { debounce } from 'lodash-es'
import { watch, ref, type Ref } from 'vue'

/**处理表格的上下键，回车键事件，上下选择时加滚动 */
export const useElTableKeyboardHook = <T>(tableRef: Ref<TableInstance | null>) => {
  const dom = ref<HTMLDivElement | undefined>(undefined)

  /**当前选中的项 */
  const currentRow = ref<T | null>(null)
  /**设置选中项 */
  const setCurrentRow = <T extends typeof currentRow.value | null>(row: T) => {
    tableRef.value?.setCurrentRow(row)
    currentRow.value = row
  }

  const lift = debounce((step: number = 0) => {
    if (!tableRef.value) return

    const { data, setScrollTop } = tableRef.value
    const len = data.length
    if (len === 0) return

    let rowIdx = data.indexOf(currentRow.value) + step
    rowIdx = Math.max(rowIdx, 0)
    rowIdx = Math.min(rowIdx, len - 1)
    setCurrentRow(data[rowIdx])
    const scroller = dom.value!.querySelector<HTMLTableElement>('.el-scrollbar__wrap') //滚动条元素
    const table = dom.value!.querySelector<HTMLTableElement>('.el-table__body') //滚动条元素

    if (scroller && table) {
      const row = table.rows[rowIdx] //下次高亮行
      if (row.offsetTop < scroller.scrollTop) {
        //row躲在tbody上面
        setScrollTop(row.offsetTop)
      } else if (row.offsetTop + row.clientHeight > scroller.scrollTop + scroller.clientHeight) {
        //row躲在tbody下面
        setScrollTop(row.offsetTop + row.clientHeight - scroller.clientHeight) //设置底部对齐
      }
    }
  })

  const up = (step = -1) => {
    lift(step)
  }

  const down = (step = 1) => {
    lift(step)
  }

  watch(tableRef, (target) => {
    if (target) {
      dom.value = target.$el
      if (dom.value) {
        dom.value.setAttribute('tabindex', '0')
        setTimeout(() => {
          dom.value?.focus()
        })
      }
    }
  })

  return { dom, currentRow, setCurrentRow, lift, up, down }
}
