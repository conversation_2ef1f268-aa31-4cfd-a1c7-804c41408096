<script setup lang="ts">
import type { SelectableBed } from '@/api/dto/discharge-settlement/patient-accounts.dto'
import type { SummaryMethodProps } from '@/components/common-highlight-table/types'
import { requestSettlementSelectBedNo } from '@/api/discharge-settlement.api'
import CommonHighlightTable from '@/components/common-highlight-table/common-highlight-table.vue'

/**
 * 床位选择弹窗
 */

const props = defineProps<{
  // 传入病区代码
  deptcode: string
  // 默认选中的床位
  defaultBedCode?: string
  confirmCallback: (data: any) => Promise<void>
}>()

const dialogVisible = defineModel({ default: false })

// 选择的床位
const selectedBed = ref<SelectableBed>()

// 床位列表
const bedList = ref<SelectableBed[]>([])

// 表格 ref
const tableRef = ref<InstanceType<typeof CommonHighlightTable>>()

watch(
  dialogVisible,
  async (val) => {
    if (val) {
      await getBedList()
      if (props.defaultBedCode) {
        tableRef.value?.setTableSelection(bedList.value.find((bed) => bed.bedno === props.defaultBedCode))
      }
    }
  },
  { immediate: true }
)

// 获取床位列表
async function getBedList() {
  const res = await requestSettlementSelectBedNo({ deptcode: props.deptcode })
  bedList.value = res
  console.log('床位列表:', bedList.value)
}

// 弹窗 - 点击确认
async function handleConfirm(done: (flag?: boolean) => void) {
  if (!selectedBed.value) return
  try {
    // 提交
    await props.confirmCallback(JSON.parse(JSON.stringify(selectedBed.value)))
    done(false)
  } catch (e) {
    done(true)
  }
}

const getSummaries = (param: SummaryMethodProps) => {
  if (param.columns.length === 0) {
    return []
  }
  const { data } = param
  const sums: (string | VNode)[] = []

  // 总人数
  const count = data.length
  // 当前患者
  const currentPatientCount = selectedBed.value ? '1' : '0'

  sums[0] = ''
  sums[1] = `当前患者：${currentPatientCount}`
  sums[2] = ''
  sums[3] = `总人数：${count}`
  return sums
}

// 当前行变化
function handleCurrentChange(row: SelectableBed) {
  selectedBed.value = row
}

// 双击床位
async function handleRowDblclick(row: SelectableBed) {
  selectedBed.value = row
  await nextTick()
  await props.confirmCallback(JSON.parse(JSON.stringify(selectedBed.value)))
  dialogVisible.value = false
}
</script>

<template>
  <CommonFormDialog
    v-model="dialogVisible"
    title="选择床位"
    width="900px"
    show-loading
    :disabled-confirm="!selectedBed"
    @confirm="handleConfirm"
  >
    <div class="dialog-content">
      <CommonHighlightTable
        ref="tableRef"
        :data="bedList"
        :max-height="548"
        width="548px"
        show-summary
        :get-summaries="getSummaries"
        @current-change="handleCurrentChange"
        @row-dblclick="handleRowDblclick"
      >
        <el-table-column prop="bedno" label="床位" width="100" align="center" />
        <el-table-column prop="patno" label="住院号" width="120" />
        <el-table-column prop="name" label="病人姓名" width="120" />
        <el-table-column prop="sex" label="性别" width="60" align="center">
          <template #default="scope">
            {{ scope.row.sex === '1' ? '男' : scope.row.sex === '2' ? '女' : '' }}
          </template>
        </el-table-column>
        <el-table-column prop="age" label="年龄" width="60" align="center" />
        <el-table-column prop="setmeth" label="结算方式" width="180" />
      </CommonHighlightTable>

      <div class="bed-detail">
        <div class="info-item">
          <div class="label">床位号：</div>
          <div class="value">{{ selectedBed?.bedno }}</div>
        </div>
        <div class="info-item">
          <div class="label">住院号：</div>
          <div class="value">{{ selectedBed?.patno }}</div>
        </div>
        <div class="info-item">
          <div class="label">姓&nbsp;&nbsp;名：</div>
          <div class="value">{{ selectedBed?.name }}</div>
        </div>
        <div class="info-item">
          <div class="label">性&nbsp;&nbsp;别：</div>
          <div class="value">{{ selectedBed?.sex === '1' ? '男' : selectedBed?.sex === '2' ? '女' : '' }}</div>
        </div>
      </div>
    </div>
  </CommonFormDialog>
</template>

<style scoped lang="scss">
:deep(.el-table__footer) {
  // 改变显示的方式
  tr {
    display: grid;
    width: 734px;
    grid-template-columns: 0fr 5fr 0fr 7fr 0fr 0fr;
  }

  // 第二列的效果
  .el-table__cell:nth-child(2) {
    padding-left: 60px;
  }

  // 第四列的效果
  .el-table__cell:nth-child(4) {
    text-align: start;
  }
}

.dialog-content {
  display: flex;
  gap: 18px;
}

.bed-detail {
  display: flex;
  flex-direction: column;
  gap: 15px;

  .info-item {
    display: flex;
    align-items: center;

    .label {
      width: 62px;
    }

    .value {
      min-height: 36px;
      width: 140px;
      border-radius: 4px;
      background: #fafafa;
      border: 1px solid #dcdfe6;
      padding: 6px 10px;
    }
  }
}
</style>
