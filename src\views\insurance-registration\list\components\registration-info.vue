<script setup lang="ts">
/**
 * 医保登记管理 - 医保登记信息
 */
import {
  InsuranceRegistrationSearchData,
  RegisteredInsuranceInfo,
  InsuredRegistrationStatusConfig,
  WaitingInsuranceRegistrationInfo
} from '@/api/dto/insurance-registration.dto.ts'
import { ResRentalQuerydeptDTO } from '@/api/dto/rental-management/rental-order-management.dto.ts'

type ListDataType = WaitingInsuranceRegistrationInfo[] | RegisteredInsuranceInfo[]

const emits = defineEmits<{
  (e: 'search', data: { currentPage: number; pageSize: number; searchData: InsuranceRegistrationSearchData }): void
  (e: 'viewDetail', data: WaitingInsuranceRegistrationInfo | RegisteredInsuranceInfo): void
}>()

defineProps<{
  listData: ListDataType
  insuredPlaceOptions: string[]
  insuranceTypeOptions: string[]
  total: number
  mode: 'waiting' | 'registered'
  departmentOptions: ResRentalQuerydeptDTO[]
}>()

onActivated(() => {
  // 从 keep-alive 缓存中恢复时，重新触发搜索
  emitSearch()
})

// 搜索表单
const searchFormData = reactive<InsuranceRegistrationSearchData>({})

// 分页
const paginationData = reactive({
  currentPage: 1,
  pageSize: 10
})

// 搜索
function handleSearch() {
  paginationData.currentPage = 1
  emitSearch()
}

// 重置
function handleReset() {
  searchFormData.admissionName = ''
  searchFormData.admissionNo = ''
  searchFormData.insuredPlaceType = undefined
  searchFormData.insuranceType = undefined
  emitSearch()
}

function emitSearch() {
  emits('search', {
    currentPage: paginationData.currentPage,
    pageSize: paginationData.pageSize,
    searchData: toRaw(searchFormData)
  })
}

function handleViewDetail(row: WaitingInsuranceRegistrationInfo | RegisteredInsuranceInfo) {
  emits('viewDetail', row)
}

function handleSizeChange() {
  // 切换每页条数时把当前页重置回 1
  paginationData.currentPage = 1
  emitSearch()
}

function handleCurrentChange() {
  emitSearch()
}
</script>

<template>
  <div class="page-container">
    <el-form inline :model="searchFormData">
      <el-form-item label="住院人姓名：">
        <el-input
          v-model="searchFormData.admissionName"
          class="input-container"
          placeholder="请输入"
          size="large"
          clearable
        />
      </el-form-item>
      <el-form-item label="住院号：">
        <el-input
          v-model="searchFormData.admissionNo"
          class="input-container"
          placeholder="请输入"
          size="large"
          clearable
        />
      </el-form-item>
      <el-form-item label="参保类别：">
        <el-select
          v-model="searchFormData.insuredPlaceType"
          class="input-container"
          placeholder="请选择"
          size="large"
          clearable
        >
          <el-option v-for="item in insuredPlaceOptions" :key="item" :label="item" :value="item" />
        </el-select>
      </el-form-item>
      <el-form-item label="医保住院类型：">
        <el-select
          v-model="searchFormData.insuranceType"
          class="input-container"
          placeholder="请选择"
          size="large"
          clearable
        >
          <el-option v-for="item in insuranceTypeOptions" :key="item" :label="item" :value="item" />
        </el-select>
      </el-form-item>
      <el-form-item prop="hospitalizationDepartmentName" label="住院科室：">
        <el-select
          class="search-input"
          v-model="searchFormData.hospitalizationDepartmentName"
          clearable
          size="large"
          filterable
        >
          <el-option
            v-for="item in departmentOptions"
            :key="item.deptcode"
            :label="item.deptname"
            :value="item.deptname"
          />
        </el-select>
      </el-form-item>
      <el-form-item class="search-btn-group">
        <el-button
          v-auth.disabled="'insuranceRegistration_getInsuranceRegistrationByPage'"
          size="large"
          type="primary"
          @click="handleSearch"
        >
          搜索
        </el-button>
        <el-button
          v-auth.disabled="'insuranceRegistration_getInsuranceRegistrationByPage'"
          size="large"
          @click="handleReset"
        >
          重置
        </el-button>
      </el-form-item>
    </el-form>

    <BaseTable class="table-container" :data="listData" border width="100%" height="580">
      <el-table-column prop="admissionName" label="住院人姓名" width="220" />
      <el-table-column prop="admissionNo" label="住院号" width="220" />
      <el-table-column prop="hospitalizationDepartmentName" label="住院科室" width="220" />
      <el-table-column prop="insuredPlaceType" label="参保类别" width="220" />
      <el-table-column prop="insuranceType" label="医保住院类型" width="220">
        <template #default="scope">
          {{ scope.row.insuranceType || '-' }}
        </template>
      </el-table-column>
      <el-table-column prop="insuranceName" label="医保参保类型" width="220">
        <template #default="scope">
          {{ scope.row.insuranceName || '-' }}
        </template>
      </el-table-column>
      <el-table-column prop="submissionTime" label="提交时间" width="220" />
      <el-table-column v-if="mode === 'registered'" prop="registrationStatus" label="登记情况" width="180">
        <template #default="scope">
          <el-tag
            :type="
              InsuredRegistrationStatusConfig[scope.row.registrationStatus]?.tagType ||
              InsuredRegistrationStatusConfig.default.tagType
            "
          >
            {{
              InsuredRegistrationStatusConfig[scope.row.registrationStatus]?.label ||
              InsuredRegistrationStatusConfig.default.label
            }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" fixed="right" min-width="130">
        <template #default="scope">
          <el-button
            v-auth.disabled="'insuranceRegistration_getInsuranceRegistrationInfo'"
            size="small"
            @click="handleViewDetail(scope.row)"
          >
            详情
          </el-button>
        </template>
      </el-table-column>
    </BaseTable>

    <base-pagination
      v-model:current-page="paginationData.currentPage"
      v-model:page-size="paginationData.pageSize"
      :total="total"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />
  </div>
</template>

<style scoped lang="scss">
.page-container {
  padding: 20px 0;
}

.input-container {
  width: 180px;
}

.table-container {
  margin: 12px 0 30px;
}

:deep(.search-btn-group.el-form-item) {
  margin-right: 0;

  .el-form-item__content {
    min-width: 100px !important;
  }
}
</style>
