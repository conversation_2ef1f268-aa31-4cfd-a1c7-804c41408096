<script setup lang="ts">
import { FormInstance, FormRules } from 'element-plus'
import { formatPrice } from '@/utils'

/**
 * 最终清账金额 弹窗
 */

// TODO 测试数据：各个字段是否能修改还没明确，DTO 也还没声明
const testData = reactive({
  // 实收金额
  actualAmount: 10000,
  // 应收金额
  shouldAmount: 222500,
  // 应退金额
  shouldRefundAmount: 30000
})

// 格式化后的展示数据
const formattedData = computed(() => ({
  actualAmount: formatPrice(testData.actualAmount),
  shouldAmount: formatPrice(testData.shouldAmount),
  shouldRefundAmount: formatPrice(testData.shouldRefundAmount)
}))

interface RuleForm {
  // 发票张数
  invoiceCount: number | undefined
  // 实收金额
  finalActualAmount: number | undefined
}

const props = defineProps<{
  confirmCallback: (data: RuleForm) => Promise<void>
}>()

const inputFormRef = ref<FormInstance>()

const inputForm = reactive<RuleForm>({
  invoiceCount: undefined,
  finalActualAmount: undefined
})

const dialogVisible = defineModel({ default: false })

const inputFormRules = reactive<FormRules<RuleForm>>({
  invoiceCount: [{ required: true, message: '请输入发票张数', trigger: 'blur' }],
  finalActualAmount: [{ required: true, message: '请输入实收金额', trigger: 'blur' }]
})

// 弹窗关闭时，重置表单
watch(dialogVisible, (val) => {
  if (val) {
    // 打开弹窗时，同步 testData 到 inputForm
    resetInputForm()
  } else {
    inputFormRef.value?.resetFields()
  }
})

// 重置表单
function resetInputForm() {
  inputForm.invoiceCount = undefined
  inputForm.finalActualAmount = undefined
}

// 弹窗 - 点击确认
async function handleConfirm(done: (flag?: boolean) => void, formEl: FormInstance | undefined) {
  if (!formEl) return
  await formEl.validate(async (valid, fields) => {
    if (valid) {
      try {
        // 提交
        await props.confirmCallback(JSON.parse(JSON.stringify(inputForm)))
        done(false)
        setTimeout(() => {
          formEl.resetFields()
        })
      } catch (e) {
        done(true)
      }
    } else {
      console.log('提交 弹窗 表单 失败 表单校验不通过：', fields)
      done(true)
    }
  })
}
</script>

<template>
  <CommonFormDialog
    v-model="dialogVisible"
    class="final-clear-amount-dialog"
    :close-on-click-modal="false"
    title="最终清账金额"
    width="580px"
    show-loading
    @confirm="handleConfirm($event, inputFormRef)"
  >
    <div class="form-item-content">
      <div class="form-item-label form-item-label--max">实收金额：</div>
      <el-input
        :model-value="formattedData.actualAmount"
        class="form-item-value--large"
        clearable
        placeholder="请输入"
        disabled
      />
    </div>

    <div class="form-item-content">
      <div class="form-item-label form-item-label--max">应收金额：</div>
      <el-input
        :model-value="formattedData.shouldAmount"
        class="form-item-value--large"
        clearable
        placeholder="请输入"
        disabled
      />
    </div>

    <div class="form-item-content">
      <div class="form-item-label form-item-label--max">应退金额：</div>
      <el-input
        :model-value="formattedData.shouldRefundAmount"
        class="form-item-value--large form-item-value--red"
        clearable
        placeholder="请输入"
        disabled
      />
    </div>

    <el-form ref="inputFormRef" size="large" :model="inputForm" :rules="inputFormRules" hide-required-asterisk>
      <div class="form-item-content">
        <div class="form-item-label">发票张数：</div>
        <el-form-item prop="invoiceCount">
          <el-input
            v-model="inputForm.invoiceCount"
            class="form-item-value form-item-value--left"
            placeholder="请输入"
          />
        </el-form-item>
      </div>

      <div class="form-item-content">
        <div class="form-item-label">实收金额：</div>
        <el-form-item prop="finalActualAmount">
          <el-input
            v-model="inputForm.finalActualAmount"
            class="form-item-value--large form-item-value--red form-item-value--height-50"
            placeholder="请输入"
          />
        </el-form-item>
      </div>
    </el-form>
  </CommonFormDialog>
</template>

<style scoped lang="scss">
.final-clear-amount-dialog {
  :deep(.el-dialog .el-dialog__body) {
    padding: 16px 20px 16px 16px !important;
  }

  :deep(.el-form-item--large) {
    margin-bottom: 0 !important;
  }

  :deep(.el-form-item__content) {
    margin-bottom: 0 !important;
    min-width: 0 !important;
  }

  :deep(.el-input .el-input__wrapper) {
    background: #fafafa !important;
  }

  :deep(.el-input.is-disabled .el-input__wrapper) {
    background-color: #ebebeb !important;
  }

  :deep(.el-input__inner) {
    color: #303133 !important;
    text-align: right !important;
    height: 48px !important;
    font-size: 28px !important;
    -webkit-text-fill-color: #303133 !important;
  }

  .form-item-value--large :deep(.el-input__inner) {
    font-size: 30px !important;
    text-align: right !important;
    height: 60px !important;
  }
  .form-item-value--red :deep(.el-input__inner) {
    color: #e10000 !important;
    -webkit-text-fill-color: #e10000 !important;
  }

  .form-item-value--left :deep(.el-input__inner) {
    text-align: left !important;
  }
}

.form-item-content {
  display: flex;
  align-items: center;
  color: #303133 !important;
  margin-bottom: 20px;

  & .form-item-label {
    width: 134px;
    text-align: right;
    font-size: 20px;

    &--max {
      font-size: 24px;
    }
  }
}

.form-item-value {
  width: 174px;

  &--large {
    width: 412px;
  }

  &--height-50 {
    height: 50px !important;
  }
}
</style>
