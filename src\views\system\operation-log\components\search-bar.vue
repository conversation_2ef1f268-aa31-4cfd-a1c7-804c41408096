<script lang="ts" setup>
import { useFormHook } from '@/hooks/useForm'
import type { GetSysOperationLogForPageInput } from '@/api/dto/system/operation-log.dto'

export type SearchData = Omit<GetSysOperationLogForPageInput, 'page' | 'rows' | 'operTimeBegin' | 'operTimeEnd'> & {
  operateTime: [string, string]
}

const { formRef, resetForm } = useFormHook()

const searchData = reactive<SearchData>({
  operateTime: ['', ''],
  account: '',
  businessmodule: '',
  operResult: '',
  operUrl: '',
  operationType: '',
  status: '',
})

const disabledDate = (time: Date) => {
  return time.getTime() > Date.now()
}

const emit = defineEmits<{
  search: [data: SearchData]
}>()

const handleSearch = () => {
  emit('search', searchData)
}

const handleReset = () => {
  resetForm()
  emit('search', searchData)
}
</script>

<template>
  <el-form ref="formRef" :model="searchData" inline class="search_bar-container" label-width="81">
    <el-form-item prop="account" label="操作账号：" class="form-item">
      <el-input v-model="searchData.account" placeholder="请输入" @keydown.enter="handleSearch"></el-input>
    </el-form-item>

    <el-form-item prop="businessmodule" label="模块名称：" class="form-item">
      <el-input v-model="searchData.businessmodule" placeholder="请输入" @keydown.enter="handleSearch"></el-input>
    </el-form-item>

    <el-form-item prop="operationType" label="操作类型：" class="form-item">
      <el-input v-model="searchData.operationType" placeholder="请输入" @keydown.enter="handleSearch"></el-input>
    </el-form-item>

    <el-form-item prop="operUrl" label="请求地址：" class="form-item">
      <el-input v-model="searchData.operUrl" placeholder="请输入" @keydown.enter="handleSearch"></el-input>
    </el-form-item>

    <el-form-item prop="status" label="状态：" class="form-item">
      <el-input v-model="searchData.status" placeholder="请输入" @keydown.enter="handleSearch"></el-input>
    </el-form-item>

    <el-form-item prop="operResult" label="访问结果：" class="form-item">
      <el-input v-model="searchData.operResult" placeholder="请输入" @keydown.enter="handleSearch"></el-input>
    </el-form-item>

    <el-form-item prop="operateTime" label="操作时间：" class="form-item">
      <el-date-picker
        v-model="searchData.operateTime"
        type="datetimerange"
        range-separator="至"
        start-placeholder="开始日期"
        end-placeholder="结束日期"
        value-format="YYYY-MM-DD HH:mm:ss"
        :editable="false"
        :clearable="false"
        :disabled-date="disabledDate"
      >
      </el-date-picker>
    </el-form-item>

    <el-form-item class="button-bar">
      <el-button type="primary" @click="handleSearch">搜索</el-button>
      <el-button @click="handleReset">重置</el-button>
    </el-form-item>
  </el-form>
</template>
