import { appMessage } from '@/hooks/useNaiveApi'
import AMapLoader from '@amap/amap-jsapi-loader'

// 加载地图API
function loadAMap() {
  window._AMapSecurityConfig = {
    securityJsCode: import.meta.env.VITE_APP_AMAP_SECURITY_JS_CODE,
  }

  const aMapLoaded = ref<boolean>(false)
  // 从环境变量读取AMAP KEY
  const key = import.meta.env.VITE_APP_AMAP_KEY

  if (key) {
    onMounted(async () => {
      await AMapLoader.load({ key, version: '2.0' })
      aMapLoaded.value = true
    })
  } else {
    const errorMessage = '地图加载失败，API Key缺失.'
    appMessage.error(errorMessage)
    throw new Error(errorMessage)
  }

  return aMapLoaded
}

// 初始化地图 ，传入作为容器的元素节点
export function useAMap(
  mapContainerRef: Ref<HTMLDivElement | undefined>,
  completeCallback: Function,
  options?: AMap.MapOptions
): Ref<AMap.Map | undefined> {
  const mapRef = shallowRef<AMap.Map>()
  const aMapLoaded = loadAMap()
  watchEffect(() => {
    if (aMapLoaded.value && mapContainerRef.value) {
      mapRef.value = new AMap.Map(mapContainerRef.value, options || {})
      mapRef.value.on('complete', completeCallback)
    }
  })
  return mapRef
}
