import { useTabsStore } from '@/stores/tabs.store'
import type { AppAfterEach } from './guards'

export const addTabs: AppAfterEach = (to) => {
  if (to.meta.useTab === false) return

  const tabsStore = useTabsStore()
  tabsStore.addView(to)
}

/**页面离开关闭tab */
export const deleteTabs: AppAfterEach = (_, form) => {
  if (form.meta.leaveOff) {
    const tabsStore = useTabsStore()
    tabsStore.delView(form)
  }
}