export interface OrgsModel {
  organizationId: string
  organizationName: string
}
export interface OrgsForAddOrUpdateModel extends OrgsModel {
  classLayer: number
  encode: string
  expanded: 'true' | 'false'
  isLeaf: 'true' | 'false'
  mainId: number
  parentId: number
}
export type GetOrgsForAddOrUpdateOutput = OrgsForAddOrUpdateModel[]

export interface GetSysManagerByPageInput {
  organizationId: string
  account: string
  realName: string
  enabledMark: string
  page: number
  rows: number
}
export interface ManagerByPageItemModel {
  account: string
  createTime: string
  createUserName: string
  enabledMark: boolean
  encode: string
  managerId: string
  modifyTime: string
  modifyUserName: string
  realName: string
}
export type GetSysManagerByPageOutput = ManagerByPageItemModel[]

export interface AccountContextInformationInput {
  keyword: string
  managerId: string
  page: number
  rows: number
}

export interface GetSysOrganizationsByManagerIdByPageInput extends AccountContextInformationInput {}
export interface OrganizationsByManagerIdByPageItemMode {
  organizationId: string
  organizationName: string
}
export type GetSysOrganizationsByManagerIdByPageOutput = OrganizationsByManagerIdByPageItemMode[]

export interface GetRolesByManagerIdByPageInput extends AccountContextInformationInput {}
export interface RolesByManagerIdByPageItemMode {
  roleId: string
  roleName: string
}
export type GetRolesByManagerIdByPageOutput = RolesByManagerIdByPageItemMode[]

export interface GetSysManagerGroupsByManagerIdByPageInput extends AccountContextInformationInput {}
export interface ManagerGroupsByManagerIdByPageItemMode {
  managerGroupId: string
  managerGroupName: string
}
export type GetSysManagerGroupsByManagerIdByPageOutput = ManagerGroupsByManagerIdByPageItemMode[]

export interface ResetPasswordInput {
  managerId: string
  password: string
}

export interface DeleteSysManagerInput {
  id: string
}

export interface RightSysOrganizationModel {
  classLayer: number
  encode: string
  expanded: 'true' | 'false'
  isLeaf: 'true' | 'false'
  mainId: number
  organizationId: string
  organizationName: string
  parentId: number
}
export type GetRightSysOrganizationForShowOutput = RightSysOrganizationModel[]

export interface ManagerGroupsModel {
  managerGroupId: string
  managerGroupName: string
}
export type GetManagerGroupsForAddOrUpdateOutput = ManagerGroupsModel[]

export interface RolesModel {
  roleId: string
  roleName: string
}
export type GetRolesForAddOrUpdateOutput = RolesModel[]

export interface AddSysManagerInput {
  account: string
  description: string
  email: string
  enabledMark: boolean
  encode: string
  forbiddenStartDate?: string
  forbiddenEndDate?: string
  forbiddenStartTime?: string
  forbiddenEndTime?: string
  gender: string
  isSys: false
  managergroupIds: string
  mobile: string
  orgIds: string
  password: string
  qicqno: string
  realName: string
  roleIds: string
  sortCode: number
  telephone: string
  weChat: string
}

export interface GetSysManagerForEditInput {
  id: string
}
export interface GetSysManagerForEditOutput extends Omit<AddSysManagerInput, 'managergroupIds' | 'orgIds' | 'roleIds'> {
  managergroups?: GetManagerGroupsForAddOrUpdateOutput
  orgs?: OrgsModel[]
  roles?: GetRolesForAddOrUpdateOutput
}

export interface UpdateSysManagerInput extends AddSysManagerInput {
  managerId: string
  // managergroups: []
  // orgs: []
  // roles: []
}
