import type { RouteRecordRaw } from 'vue-router'
import { ROUTER_PATH } from '../router-path'

const Layout = () => import('@/layout')

export const inpatientRegistrationRouter: RouteRecordRaw = {
  path: ROUTER_PATH.INPATIENT_REGISTRATION,
  name: 'InpatientRegistration',
  component: Layout,
  redirect: ROUTER_PATH.INPATIENT_REGISTRATION_LIST,
  meta: { title: '入院登记管理', icon: 'el-icon-document-add' },
  children: [
    // 根级路由从动态路由接口获取
    // {
    //   path: ROUTER_PATH.INPATIENT_REGISTRATION_LIST,
    //   name: 'InpatientRegistrationList',
    //   component: () =>
    //     import('@/views/inpatient-registration/inpatient-registration-list/inpatient-registration-list.vue'),
    //   meta: {
    //     title: '住院登记信息',
    //     leaveOff: false
    //   }
    // },

    {
      path: ROUTER_PATH.INPATIENT_REGISTRATION_DETAIL,
      name: 'InpatientRegistrationDetail',
      hidden: true,
      component: () =>
        import('@/views/inpatient-registration/inpatient-registration-detail/inpatient-registration-detail.vue'),
      meta: {
        title: '住院登记详情',
        leaveOff: true,
        permissionFrom: ROUTER_PATH.INPATIENT_REGISTRATION_LIST,
        activeMenu: ROUTER_PATH.INPATIENT_REGISTRATION_LIST,
        noCache: true,
        useTab: false
      }
    }
  ]
}
