<script setup lang="ts">
import {
  AuditStatus,
  AuditStatusConfig,
  OrderModifyInfo
} from '@/api/dto/rental-management/rental-order-management.dto.ts'

defineProps({
  orderModifyInfo: {
    type: Array<OrderModifyInfo>,
    required: true,
    default: () => ({})
  }
})

defineEmits<{
  (
    e: 'modify',
    data: {
      orderItem: OrderModifyInfo
    }
  ): void
}>()
</script>

<template>
  <div>
    <div class="common-panel-title">
      <span>订单修改</span>
    </div>
    <BaseTable :data="orderModifyInfo" border width="100%" max-height="300">
      <el-table-column prop="updateServerType" label="修改类型" width="230" />
      <el-table-column prop="serverPrice" label="价格" width="230">
        <template #default="scope">
          {{ `${scope.row.serverPrice}元/天` || '-' }}
        </template>
      </el-table-column>
      <el-table-column prop="leaseTime" label="租赁时间" width="230" />
      <el-table-column prop="feeType" label="费用类型" width="190" />
      <el-table-column prop="totalAmount" label="金额（元）" width="190" />
      <el-table-column prop="reviewStatus" label="状态" width="160">
        <template #default="scope">
          <el-tag :type="AuditStatusConfig[scope.row.reviewStatus]?.tagType || AuditStatusConfig.default.tagType">
            {{ AuditStatusConfig[scope.row.reviewStatus]?.label || AuditStatusConfig.default.label }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="reviewTime" label="审核时间" width="190">
        <template #default="scope">
          {{ scope.row.reviewTime || '-' }}
        </template>
      </el-table-column>

      <el-table-column label="操作" fixed="right" min-width="220">
        <template #default="scope">
          <el-button
            v-if="scope.row.reviewStatus === AuditStatus.PENDING"
            v-auth.disabled="'InpatientCaregiverModifyReview'"
            size="small"
            type="warning"
            @click="$emit('modify', { orderItem: scope.row })"
            >修改审核
          </el-button>
        </template>
      </el-table-column>
    </BaseTable>
  </div>
</template>

<style scoped lang="scss"></style>
