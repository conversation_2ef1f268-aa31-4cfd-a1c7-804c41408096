<script setup lang="ts">
defineProps({
  fileItem: {
    type: Array as PropType<string[]>,
    required: true,
    default: () => []
  }
})

const dialogVisible = defineModel({ default: false })
</script>
<template>
  <CommonFormDialog v-model="dialogVisible" title="查看" width="560px" @confirm="dialogVisible = false">
    <div class="picture-container">
      <el-image
        class="image-item"
        v-for="(item, index) in fileItem"
        :key="index"
        :src="item"
        :preview-src-list="fileItem"
        :initial-index="index"
        fit="contain"
        :infinite="false"
      />
    </div>
  </CommonFormDialog>
</template>

<style scoped lang="scss">
.picture-container {
  display: flex;
  gap: 16px;
  flex-flow: wrap;
}

.image-item {
  height: 120px;
  width: 120px;
}
</style>
