<script setup lang="ts">
import {
  RefundCirculationInfoItem,
  BankCardRefundAuditStatusConfig
} from '@/api/dto/discharge-settlement-refunds/bank-card-refund-records.dto.ts'
import { SuccessFilled } from '@element-plus/icons-vue'

defineProps<{
  circulationInfo: RefundCirculationInfoItem[]
}>()

function getStepTitle(status: number) {
  return BankCardRefundAuditStatusConfig[status]?.label || BankCardRefundAuditStatusConfig.default.label
}
</script>

<template>
  <el-steps class="steps" direction="vertical" :active="0" finish-status="success">
    <el-step v-for="item in circulationInfo" :title="getStepTitle(item.status)" :icon="SuccessFilled">
      <template #description>
        <div class="steps-description">
          <div class="steps-description-prefix" />
          <div>
            <div class="steps-description-text">{{ item.content }}</div>
            <div class="steps-description-date">{{ item.createTime }}</div>
          </div>
        </div>
      </template>
    </el-step>
  </el-steps>
</template>

<style scoped lang="scss">
.steps {
  height: 10px;

  &-description {
    display: flex;
    background-color: #f6f8fa;
    padding: 16px 12px;
    margin-top: 16px;
    font-size: 14px;
    margin-bottom: 24px;
    align-items: baseline;

    &-prefix {
      width: 8px;
      height: 8px;
      background: #67c23a;
      border-radius: 50%;
      margin-right: 12px;
      flex: none;
    }

    &-text {
      color: #606266;
    }

    &-date {
      color: #909399;
      margin-top: 8px;
    }
  }

  :deep(.el-step__head.is-process .el-step__icon) {
    color: #67c23a;
  }

  :deep(.el-step__icon) {
    width: 16px !important;
    height: 16px !important;
    color: #dcdfe6;
  }

  :deep(.el-step__title) {
    color: #303133 !important;
    font-size: 16px !important;
    font-weight: 400 !important;
  }

  :deep(.el-step__line) {
    left: 8px !important;
  }

  :deep(.el-step__head) {
    margin-top: 4px;
  }
}
</style>
