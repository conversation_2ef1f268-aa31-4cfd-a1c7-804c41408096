/**盘古c#返回code枚举 */
export enum PAN_GU_RESPONSE_CODE {
  /**成功 */
  SUCCESS = 'success',
  /**没数据 */
  NORECORD = 'norecord',
  /**失败 */
  FAIL = 'fail',
}

/**盘古C#接口返回model */
export interface PanGuResponse<T = any> {
  /**返回信息 */
  resultMsg: string
  /**返回信息 */
  resultMessage?: string
  /**返回code */
  resultCode: PAN_GU_RESPONSE_CODE
  /**数据 */
  data: T
  /**页数 */
  pageCount?: number
  /**页数 */
  totalCount?: number
  /**java当前页 */
  pageIndex?: number
  /**java当前页 */
  pageSize?: number
  /**java总记录数 */
  recordCount?: number
}

export enum JWT_K3 {
  ADMIN = '1',
  TOURIST = '0',
}

/**盘古java jwt数据格式 */
export interface PangeJavaJwtData {
  /**token结束时间 */
  exp: number
  /**token开始时间 */
  iat: number
  jti: string
  /**账号主键 */
  k1: string
  /**用户名 */
  k2: string
  /**是否为管理员 */
  k3: JWT_K3
  sub: string
}
