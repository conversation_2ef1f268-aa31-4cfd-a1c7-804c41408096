<script lang="ts" setup>
import type { GetSysDictionaryDetailForUpdateInput, AddSysDictionaryDetailInput } from '@/api/dto/system/dictionary.dto'
import {
  addSysDictionaryDetailApi,
  getSysDictionaryDetailForUpdateApi,
  updateSysDictionaryDetailApi,
} from '@/api/system/dictionary.api'
import ElConfig from '@/components/el-config'
import { useElDialogHook } from '@/hooks/useDialog'
import { useFormHook } from '@/hooks/useForm'
import { useLoadingHook } from '@/hooks/useLoading'
import { appMessage } from '@/hooks/useNaiveApi'
import type { DialogBeforeCloseFn, FormRules } from 'element-plus'

export interface OpenParams extends GetSysDictionaryDetailForUpdateInput {
  dictionaryInfoId: string
}
type FormData = Omit<AddSysDictionaryDetailInput, 'dictionaryDetailId' | 'dictionaryInfoId'>

const { dialogVisible, __open, __close, promiseFunc } = useElDialogHook<boolean>()

const { formRef, validate, resetForm } = useFormHook()

const { loading, loadingFunc } = useLoadingHook()

const formData = reactive<FormData>({
  description: '',
  dictionaryDetailName: '',
  encode: '',
  enabledMark: true,
  isSys: false,
  sortCode: 1,
})
/**表单验证规则 */
const rules = reactive<FormRules<FormData>>({
  dictionaryDetailName: [{ required: true, message: '请输入字典名字', trigger: 'blur' }],
  encode: [{ required: true, message: '请输入字典编码', trigger: 'blur' }],
  enabledMark: [{ required: true, message: '请选择是否可以用', trigger: 'blur' }],
  isSys: [{ required: true, message: '请选择是否为系统字段', trigger: 'blur' }],
})

const getSysDictionaryDetailForUpdate = async () => {
  try {
    const result = await getSysDictionaryDetailForUpdateApi({ dictionaryDetailId: dictionaryDetailId.value })
    formData.dictionaryDetailName = result.dictionaryDetailName
    formData.encode = result.encode
    formData.enabledMark = result.enabledMark
    formData.isSys = result.isSys
    formData.sortCode = result.sortCode
    formData.description = result.description
  } catch (error) {
    resetForm()
    __close()
    promiseFunc.reject?.('获取字典详情失败，请稍后重试')
  }
}

/**修改值 */
const updateSysDictionaryDetail = async () => {
  await updateSysDictionaryDetailApi(
    {
      ...formData,
      dictionaryInfoId: dictionaryInfoId.value,
      dictionaryDetailId: dictionaryDetailId.value,
    },
    { loading: loadingFunc }
  )
}

/**新增值 */
const addSysDictionaryDetail = async () => {
  await addSysDictionaryDetailApi(
    {
      ...formData,
      dictionaryInfoId: dictionaryInfoId.value,
    },
    { loading: loadingFunc }
  )
}

/**确认按钮 */
const confrim = async () => {
  try {
    await validate()

    if (dictionaryDetailId.value) {
      // 修改
      await updateSysDictionaryDetail()
      appMessage.success('修改成功')
    } else {
      // 新增
      await addSysDictionaryDetail()
      appMessage.success('新增成功')
    }
    promiseFunc.resolve?.(true)
    __close()
  } catch (error) {}
}

const handleBeforeClose: DialogBeforeCloseFn = async (done) => {
  try {
    await validate()
    if (loading.value) {
      appMessage.warning('正在提交中，请稍后')
      return
    }
    done()
  } catch (error) {}
}

/**父级字典ID */
const dictionaryInfoId = ref('')
/**值ID */
const dictionaryDetailId = ref('')

const handleOpenDialog = async (openParams: OpenParams) => {
  console.log(openParams)

  setTimeout(() => {
    dictionaryInfoId.value = openParams.dictionaryInfoId
    if (openParams.dictionaryDetailId) {
      dictionaryDetailId.value = openParams.dictionaryDetailId
      getSysDictionaryDetailForUpdate()
    }
  })

  return await __open()
}

const handleCloseDialog = () => {
  resetForm()
  dictionaryInfoId.value = ''
  dictionaryDetailId.value = ''
  __close()
  promiseFunc.reject?.('取消')
}

const handleClosed = () => {
  resetForm()
  dictionaryInfoId.value = ''
  dictionaryDetailId.value = ''
}

defineExpose({
  __open: handleOpenDialog,
  __close: handleCloseDialog,
})
</script>

<template>
  <ElConfig>
    <el-dialog
      v-model="dialogVisible"
      :before-close="handleBeforeClose"
      width="420"
      class="dictionary_dialog-container"
      @closed="handleClosed"
    >
      <template #header>
        <div class="title">{{ dictionaryDetailId ? '修改值' : '新增值' }}</div>
      </template>

      <el-form ref="formRef" :model="formData" :rules="rules" label-width="120">
        <el-form-item prop="dictionaryDetailName" label="值名称：">
          <el-input v-model="formData.dictionaryDetailName"></el-input>
        </el-form-item>

        <el-form-item prop="encode" label="编号标识：">
          <el-input v-model="formData.encode"></el-input>
        </el-form-item>

        <el-form-item prop="sortCode" label="排序：">
          <el-input-number v-model="formData.sortCode" :min="0"></el-input-number>
        </el-form-item>

        <el-form-item label="是否启用：" prop="enabledMark">
          <el-switch v-model="formData.enabledMark" active-text="启用后才能显示"> </el-switch>
        </el-form-item>

        <el-form-item label="系统管理：" prop="isSys">
          <el-switch v-model="formData.isSys" active-text="开启后无法删除"> </el-switch>
        </el-form-item>

        <el-form-item prop="description" label="备注：">
          <el-input v-model="formData.description" type="textarea" placeholder="请输入备注"></el-input>
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button :disabled="loading" @click="handleCloseDialog">取 消</el-button>
        <el-button :loading="loading" type="primary" @click="confrim">确 定</el-button>
      </template>
    </el-dialog>
  </ElConfig>
</template>

<style lang="scss" scoped>
.dictionary_dialog-container {
  .title {
    height: 44px;
    padding: 10px 16px;
    font-size: 18px;
    color: var(--el-text-color-regular);
  }
}
</style>
