import GlobalTooltip from '@/components/global-tooltip'

export const useTooltip = (content = '') => {
  /**是否显示 */
  const visible = ref(false)
  /**触发对象，实现单例 */
  const virtualRef = ref<HTMLElement>()
  /**显示内容 */
  const tooltipContent = ref(content)

  // 异步加载
  const overlayElement = document.createElement('div')
  document.body.appendChild(overlayElement)

  const OverlayInstance = createApp(GlobalTooltip, {
    visible,
    virtualRef,
    content: tooltipContent,
  })
  OverlayInstance.mount(overlayElement)

  return { visible, virtualRef, tooltipContent }
}
