<script setup lang="ts">
import { FormInstance, FormRules } from 'element-plus'
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import {
  requestConfirmLeaseOfCaregiverPerson,
  requestPersonnelReplacementReviewInfo
} from '@/api/rental-management.api.ts'
import {
  ConfirmLeaseOfCaregiverPerson,
  ResPersonnelReplacementReviewInfo
} from '@/api/dto/rental-management/rental-order-management.dto.ts'

interface RuleForm {
  accompanyingPerson: string
  reviewOperate: string
}

const props = defineProps<{
  inpatientCaregiverId: string
  confirmCallback: (
    inpatientCaregiverId: string,
    inpatientCaregiverPersonId: string,
    reviewOperate: boolean
  ) => Promise<void>
}>()

const inputFormRef = ref<FormInstance>()
const getAccompanyingPersonsLoading = ref(false)

// 选择陪护人
const inputForm = reactive<{
  accompanyingPerson: string
  reviewOperate: boolean | undefined
}>({
  accompanyingPerson: '',
  reviewOperate: undefined
})

const dialogVisible = defineModel({ default: false })

const inputFormRules = reactive<FormRules<RuleForm>>({
  accompanyingPerson: [{ required: true, message: '选择陪护人', trigger: 'change' }],
  reviewOperate: [{ required: true, message: '请选择审核结果', trigger: 'change' }]
})

const auditActionOptions = [
  {
    label: '审核通过',
    value: true
  },
  {
    label: '审核不通过',
    value: false
  }
]

// 换人审核信息
const replacementReviewInfo = ref<ResPersonnelReplacementReviewInfo>()

// 陪护人选择相关
const popoverVisible = ref(false)
const accompanyingPersonList = ref<ConfirmLeaseOfCaregiverPerson[]>([])
const selectedAccompanyingPerson = ref<ConfirmLeaseOfCaregiverPerson | null>(null)

// 分页
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)

const searchParams = reactive<{
  name: string
  isCaregiver: boolean | undefined
}>({
  name: '',
  isCaregiver: undefined
})

watch(
  dialogVisible,
  (v) => {
    if (v) {
      // 重置表单，避免在点击编辑后再点击新增时候，因为弹窗组件没有销毁导致数据不清空
      inputFormRef.value?.resetFields()
      replacementReviewInfo.value = undefined
      setTimeout(() => {
        fetchReplacementReviewInfo()
      })
    }
  },
  {
    immediate: true
  }
)

// 获取换人审核信息
const fetchReplacementReviewInfo = async () => {
  try {
    const res = await requestPersonnelReplacementReviewInfo({ inpatientCaregiverId: props.inpatientCaregiverId })
    console.log('获取换人审核信息成功：', res)
    replacementReviewInfo.value = res
  } catch (error) {
    console.error('获取换人审核信息失败:', error)
  }
}

// 获取陪护人列表
const fetchAccompanyingPersons = async () => {
  getAccompanyingPersonsLoading.value = true
  try {
    const { data, recordCount } = await requestConfirmLeaseOfCaregiverPerson({
      name: searchParams.name,
      isCaregiver: searchParams.isCaregiver,
      page: currentPage.value,
      rows: pageSize.value
    })

    accompanyingPersonList.value = data
    total.value = recordCount
  } catch (error) {
    console.error('获取陪护人列表失败:', error)
    ElMessage.error('获取陪护人列表失败')
  } finally {
    getAccompanyingPersonsLoading.value = false
  }
}

const handleSearch = () => {
  currentPage.value = 1
  fetchAccompanyingPersons()
}

const handleReset = () => {
  searchParams.name = ''
  searchParams.isCaregiver = undefined
  handleSearch()
}

const selectPerson = (person: ConfirmLeaseOfCaregiverPerson) => {
  selectedAccompanyingPerson.value = person
  inputForm.accompanyingPerson = person.name
  popoverVisible.value = false
}

const handleCurrentChange = (page) => {
  currentPage.value = page
  fetchAccompanyingPersons()
}

// 点击确认租赁
async function handleConfirm(done: (flag?: boolean) => void, formEl: FormInstance | undefined) {
  if (!formEl) return
  await formEl.validate(async (valid, fields) => {
    if (valid) {
      if (!selectedAccompanyingPerson.value?.inpatientCaregiverPersonId) {
        ElMessage.error('换人审核信息 ID 不存在，请稍后重试')
        done(true)
        return
      }

      try {
        await props.confirmCallback(
          props.inpatientCaregiverId,
          selectedAccompanyingPerson.value?.inpatientCaregiverPersonId,
          inputForm.reviewOperate!
        )
        done(false)
        setTimeout(() => {
          formEl.resetFields()
          selectedAccompanyingPerson.value = null
        })
      } catch (e) {
        done(true)
      }
    } else {
      console.log('提交 确定服务 表单 失败 表单校验不通过：', fields)
      done(true)
    }
  })
}
</script>

<template>
  <CommonFormDialog
    v-model="dialogVisible"
    title="换人审核"
    width="732px"
    show-loading
    :close-on-click-modal="false"
    @confirm="handleConfirm($event, inputFormRef)"
  >
    <template v-if="replacementReviewInfo">
      <el-descriptions title="" border :column="1">
        <el-descriptions-item label="换人原因：">{{ replacementReviewInfo?.substitutionReason }}</el-descriptions-item>
        <el-descriptions-item label="提交时间：">{{ replacementReviewInfo?.createTime }}</el-descriptions-item>
      </el-descriptions>

      <el-form
        ref="inputFormRef"
        class="mt-20"
        size="large"
        :model="inputForm"
        :rules="inputFormRules"
        label-width="110px"
      >
        <el-form-item label="审核操作：" prop="reviewOperate">
          <el-select v-model="inputForm.reviewOperate" placeholder="请选择" size="large" clearable>
            <el-option
              v-for="(item, index) in auditActionOptions"
              :key="index"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="选择陪护人：" prop="accompanyingPerson">
          <el-popover
            placement="top-start"
            :width="920"
            trigger="click"
            v-model:visible="popoverVisible"
            @show="fetchAccompanyingPersons"
          >
            <template #reference>
              <el-select
                popper-class="rental-replacement-accompanying-person-selector"
                v-model="inputForm.accompanyingPerson"
                placeholder="请选择"
                readonly
              />
            </template>

            <div class="popover-content" v-loading="getAccompanyingPersonsLoading">
              <el-form :inline="true">
                <el-form-item label="陪护人名称：">
                  <el-input class="search-input" v-model="searchParams.name" placeholder="请输入" clearable />
                </el-form-item>
                <el-form-item label="是否陪护中：">
                  <el-select
                    class="search-input"
                    v-model="searchParams.isCaregiver"
                    placeholder="请选择"
                    :teleported="false"
                    clearable
                  >
                    <el-option label="是" :value="true" />
                    <el-option label="否" :value="false" />
                  </el-select>
                </el-form-item>
                <el-form-item>
                  <el-button type="primary" @click="handleSearch">搜索</el-button>
                  <el-button @click="handleReset">重置</el-button>
                </el-form-item>
              </el-form>

              <el-table :data="accompanyingPersonList" border height="300">
                <el-table-column prop="name" label="陪护人名称" min-width="140" />
                <el-table-column prop="gender" label="性别" width="140" />
                <el-table-column prop="age" label="年龄" width="140" />
                <el-table-column prop="isCaregiver" label="是否陪护中" width="140">
                  <template #default="{ row }">
                    {{ row.isCaregiver ? '是' : '否' }}
                  </template>
                </el-table-column>
                <el-table-column prop="phone" label="联系电话" width="140" />
                <el-table-column label="操作" width="140" fixed="right">
                  <template #default="{ row }">
                    <el-button type="success" size="small" @click="selectPerson(row)">选择</el-button>
                  </template>
                </el-table-column>
              </el-table>

              <div class="pagination-wrapper">
                <el-pagination
                  v-model:current-page="currentPage"
                  layout="prev, pager, next, jumper"
                  :total="total"
                  @current-change="handleCurrentChange"
                />
              </div>
            </div>
          </el-popover>
        </el-form-item>
      </el-form>
    </template>
  </CommonFormDialog>
</template>

<style>
.rental-replacement-accompanying-person-selector {
  display: none;
}
</style>

<style scoped lang="scss">
.patient-info {
  height: 40px;
  border-radius: 4px;
  background: #ecf5ff;
  border: 1px solid #d9ecff;
  color: #1890ef;
  line-height: 40px;
  padding: 0 16px;
}

.mt-20 {
  margin-top: 20px;
}

:deep(.el-descriptions .el-descriptions__label.el-descriptions__cell.is-bordered-label) {
  width: 160px;
}

.popover-content {
  padding: 10px;

  .pagination-wrapper {
    margin-top: 20px;
    display: flex;
    justify-content: flex-start;
  }
}

.search-input {
  width: 200px;
}
</style>
