<script setup lang="ts">
import { ReqSaveInpatientCaregiverIntroduceInfo } from '@/api/dto/rental-management/caregiver-intro-management.dto.ts'
import {
  requestInpatientCaregiverIntroduceInfo,
  requestSaveInpatientCaregiverIntroduceInfo
} from '@/api/rental-management.api.ts'
import CommonArticleEditor from '@/components/common-article-editor/common-article-editor.vue'
import CommonImgUpload from '@/components/common-img-upload/common-img-upload.vue'
import { useFormHook } from '@/hooks/useForm.ts'
import { appMessage } from '@/hooks/useNaiveApi.ts'
import { FormRules } from 'element-plus'
import type { UploadUserFile } from 'element-plus'

/**
 * 陪护介绍管理
 */

const { formRef } = useFormHook()

const serviceIntroduceEditorRef = ref<InstanceType<typeof CommonArticleEditor>>()
const teamIntroduceEditorRef = ref<InstanceType<typeof CommonArticleEditor>>()

const formData = ref<ReqSaveInpatientCaregiverIntroduceInfo>({
  serverName: '',
  phone: '',
  base64picture: '',
  serverIntroduce: '',
  teamIntroduce: ''
} as ReqSaveInpatientCaregiverIntroduceInfo)

const rules = reactive<FormRules>({
  serverName: [{ required: true, message: '请输入服务名称', trigger: 'blur' }],
  phone: [{ required: true, message: '请输入咨询电话', trigger: 'blur' }],
  base64picture: [{ required: true, message: '请选择主图', trigger: 'change' }],
  serverIntroduce: [{ required: true, message: '请输入服务介绍', trigger: 'blur' }],
  teamIntroduce: [{ required: true, message: '请输入团队介绍', trigger: 'blur' }]
})

const uploadFileList = ref<UploadUserFile[]>([])

// 初始化时获取陪护介绍信息
onMounted(async () => {
  await fetchCaregiverIntroduceInfo()
})

// 获取陪护介绍信息
async function fetchCaregiverIntroduceInfo() {
  try {
    const data = await requestInpatientCaregiverIntroduceInfo()
    console.log('获取陪护介绍信息：', data)

    // 填充表单数据
    formData.value = {
      serverName: data.serverName,
      phone: data.phone,
      base64picture: data.picture,
      serverIntroduce: data.serverIntroduce,
      teamIntroduce: data.teamIntroduce
    }

    await nextTick()

    // 设置介绍内容
    serviceIntroduceEditorRef.value?.insertHtml(data.serverIntroduce)
    teamIntroduceEditorRef.value?.insertHtml(data.teamIntroduce)

    // 如果有主图，则设置上传文件列表
    if (data.picture) {
      uploadFileList.value = [
        {
          name: '主图',
          url: data.picture
        }
      ]
    }
  } catch (error) {
    console.error('获取陪护介绍信息失败：', error)
    appMessage.error('获取陪护介绍信息失败')
  }
}

watch(uploadFileList, (val) => {
  formData.value.base64picture = val[0]?.url ?? ''
  if (formData.value.base64picture) {
    // 单独触发验证图片验证，去除可能存在的校验错误信息提示
    formRef.value!.validateField('base64picture')
  }
})

const handleConfirm = async () => {
  // 导出服务介绍
  const serverIntroduceHtml = serviceIntroduceEditorRef.value?.exportHtml()
  // 导出团队介绍
  const teamIntroduceHtml = teamIntroduceEditorRef.value?.exportHtml()

  formData.value.serverIntroduce = serverIntroduceHtml ?? ''
  formData.value.teamIntroduce = teamIntroduceHtml ?? ''

  await formRef.value!.validate(async (isValid) => {
    if (isValid) {
      try {
        console.log('保存陪护介绍信息：', formData.value)

        const res = await requestSaveInpatientCaregiverIntroduceInfo(formData.value)
        appMessage.success(res.msg || '保存成功')

        // 重新获取信息
        await fetchCaregiverIntroduceInfo()
      } catch (error) {
        console.error('保存陪护介绍信息失败：', error)
        appMessage.error('保存陪护介绍信息失败')
      }
    } else {
      console.warn('表单校验未通过：', formData.value)
    }
  })
}
</script>

<template>
  <div class="content-container">
    <div class="top-tips">温馨提示：编辑后请点击保存按钮进行保存，保存成功后将同步更新手机端，请注意内容规范</div>
    <el-form
      class="form-container"
      ref="formRef"
      size="large"
      label-width="140px"
      label-position="right"
      :rules="rules"
      :model="formData"
    >
      <el-row>
        <el-col :span="12">
          <el-form-item label="服务名称：" prop="serverName">
            <el-input v-model="formData.serverName" placeholder="请输入" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="咨询电话：" prop="phone">
            <el-input v-model="formData.phone" placeholder="请输入" maxlength="11" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item label="主图：" prop="base64picture">
        <CommonImgUpload
          v-model="uploadFileList"
          tips="建议200*200（1:1）jpg/png格式图片，且不超过200kb"
          :limit="1"
          :max-file-size="200"
          :file-type="['image/jpeg', 'image/png']"
          use-base64
        />
      </el-form-item>

      <el-form-item label="服务介绍：" prop="serverIntroduce">
        <CommonArticleEditor ref="serviceIntroduceEditorRef" v-model="formData.serverIntroduce" />
      </el-form-item>

      <el-form-item label="团队介绍：" prop="teamIntroduce">
        <CommonArticleEditor ref="teamIntroduceEditorRef" v-model="formData.teamIntroduce" />
      </el-form-item>
    </el-form>

    <el-button
      v-auth.disabled="'SaveInpatientCaregiverIntroduceInfo'"
      class="save-btn"
      type="primary"
      @click="handleConfirm"
      >确定保存
    </el-button>
  </div>
</template>

<style lang="scss" scoped>
.content-container {
  padding: 30px 20px;
}

:deep(.el-form .el-form-item) {
  align-items: flex-start !important;
}

.top-tips {
  padding: 10px 16px;
  border-radius: 4px;
  background: #ecf5ff;
  border: 1px solid #d9ecff;
  font-size: 14px;
  text-align: left;
  color: #1890ef;
  margin-bottom: 30px;
}

.form-container {
  padding-right: 60px;
}

.save-btn {
  margin-left: 140px;
}
</style>
