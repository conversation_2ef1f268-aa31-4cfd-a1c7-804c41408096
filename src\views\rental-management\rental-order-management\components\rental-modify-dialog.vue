<script setup lang="ts">
import { useFormHook } from '@/hooks/useForm.ts'
import { requestModifyReviewInfo } from '@/api/rental-management.api.ts'
import { FormRules } from 'element-plus'
import { ResModifyReviewInfo } from '@/api/dto/rental-management/rental-order-management.dto.ts'
import { maskIdCard } from '@/utils/index.ts'

const props = defineProps<{
  inpatientCaregiverId: string
  confirmCallback: (
    inpatientCaregiverUpdateId: string,
    inpatientCaregiverId: string,
    reviewOperate: boolean
  ) => Promise<void>
}>()

const { formRef } = useFormHook()

const formData = ref<{
  reviewOperate: boolean | undefined
}>({
  reviewOperate: undefined
})

// 修改审核信息
const modifyReviewInfo = ref<ResModifyReviewInfo>()

const visible = defineModel({ default: false })

const rules = reactive<FormRules>({
  reviewOperate: [{ required: true, message: '请选择审核结果', trigger: 'change' }]
})

const auditActionOptions = [
  {
    label: '审核通过',
    value: true
  },
  {
    label: '审核不通过',
    value: false
  }
]

watch(
  visible,
  (v) => {
    if (v) {
      // 重置表单，避免在点击编辑后再点击新增时候，因为弹窗组件没有销毁导致数据不清空
      formRef.value?.resetFields()
      modifyReviewInfo.value = undefined
      setTimeout(() => {
        getModifyReviewInfo()
      })
    }
  },
  {
    immediate: true
  }
)

// 获取修改审核信息
const getModifyReviewInfo = async () => {
  try {
    const res = await requestModifyReviewInfo({ inpatientCaregiverId: props.inpatientCaregiverId })
    modifyReviewInfo.value = res
  } catch (error) {
    console.error('获取修改审核信息失败', error)
  }
}

const handleConfirm = async (done: (keepVisible: boolean) => void) => {
  await formRef.value!.validate(async (valid) => {
    if (valid) {
      if (!modifyReviewInfo.value?.inpatientCaregiverUpdateId) {
        ElMessage.error('修改审核信息 ID 不存在，请稍后重试')
        done(true)
        return
      }

      try {
        await props.confirmCallback(
          modifyReviewInfo.value?.inpatientCaregiverUpdateId,
          props.inpatientCaregiverId,
          formData.value.reviewOperate!
        )
        done(false)
        setTimeout(() => {
          formRef.value!.resetFields()
        })
      } catch (error) {
        // 结束按钮 loading，但是不关闭弹窗
        done(true)
      }
    } else {
      console.log('表单数据验证失败:', formRef.value!, modifyReviewInfo.value)
      done(true)
    }
  })
}
</script>

<template>
  <CommonFormDialog
    v-model="visible"
    title="修改审核"
    width="640px"
    showLoading
    :showCancelTips="false"
    @confirm="handleConfirm"
  >
    <template v-if="modifyReviewInfo">
      <div class="patient-info">
        住院人姓名：{{ modifyReviewInfo?.patientName }}丨证件号：{{ maskIdCard(modifyReviewInfo?.patientIdNo) }}
      </div>

      <el-form ref="formRef" :model="formData" :rules="rules" label-width="auto" label-position="right" size="large">
        <el-row>
          <el-col :span="24">
            <el-form-item label="审核操作：" prop="reviewOperate">
              <el-radio-group v-model="formData.reviewOperate">
                <el-radio
                  v-for="(item, index) in auditActionOptions"
                  :key="index"
                  :label="item.label"
                  :value="item.value"
                />
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>

      <el-descriptions title="原租赁信息" :column="2">
        <el-descriptions-item label="服务类型：">{{ modifyReviewInfo?.originalServerType }}</el-descriptions-item>
        <el-descriptions-item label="价格：">{{ modifyReviewInfo?.originalPrice }}元/天</el-descriptions-item>
        <el-descriptions-item label="租赁时间：">{{ modifyReviewInfo?.originalLeaseTime }}</el-descriptions-item>
        <el-descriptions-item label="支付金额：" class-name="payment-amount"
          >{{ modifyReviewInfo?.originalPayFee }}元</el-descriptions-item
        >
      </el-descriptions>

      <el-divider border-style="dashed" />

      <el-descriptions title="新租赁信息" :column="2">
        <el-descriptions-item label="服务类型：">{{ modifyReviewInfo?.newServerType }}</el-descriptions-item>
        <el-descriptions-item label="价格：">{{ modifyReviewInfo?.newPrice }}元/天</el-descriptions-item>
        <el-descriptions-item label="租赁时间：">{{ modifyReviewInfo?.newLeaseTime }}</el-descriptions-item>
        <el-descriptions-item v-if="modifyReviewInfo?.pendingPayment" label="补缴金额：" class-name="payment-amount"
          >{{ modifyReviewInfo?.pendingPayment }}元</el-descriptions-item
        >
        <el-descriptions-item v-if="modifyReviewInfo?.pendingRefund" label="退费金额：" class-name="payment-amount"
          >{{ modifyReviewInfo?.pendingRefund }}元</el-descriptions-item
        >
      </el-descriptions>
    </template>
  </CommonFormDialog>
</template>

<style lang="scss" scoped>
.patient-info {
  padding: 10px 16px;
  border-radius: 4px;
  background: #ecf5ff;
  border: 1px solid #d9ecff;
  font-size: 14px;
  color: #1890ef;
  margin-bottom: 20px;
}

:deep(.el-descriptions__header) {
  margin-bottom: 0 !important;
}

:deep(.el-descriptions__title) {
  font-size: 14px;
  font-weight: 700;
  color: #303133;
}

:deep(.el-descriptions__label) {
  font-size: 14px;
  color: #606266 !important;
  margin-right: 0;
}

:deep(.el-descriptions__content) {
  font-size: 14px;
  color: #606266 !important;
}

:deep(.el-descriptions__cell) {
  padding: 0 !important;
}

:deep(.payment-amount) {
  color: #e6a23c !important;
}
</style>
