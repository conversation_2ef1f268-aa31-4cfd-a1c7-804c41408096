<script setup lang="ts">
import {
  AuditStatus,
  AuditStatusConfig,
  ChangeEscortInfo
} from '@/api/dto/rental-management/rental-order-management.dto.ts'

defineProps({
  changeEscortInfo: {
    type: Array<ChangeEscortInfo>,
    required: true,
    default: () => []
  }
})

defineEmits<{
  (
    e: 'replacement',
    data: {
      orderItem: ChangeEscortInfo
    }
  ): void
}>()
</script>

<template>
  <div>
    <div class="common-panel-title">
      <span>换人申请</span>
    </div>
    <BaseTable :data="changeEscortInfo" border width="100%" max-height="300">
      <el-table-column prop="substitutionReason" label="换人原因" width="630" />
      <el-table-column prop="submitTime" label="提交时间" width="240" />
      <el-table-column prop="reviewStatus" label="状态" width="240">
        <template #default="scope">
          <el-tag :type="AuditStatusConfig[scope.row.reviewStatus]?.tagType || AuditStatusConfig.default.tagType">
            {{ AuditStatusConfig[scope.row.reviewStatus]?.label || AuditStatusConfig.default.label }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="reviewTime" label="审核时间" width="240">
        <template #default="scope">
          {{ scope.row.reviewTime || '-' }}
        </template>
      </el-table-column>

      <el-table-column label="操作" fixed="right" min-width="280">
        <template #default="scope">
          <el-button
            v-if="scope.row.reviewStatus === AuditStatus.PENDING"
            v-auth.disabled="'PersonnelReplacementReview'"
            size="small"
            type="warning"
            @click="$emit('replacement', scope.row)"
            >换人审核
          </el-button>
        </template>
      </el-table-column>
    </BaseTable>
  </div>
</template>

<style scoped lang="scss"></style>
