<script lang="ts" setup>
import Collapse from './layout-header-collapse.vue'
import Breadcrumb from './layout-header-breadcrumb.vue'
import SearchNav from './layout-header-search.vue'
import Screenfull from './layout-header-screenfull.vue'
import User from './layout-header-user.vue'
import PrinterSetting from './layout-header-printer-setting.vue'
import PendingAdmissionNotification from './pending-admission-notification.vue'
</script>

<template>
  <ul class="layout_header-container">
    <li class="layout_header-item">
      <Collapse />

      <Breadcrumb />
    </li>

    <li class="layout_header-item">
      <SearchNav />

      <Screenfull />

      <PrinterSetting />

      <PendingAdmissionNotification />

      <User />
    </li>
  </ul>
</template>

<style lang="scss" scoped>
.layout_header-container {
  position: relative;
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  height: $layoutHeaderHeight;
  padding: 0 20px;
  background-color: var(--el-bg-color-overlay);
  box-shadow: 0 1px 4px var(--el-border-color-light);
  overflow: hidden;

  .layout_header-item {
    display: flex;
    align-items: center;
    & > * {
      margin-right: 16px;
      &:last-child {
        margin-right: 0;
      }
    }
  }
}
</style>
