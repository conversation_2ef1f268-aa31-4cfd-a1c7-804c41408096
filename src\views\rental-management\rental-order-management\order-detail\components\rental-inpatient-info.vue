<script setup lang="ts">
import {
  AuditStatus,
  RentalOrderStatus,
  ResRentalOrderDetail
} from '@/api/dto/rental-management/rental-order-management.dto.ts'
import { maskIdCard } from '@/utils/index.ts'

defineEmits<{
  (e: 'confirm'): void
  (e: 'finished'): void
  (e: 'cancel'): void
  (e: 'showPaymentInfo'): void
}>()

defineProps({
  detailInfo: {
    type: Object as PropType<ResRentalOrderDetail>,
    required: true,
    default: () => ({})
  }
})
</script>

<template>
  <div>
    <div class="panel-header">
      <span class="common-panel-title">住院信息</span>
      <div>
        <!-- 因为只有修改订单，审核通过之后，才会有补缴或者退款的信息，才显示补缴支付信息按钮 -->
        <!-- 所以这里判断如果修改信息列表里，有审核通过的，就显示补缴支付信息按钮 -->
        <el-button
          v-if="
            detailInfo.orderUpdateInfoDTOList?.length &&
            detailInfo.orderUpdateInfoDTOList.some((item) => item.reviewStatus === AuditStatus.APPROVED)
          "
          v-auth.disabled="'SupplementaryPaymentInformation'"
          class="payment-info-btn"
          size="large"
          @click="$emit('showPaymentInfo')"
          >补缴支付信息
        </el-button>
        <el-button
          v-if="detailInfo.orderStatus === RentalOrderStatus.RESERVED && !detailInfo.pendingRefund"
          v-auth.disabled="'InpatientCaregiverConfirmLease'"
          type="success"
          size="large"
          @click="$emit('confirm')"
          >确定租赁</el-button
        >
        <el-button
          v-if="detailInfo.orderStatus === RentalOrderStatus.ESCORTING"
          v-auth.disabled="'InpatientCaregiverServiceCompleted'"
          type="primary"
          size="large"
          @click="$emit('finished')"
          >服务完成</el-button
        >
        <el-button
          v-if="detailInfo.orderStatus === RentalOrderStatus.AWAITING_PAYMENT"
          v-auth.disabled="'InpatientCaregiverCancelOrder'"
          type="warning"
          size="large"
          @click="$emit('cancel')"
          >取消订单</el-button
        >
      </div>
    </div>
    <el-descriptions border :column="2">
      <el-descriptions-item label="住院人姓名">{{ detailInfo.patientName }}</el-descriptions-item>
      <el-descriptions-item label="证件号">{{ maskIdCard(detailInfo.patientIdNo) }}</el-descriptions-item>
      <el-descriptions-item label="住院科室">{{ detailInfo.department }}</el-descriptions-item>
      <!-- <el-descriptions-item label="主治医生">{{ detailInfo.doctorName }}</el-descriptions-item>
      <el-descriptions-item label="住院床号">{{ detailInfo.bedNo }}</el-descriptions-item> -->
      <el-descriptions-item label="入院时间">{{ detailInfo.indate }}</el-descriptions-item>
    </el-descriptions>
  </div>
</template>

<style scoped lang="scss">
.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.payment-info-btn {
  background-color: #00b798;
  color: white;
}
</style>
