import { ROUTER_PATH } from '../router-path'
import type { RouteRecordRaw } from 'vue-router'

const Layout = () => import('@/layout')

export const declarationDataManagement: RouteRecordRaw = {
  path: ROUTER_PATH.DECLARATION_DATA_MANAGEMENT,
  name: 'DeclarationDataManagement',
  component: Layout,
  redirect: ROUTER_PATH.DECLARATION_DATA_LIST,
  meta: { title: '申报资料管理', icon: 'el-icon-edit' },
  children: [
    // {
    //   path: ROUTER_PATH.DECLARATION_DATA_LIST,
    //   name: 'DeclarationDataList',
    //   component: () => import('@/views/declaration-data-management/list'),
    //   meta: {
    //     title: '申报资料信息',
    //     leaveOff: false
    //   }
    // }
  ]
}
