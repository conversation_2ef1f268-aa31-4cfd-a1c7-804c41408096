<script setup lang="ts">
import { FormInstance, FormRules } from 'element-plus'
import { useMountDialog } from '@/hooks/useMountDialog.ts'
import InvoiceListDialog from './invoice-list-dialog.vue'

const { open: openInvoiceListDialog } = useMountDialog(InvoiceListDialog)

/**
 * 发票查询 弹窗
 */

interface RuleForm {
  // 发票号
  invoiceNo: string
}

const props = defineProps<{
  confirmCallback: (invoiceNo: string) => Promise<void>
}>()

const inputFormRef = ref<FormInstance>()

const inputForm = reactive({
  invoiceNo: ''
})

const dialogVisible = defineModel({ default: false })

const inputFormRules = reactive<FormRules<RuleForm>>({
  invoiceNo: [{ required: true, message: '请输入发票号', trigger: 'blur' }]
})

// 弹窗关闭时，重置表单
watch(dialogVisible, (val) => {
  if (val) {
    inputFormRef.value?.resetFields()
  }
})

// 弹窗 - 点击确认
async function handleConfirm(done: (flag?: boolean) => void, formEl: FormInstance | undefined) {
  if (!formEl) return
  await formEl.validate(async (valid, fields) => {
    if (valid) {
      try {
        // 提交
        await props.confirmCallback(inputForm.invoiceNo)
        done(false)
        setTimeout(() => {
          formEl.resetFields()
        })
      } catch (e) {
        done(true)
      }
    } else {
      console.log('提交 弹窗 表单 失败 表单校验不通过：', fields)
      done(true)
    }
  })
}

// 弹窗 - 点击选择发票号
function handleSelectInvoiceNo() {
  console.log('选择发票号')
  openInvoiceListDialog({
    confirmCallback: async (invoiceNo: string) => {
      console.log('选择发票号', invoiceNo)
      inputForm.invoiceNo = invoiceNo
    }
  })
}
</script>

<template>
  <CommonFormDialog
    v-model="dialogVisible"
    title="发票查询"
    width="454px"
    show-loading
    :disabled-confirm="!inputForm.invoiceNo"
    @confirm="handleConfirm($event, inputFormRef)"
  >
    <el-form ref="inputFormRef" size="large" :model="inputForm" :rules="inputFormRules" hide-required-asterisk>
      <el-form-item label="发票号：" prop="invoiceNo">
        <el-input v-model="inputForm.invoiceNo" class="input-box" clearable placeholder="请输入" />
        <div class="more-select" @click="handleSelectInvoiceNo">···</div>
      </el-form-item>
    </el-form>
  </CommonFormDialog>
</template>

<style scoped lang="scss">
.input-box {
  width: 300px;
}

.more-select {
  width: 40px;
  height: 40px;
  border-radius: 4px;
  background: #fff;
  border: 1px solid #1890ff;
  font-weight: 500;
  font-size: 16px;
  line-height: 40px;
  text-align: center;
  color: #1890ff;
  margin-left: 16px;
}
</style>
