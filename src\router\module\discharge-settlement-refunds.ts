import { ROUTER_PATH } from '@/router/router-path.ts'
import type { RouteRecordRaw } from 'vue-router'

const Layout = () => import('@/layout')

export const dischargeSettlementRefundsRouter: RouteRecordRaw = {
  path: ROUTER_PATH.DISCHARGE_SETTLEMENT_REFUNDS,
  name: 'DischargeSettlementRefunds',
  redirect: ROUTER_PATH.BANK_CARD_REFUND_RECORDS_LIST,
  component: Layout,
  meta: { title: '出院结算退费', icon: 'el-icon-money' },
  children: [
    // {
    //   path: ROUTER_PATH.BANK_CARD_REFUND_RECORDS_LIST,
    //   name: 'BankCardRefundRecordsList',
    //   component: () => import('@/views/discharge-settlement-refunds/bank-card-refund-records/list'),
    //   meta: { title: '银行卡退费记录' }
    // },
    {
      path: ROUTER_PATH.BANK_CARD_REFUND_RECORDS_DETAIL,
      name: 'BankCardRefundRecordsDetail',
      component: () => import('@/views/discharge-settlement-refunds/bank-card-refund-records/detail'),
      hidden: true,
      meta: {
        title: '银行卡退费记录详情',
        leaveOff: true,
        activeMenu: ROUTER_PATH.BANK_CARD_REFUND_RECORDS_LIST,
        noCache: true,
        permissionFrom: ROUTER_PATH.BANK_CARD_REFUND_RECORDS_LIST,
        useTab: false
      }
    }
  ]
}
