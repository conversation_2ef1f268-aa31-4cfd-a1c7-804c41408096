<script setup lang="ts">
/* 病人账单基本信息 */
import type { PatientAccountsBaseInfo } from '@/api/dto/discharge-settlement/patient-accounts.dto'
import PatientInfoItem from './patient-info-item.vue'

defineProps<{
  baseInfo: PatientAccountsBaseInfo
  showInvoiceAdjustments?: boolean
}>()

const emits = defineEmits<{
  (e: 'viewPatientInfo'): void
  (e: 'switchPatient'): void
  (e: 'invoiceAdjustments'): void
}>()

/**
 * 病人资料
 */
const handleViewPatientInfo = () => {
  emits('viewPatientInfo')
}

/**
 * 切换病人
 */
const handleSwitchPatient = () => {
  emits('switchPatient')
}

/**
 * 发票查询
 */
const handleInvoiceAdjustments = () => {
  emits('invoiceAdjustments')
}
</script>

<template>
  <div class="container">
    <div class="header">
      <div class="common-panel-title">基本信息</div>
      <div class="operation-container">
        <el-button v-if="showInvoiceAdjustments" type="warning" size="large" @click="handleInvoiceAdjustments"
          >发票查询</el-button
        >
        <el-button type="primary" size="large" @click="handleViewPatientInfo">病人资料</el-button>
        <el-button type="success" size="large" @click="handleSwitchPatient">切换病人</el-button>
      </div>
    </div>

    <div class="content">
      <div class="form-row row-layout-1">
        <PatientInfoItem label="住院号" :value="baseInfo.patno" />
        <PatientInfoItem label="记账号" :value="baseInfo.inpno" />
        <PatientInfoItem emphasize label="姓名" :value="baseInfo.name" />
        <PatientInfoItem label="性别" :value="baseInfo.sex === '1' ? '男' : baseInfo.sex === '2' ? '女' : ''" />
        <PatientInfoItem label="年龄" :value="baseInfo.age" />
        <PatientInfoItem label="自付" :value="baseInfo.rate" />
        <PatientInfoItem emphasize label="结算" :value="baseInfo.setmeth" />
        <PatientInfoItem label="统筹" :value="baseInfo.overallPlanning" />
        <PatientInfoItem label="医保" :value="baseInfo.insutype" />
      </div>

      <div class="form-row row-layout-2">
        <PatientInfoItem label="病区" :value="baseInfo.curdptnm" />
        <PatientInfoItem label="次数" :value="baseInfo.numberOfTimes" />
        <PatientInfoItem label="床位" :value="baseInfo.bedno" />
        <PatientInfoItem label="价类" :value="baseInfo.pricetype" />
        <PatientInfoItem label="总天数" :value="baseInfo.totalDays" />
        <PatientInfoItem label="未接天数" :value="baseInfo.numberOfMissedDays" />
        <PatientInfoItem label="诊断" :value="baseInfo.diagnosis" />
      </div>

      <div class="form-row row-layout-3">
        <PatientInfoItem label="入院日期" :value="baseInfo.indate" />
        <PatientInfoItem label="出院日期" :value="baseInfo.outdate" />
        <PatientInfoItem label="已清日期" :value="baseInfo.clearedDate" />
        <PatientInfoItem label="结算日期" :value="baseInfo.settlementDate" />
        <PatientInfoItem label="特清自费" :value="baseInfo.teqingSelfFunded" />
      </div>

      <div class="form-row row-layout-4">
        <PatientInfoItem label="总费用" :value="baseInfo.sumpay" />
        <PatientInfoItem label="待结" :value="baseInfo.pendingSettlement" />
        <PatientInfoItem label="直结交费" :value="baseInfo.directPayment" />
        <PatientInfoItem label="已清" :value="baseInfo.payment" />
        <PatientInfoItem label="待清" :value="baseInfo.unpayment" />
        <PatientInfoItem label="自费百分比" :value="baseInfo.selfFundedPercentage" />
      </div>

      <div class="form-row row-layout-5">
        <PatientInfoItem label="预付费" :value="baseInfo.prepay" />
        <PatientInfoItem emphasize label="欠款" :value="baseInfo.debt" />
        <PatientInfoItem label="清账日期" :value="baseInfo.clearingDate" />
        <PatientInfoItem emphasize label="提示" :value="baseInfo.prompt" />
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.container {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;

  .operation-container {
    display: flex;
    gap: 12px;
  }
}

.content {
  .form-row {
    display: grid;
    gap: 0 20px;
    align-items: center;
    margin-bottom: 10px;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .row-layout-1 {
    grid-template-columns: 216fr 216fr 162fr 102fr 102fr 102fr 172fr 172fr 172fr;
  }

  .row-layout-2 {
    grid-template-columns: 272fr 152fr 152fr 152fr 166fr 180fr 386fr;
  }

  .row-layout-3 {
    grid-template-columns: repeat(5, 300fr);
  }

  .row-layout-4 {
    grid-template-columns: 246fr 232fr 260fr 232fr 232fr 274fr;
  }

  .row-layout-5 {
    grid-template-columns: 246fr 232fr 260fr 780fr;
  }
}
</style>
