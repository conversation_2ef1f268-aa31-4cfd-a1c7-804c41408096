<script lang="ts" setup>
const modelValue = defineModel<string>({ required: true })

const emit = defineEmits<{
  search: []
}>()

const handleInput = (val: string) => {
  modelValue.value = val
}

const handleSearch = () => {
  emit('search')
}
</script>

<template>
  <div class="search_bar-container flex mt-10 mb-10">
    <el-input
      :model-value="modelValue"
      placeholder="输入搜索关键字"
      style="width: 220px"
      @keydown.enter="handleSearch"
      @input="handleInput"
    />
    <el-button type="primary" class="ml-8" @click="handleSearch">搜索</el-button>
  </div>
</template>
