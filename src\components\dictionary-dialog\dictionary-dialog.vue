<script lang="ts" setup>
import type {
  GetSysDictionaryInfoForUpdateInput,
  SysDictionaryDirModel,
  AddSysDictionaryInfoInput,
} from '@/api/dto/system/dictionary.dto'
import {
  addSysDictionaryInfoApi,
  getSysDictionaryInfoForShowApi,
  getSysDictionaryInfoForUpdateApi,
  updateSysDictionaryInfoApi,
} from '@/api/system/dictionary.api'
import ElConfig from '@/components/el-config'
import { useElDialogHook } from '@/hooks/useDialog'
import { useFormHook } from '@/hooks/useForm'
import { useLoadingHook } from '@/hooks/useLoading'
import { appMessage } from '@/hooks/useNaiveApi'
import type { Tree } from '@/types/tree'
import { formatTreeData } from '@/utils'
import type { CascaderOption, DialogBeforeCloseFn, FormRules } from 'element-plus'

export interface OpenParams extends GetSysDictionaryInfoForUpdateInput {}
type FormData = Omit<AddSysDictionaryInfoInput, 'parentId'>

const { dialogVisible, __open, __close, promiseFunc } = useElDialogHook<boolean>()

const { formRef, validate, resetForm } = useFormHook()

const { loading, loadingFunc } = useLoadingHook()

const cascaderProps = { checkStrictly: true }

const formData = reactive<FormData & { parentId: number[] }>({
  description: '',
  dictionaryInfoName: '',
  enCode: '',
  enabledMark: true,
  isSys: false,
  parentId: [0],
  sortCode: 1,
})
/**表单验证规则 */
const rules = reactive<FormRules<FormData>>({
  dictionaryInfoName: [{ required: true, message: '请输入字典名字', trigger: 'blur' }],
  enCode: [{ required: true, message: '请输入字典编码', trigger: 'blur' }],
  enabledMark: [{ required: true, message: '请选择是否可以用', trigger: 'blur' }],
  isSys: [{ required: true, message: '请选择是否为系统字段', trigger: 'blur' }],
})

const getSysDictionaryInfoForUpdate = async () => {
  try {
    const result = await getSysDictionaryInfoForUpdateApi({ dictionaryInfoId: dictionaryInfoId.value })
    formData.dictionaryInfoName = result.dictionaryInfoName
    formData.enCode = result.enCode
    formData.enabledMark = result.enabledMark
    formData.isSys = result.isSys
    formData.parentId = [result.parentId]
    formData.sortCode = result.sortCode
  } catch (error) {
    resetForm()
    __close()
    promiseFunc.reject?.('获取字典详情失败，请稍后重试')
  }
}

/**字典列表 */
const sysDictionaryList = ref<CascaderOption[]>([])
const handleTreeData = (tree: Tree<SysDictionaryDirModel>[]): CascaderOption[] => {
  return tree.map((item) => {
    const target: CascaderOption = {
      label: item.extData.dictionaryInfoName,
      value: item.extData.mainId,
    }

    if (item.children) {
      target.children = handleTreeData(item.children)
    }

    return target
  })
}
/**获取字典列表 */
const getSysDictionaryInfoForShow = async () => {
  try {
    const reslut = await getSysDictionaryInfoForShowApi({ loading: false })
    const data = formatTreeData<SysDictionaryDirModel>(reslut, 'dictionaryInfoName', 'dictionaryInfoId')
    sysDictionaryList.value = handleTreeData(data)
    sysDictionaryList.value.unshift({
      label: '顶级',
      value: 0,
    })
  } catch (error) {
    sysDictionaryList.value = []
  }
}
/**下拉框显示的时候获取字典列表 */
const handleSelectVisibleChange = (flag: boolean) => {
  flag && !sysDictionaryList.value.length && getSysDictionaryInfoForShow()
}

/**修改字典信息 */
const updateSysDictionaryInfo = async () => {
  await updateSysDictionaryInfoApi(
    {
      ...formData,
      parentId: formData.parentId[formData.parentId.length - 1],
      dictionaryInfoId: dictionaryInfoId.value,
    },
    { loading: loadingFunc }
  )
}

/**新增字典 */
const addSysDictionaryInfo = async () => {
  await addSysDictionaryInfoApi(
    { ...formData, parentId: formData.parentId[formData.parentId.length - 1] },
    { loading: loadingFunc }
  )
}

/**确认按钮 */
const confrim = async () => {
  try {
    await validate()

    if (dictionaryInfoId.value) {
      // 修改
      await updateSysDictionaryInfo()
      appMessage.success('修改成功')
    } else {
      // 新增
      await addSysDictionaryInfo()
      appMessage.success('新增成功')
    }
    promiseFunc.resolve?.(true)
    __close()
  } catch (error) {}
}

const handleBeforeClose: DialogBeforeCloseFn = async (done) => {
  try {
    await validate()
    if (loading.value) {
      appMessage.warning('正在提交中，请稍后')
      return
    }
    done()
  } catch (error) {}
}

/**字典ID */
const dictionaryInfoId = ref('')

const handleOpenDialog = async (openParams: OpenParams) => {
  setTimeout(() => {
    if (openParams.dictionaryInfoId) {
      dictionaryInfoId.value = openParams.dictionaryInfoId
      getSysDictionaryInfoForUpdate()
    }
  })

  getSysDictionaryInfoForShow()

  return await __open()
}

const handleCloseDialog = () => {
  resetForm()
  dictionaryInfoId.value = ''
  __close()
  promiseFunc.reject?.('取消')
}

const handleClosed = () => {
  resetForm()
  dictionaryInfoId.value = ''
}

defineExpose({
  __open: handleOpenDialog,
  __close: handleCloseDialog,
})
</script>

<template>
  <ElConfig>
    <el-dialog
      v-model="dialogVisible"
      :before-close="handleBeforeClose"
      width="420"
      class="dictionary_dialog-container"
      @closed="handleClosed"
    >
      <template #header>
        <div class="title">{{ dictionaryInfoId ? '修改字典' : '新增字典' }}</div>
      </template>

      <el-form ref="formRef" :model="formData" :rules="rules" label-width="120">
        <el-form-item prop="parentId" label="上级名称：">
          <el-cascader
            v-model="formData.parentId"
            :options="sysDictionaryList"
            :props="cascaderProps"
            separator=">"
            style="width: 100%"
            @visible-change="handleSelectVisibleChange"
          />
        </el-form-item>

        <el-form-item prop="dictionaryInfoName" label="字典名称：">
          <el-input v-model="formData.dictionaryInfoName"></el-input>
        </el-form-item>

        <el-form-item prop="enCode" label="编号标识：">
          <el-input v-model="formData.enCode"></el-input>
        </el-form-item>

        <el-form-item prop="sortCode" label="排序：">
          <el-input-number v-model="formData.sortCode" :min="0"></el-input-number>
        </el-form-item>

        <el-form-item label="是否启用：" prop="enabledMark">
          <el-switch v-model="formData.enabledMark" active-text="启用后才能显示"> </el-switch>
        </el-form-item>

        <el-form-item label="系统管理：" prop="isSys">
          <el-switch v-model="formData.isSys" active-text="开启后无法删除"> </el-switch>
        </el-form-item>

        <el-form-item prop="description" label="备注：">
          <el-input v-model="formData.description" type="textarea" placeholder="请输入备注"></el-input>
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button :disabled="loading" @click="handleCloseDialog">取 消</el-button>
        <el-button :loading="loading" type="primary" @click="confrim">确 定</el-button>
      </template>
    </el-dialog>
  </ElConfig>
</template>

<style lang="scss" scoped>
.dictionary_dialog-container {
  .title {
    height: 44px;
    padding: 10px 16px;
    font-size: 18px;
    color: var(--el-text-color-regular);
  }
}
</style>
