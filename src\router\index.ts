import { declarationDataManagement } from '@/router/module/declaration-data-management.ts'
import { depositManagementRouter } from '@/router/module/deposit-management.ts'
import { escortManagementRouter } from '@/router/module/escort-application.ts'
import { insuranceRegistrationRouter } from '@/router/module/insurance-registration.ts'
import { rentalManagementRouter } from '@/router/module/rental-management.ts'

import Login from '@/views/login'
import Refresh from '@/views/refresh'
import { createRouter, createWebHashHistory, type RouteRecordRaw } from 'vue-router'
import { inpatientRegistrationRouter } from './module/inpatient-registration'
import { dischargeSettlementRouter } from './module/discharge-settlement'
import { systemRouter } from './module/system'
import { ROUTER_PATH } from './router-path'
import { dischargeSettlementRefundsRouter } from './module/discharge-settlement-refunds'


const Layout = () => import('@/layout')

//公共路由
export const constantRoutes: RouteRecordRaw[] = [
  {
    path: ROUTER_PATH.LOGIN,
    name: Symbol('Login'),
    component: Login,
    hidden: true,
    meta: { title: '登录', useTab: false }
  },

  {
    path: ROUTER_PATH.ROOT,
    name: Symbol('Root'),
    redirect: ROUTER_PATH.HOME,
    component: Layout,
    meta: { title: '首页', icon: 'el-icon-home-filled' },
    children: [
      // {
      //   path: ROUTER_PATH.HOME,
      //   name: Symbol('Home'),
      //   component: () => import('@/views/home'),
      //   meta: { title: '平台总览', affix: true }
      // },
      {
        path: ROUTER_PATH.UNAUTHORIZED,
        name: Symbol('401'),
        component: () => import('@/views/exception/401.vue'),
        hidden: true,
        meta: { title: '无权限', useTab: false }
      }
    ]
  },

  systemRouter,

  {
    path: ROUTER_PATH.REFRESH,
    name: Symbol('Refresh'),
    component: Layout,
    hidden: true,
    children: [
      {
        path: `${ROUTER_PATH.REFRESH}/:path(.*)`,
        name: Symbol('RefreshPage'),
        component: Refresh,
        hidden: true,
        meta: { useTab: false }
      }
    ],
    meta: { useTab: false }
  },

  {
    path: ROUTER_PATH.SERVER_ERROR,
    name: Symbol('500'),
    component: () => import('@/views/exception/500.vue'),
    hidden: true,
    meta: { title: '服务器异常', useTab: false, useBreadcrumb: false }
  },
  inpatientRegistrationRouter,
  depositManagementRouter,
  insuranceRegistrationRouter,
  escortManagementRouter,
  rentalManagementRouter,
  declarationDataManagement,
  dischargeSettlementRouter,
  dischargeSettlementRefundsRouter
  // publicEdArticleManagement
]

//动态路由
export const asyncRoutes = [
  // 全局404页面，不能定义name，否则会导致刷新动态路由的时候出现404
  {
    path: '/:catchAll(.*)*',
    component: () => import('@/views/exception/404.vue'),
    hidden: true,
    meta: { useTab: false, useBreadcrumb: false }
  }
]

const router = createRouter({
  history: createWebHashHistory(),
  routes: constantRoutes
})

export default router
