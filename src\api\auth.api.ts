import { request } from '@/utils/axios-utils'
import type { ApiFunc, AxiosRequestConfig } from 'axios'
import type {
  LoginInput,
  LoginOutput,
  RefreshAuthTokenInput,
  RefreshAuthTokenOutput,
  LogoutInput,
  UpdatePasswordInput,
} from './dto/auth.dto'

const authRequest = (options: AxiosRequestConfig) => {
  return request({
    checkAuthToken: false,
    ...options,
    url: `/foying/web/admin/Login/${options.url}`,
  })
}

/**获取登录验证码 */
export const getVerifyCodeApi: ApiFunc<undefined, string> = (options) => {
  return authRequest({ url: 'captchaImage', method: 'get', responseType: 'arraybuffer', ...options })
}

/**登录 */
export const loginApi: ApiFunc<LoginInput, LoginOutput> = (data, options) => {
  return authRequest({ url: 'login', data, ...options })
}

/**刷新token */
export const refreshTokenApi: ApiFunc<RefreshAuthTokenInput, RefreshAuthTokenOutput> = (data, options) => {
  return authRequest({ url: 'refreshAuthToken', data, ...options })
}

/**注销登录 */
export const logoutApi: ApiFunc<LogoutInput, undefined> = (data, options) => {
  return authRequest({ url: 'logout', data, ...options, checkAuthToken: true })
}

/**修改密码 */
export const updatePasswordApi: ApiFunc<UpdatePasswordInput, undefined> = (data, options) => {
  return request({ url: '/foying/web/admin/SysManager/updatePassword', data, ...options })
}
