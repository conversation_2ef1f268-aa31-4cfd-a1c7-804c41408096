<script setup lang="ts">
import type { SelectableWard } from '@/api/dto/discharge-settlement/patient-accounts.dto'
import { requestSettlementDepartmentList } from '@/api/discharge-settlement.api'

/**
 * 病区选择弹窗
 */

const props = defineProps<{
  // 默认选中的病区
  defaultWardCode?: string
  // 确认回调
  confirmCallback: (data: any) => Promise<void>
}>()

const dialogVisible = defineModel({ default: false })

// 选择的病区
const selectedWard = ref<SelectableWard>()

// 病区列表
const wardList = ref<SelectableWard[]>([])

watch(
  dialogVisible,
  async (val) => {
    if (val) {
      await getWardList()
      if (props.defaultWardCode) {
        selectedWard.value = wardList.value.find((ward) => ward.deptcode === props.defaultWardCode)
      }
    }
  },
  { immediate: true }
)

// 获取病区列表
async function getWardList() {
  const res = await requestSettlementDepartmentList()
  wardList.value = res
  console.log('病区列表:', wardList.value)
}

// 弹窗 - 点击确认
async function handleConfirm(done: (flag?: boolean) => void) {
  if (!selectedWard.value) return
  await props.confirmCallback(JSON.parse(JSON.stringify(selectedWard.value)))
  done()
}

// 双击病区
async function handleDoubleClick(ward: SelectableWard) {
  selectedWard.value = ward
  await props.confirmCallback(JSON.parse(JSON.stringify(selectedWard.value)))
  dialogVisible.value = false
}
</script>

<template>
  <CommonFormDialog
    v-model="dialogVisible"
    title="选择病区"
    width="766px"
    show-loading
    :disabled-confirm="!selectedWard"
    @confirm="handleConfirm"
  >
    <div class="ward-grid">
      <div class="ward-grid-body ward-grid-header">
        <div class="ward-item">
          <div class="header-cell">病区代码</div>
          <div class="header-cell">病区名称</div>
        </div>
        <div class="ward-item">
          <div class="header-cell">病区代码</div>
          <div class="header-cell">病区名称</div>
        </div>
      </div>
      <div class="ward-grid-body">
        <template v-for="ward in wardList" :key="ward.wardCode">
          <div
            class="ward-item"
            :class="{ selected: selectedWard && selectedWard.deptcode === ward.deptcode }"
            @click="selectedWard = ward"
            @dblclick="handleDoubleClick(ward)"
          >
            <div class="code-cell">{{ ward.deptcode }}</div>
            <div class="name-cell">{{ ward.deptname }}</div>
          </div>
        </template>
      </div>
      <!-- 空数据 -->
      <div v-if="wardList.length === 0" class="empty-body">
        <div class="empty-text">暂无数据</div>
      </div>
    </div>
  </CommonFormDialog>
</template>

<style scoped lang="scss">
.ward-grid {
  border: 1px solid #ebeef5;
  border-radius: 4px;
  overflow-x: hidden;
  overflow-y: auto;
  max-height: 548px;
}

.ward-grid-header {
  background: #f5f7fa;
  font-weight: 500;
  color: #909399;
  pointer-events: none;
  position: sticky;
  top: 0;

  .header-cell {
    padding: 12px;
    text-align: center;
    border-right: 1px solid #ebeef5;
    &:last-child {
      border-right: none;
    }
  }
}

.ward-grid-body {
  display: grid;
  grid-template-columns: repeat(2, 1fr);

  .ward-item {
    display: grid;
    grid-template-columns: 120px 240px;
    cursor: pointer;
    transition: background 0.2s;
    border-bottom: 1px solid #ebeef5;
    border-right: 1px solid #ebeef5;

    &:nth-child(even) {
      border-right: none;
    }

    &:nth-last-child(-n + 1) {
      border-bottom: none;
    }

    &:hover {
      background: #f5f7fa;
    }

    &.selected {
      background: #1890ef;
      color: #fff;
    }

    .code-cell,
    .name-cell {
      padding: 12px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    .code-cell {
      text-align: center;
      border-right: 1px solid #ebeef5;
    }
  }
}

.empty-body {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 60px;

  .empty-text {
    color: #909399;
  }
}
</style>
