<script lang="ts" setup>
import TreeCom from './components/tree.vue'
import TableCom from './components/table.vue'

import { formatTreeData } from '@/utils'

import type { Tree } from '@/types/tree'
import { useLoadingHook } from '@/hooks/useLoading'
import { appMessage } from '@/hooks/useNaiveApi'
import { ROUTER_PATH } from '@/router/router-path'
import {
  getLeftSysOrganizationForShowApi,
  getSysOrganizationForPageApi,
  deleteSysOrganizationApi,
} from '@/api/system/organization.api'
import type {
  GetSysOrganizationForPageInput,
  GetSysOrganizationForPageOutput,
  LeftSysOrganizationModel,
  OrganizationPageItemModel,
} from '@/api/dto/system/organization.dto'
import type { OperaRow } from '@/api/dto/index.dto'

const tableComRef = ref<null | InstanceType<typeof TableCom>>(null)

const treeData = ref<Tree<LeftSysOrganizationModel>[]>([])

/**获取机构tree */
const getLeftSysOrganizationForShow = async () => {
  try {
    const data = await getLeftSysOrganizationForShowApi({ showNoData: false })
    treeData.value = formatTreeData<LeftSysOrganizationModel>(data, 'organizationName', 'organizationId')
  } catch (error) {
    treeData.value = []
  }
}

onActivated(() => {
  getLeftSysOrganizationForShow()
  tableComRef.value?.getData()
})

const parentId = ref(0)

const handleSelectAll = () => {
  parentId.value = 0
}

const handleSelect = (target: Tree<LeftSysOrganizationModel>) => {
  parentId.value = target.extData.mainId
}

const tableData = ref<GetSysOrganizationForPageOutput>([])
const total = ref(1)
const { loading, loadingFunc } = useLoadingHook()

/**获取机构信息列表 */
const getSysOrganizationForPage = async (param: GetSysOrganizationForPageInput) => {
  try {
    const { data, recordCount } = await getSysOrganizationForPageApi(param, {
      loading: loadingFunc,
      retonly: false,
      showNoData: false,
    })
    tableData.value = data
    total.value = recordCount!
  } catch (error) {
    tableData.value = []
    total.value = 1
  }
}

const router = useRouter()
/**新增机构 */
const handleNewOrganization = () => {
  router.push({ path: ROUTER_PATH.ORGANIZATION_DETAILS, query: { parentId: parentId.value } })
}
/**修改机构 */
const handleEditInfo = async (target: OperaRow<OrganizationPageItemModel>) => {
  const { organizationId } = target
  router.push({ path: ROUTER_PATH.ORGANIZATION_DETAILS, query: { organizationId, parentId: parentId.value } })
}

/**删除机构 */
const handleDeleteRow = async (target: OperaRow<OrganizationPageItemModel>) => {
  const { organizationId, organizationName } = target
  try {
    await ElMessageBox.confirm(`确定要删除机构【${organizationName}】吗?`, '警告', { type: 'warning' })
    await deleteSysOrganizationApi({ organizationId }, { loading: (flag) => (target.loading = flag) })
    appMessage.success('删除成功')
    getLeftSysOrganizationForShow()
    tableComRef.value!.getData()
  } catch (error) {}
}
</script>

<template>
  <div class="organization-container flex">
    <TreeCom :tree-data="treeData" @select-all="handleSelectAll" @select="handleSelect" />

    <TableCom
      ref="tableComRef"
      v-loading="loading"
      :parent-id="parentId"
      :table-data="tableData"
      :total="total"
      @search="getSysOrganizationForPage"
      @new-organization="handleNewOrganization"
      @edit-info="handleEditInfo"
      @delete-row="handleDeleteRow"
    />
  </div>
</template>

<style lang="scss" scoped>
.organization-container {
  height: calc(100vh - 90px);
  background-color: var(--el-bg-color-page);
}
</style>
