<script setup lang="ts">
import { FormInstance, FormRules } from 'element-plus'

/**
 * 按期数清账弹窗
 */

interface RuleForm {
  period: number
}

const props = defineProps<{
  // 最大期数
  maxPeriod: number
  confirmCallback: (period: number) => void
}>()

const inputFormRef = ref<FormInstance>()

const inputForm = reactive({
  period: undefined
})

const dialogVisible = defineModel({ default: false })

const inputFormRules = reactive<FormRules<RuleForm>>({
  period: [
    { required: true, message: '请输入期数', trigger: 'blur' },
    {
      validator: (_, value, callback) => {
        if (value > props.maxPeriod) {
          callback(new Error(`不能大于最大期数：${props.maxPeriod}`))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ]
})

// 弹窗关闭时，重置表单
watch(dialogVisible, (val) => {
  if (val) {
    inputFormRef.value?.resetFields()
  }
})

// 弹窗 - 点击确认
async function handleConfirm(done: (flag?: boolean) => void, formEl: FormInstance | undefined) {
  if (!formEl) return
  await formEl.validate((valid, fields) => {
    console.log('按期数清账 弹窗 表单校验：', valid, fields)
    if (valid) {
      props.confirmCallback(inputForm.period!)
      done()
    } else {
      console.log('提交 弹窗 表单 失败 表单校验不通过：', fields)
      done(true)
    }
  })
}
</script>

<template>
  <CommonFormDialog
    v-model="dialogVisible"
    title="按期数清账"
    :close-on-click-modal="false"
    width="420px"
    :disabled-confirm="!inputForm.period"
    @confirm="handleConfirm($event, inputFormRef)"
  >
    <el-form ref="inputFormRef" size="large" :model="inputForm" :rules="inputFormRules" hide-required-asterisk>
      <el-form-item label="请输入期数：" prop="period">
        <el-input-number
          v-model="inputForm.period"
          :min="1"
          class="input-box"
          clearable
          placeholder="请输入"
          controls-position="right"
        />
      </el-form-item>
    </el-form>
  </CommonFormDialog>
</template>

<style scoped lang="scss">
.input-box {
  width: 300px;
}
</style>
