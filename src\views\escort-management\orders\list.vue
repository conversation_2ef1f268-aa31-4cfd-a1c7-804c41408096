<script setup lang="ts">
/**
 * 留人陪护申请管理 - 服务订单管理
 */
import { ReqGetHospitalSitterByPage, HospitalSitterItem } from '@/api/dto/escort-management.dto.ts'
import {
  requestConfirmReturn,
  requestConfirmService,
  requestGetHospitalSitterByPage
} from '@/api/escort-management.api.ts'
import { ROUTER_PATH } from '@/router/router-path.ts'
import EscortOrdersTable from '@/views/escort-management/orders/components/escort-orders-table.vue'
import { appMessage } from '@/hooks/useNaiveApi.ts'

const router = useRouter()

const escortOrdersTableRef = ref<InstanceType<typeof EscortOrdersTable>>()

// 表格数据
const infoData = reactive({
  listData: [] as HospitalSitterItem[],
  total: 0
})

// 搜索
async function handleSearchData(e: { currentPage: number; pageSize: number; searchData: ReqGetHospitalSitterByPage }) {
  try {
    const { data, recordCount } = await requestGetHospitalSitterByPage({
      platformOrderNo: e.searchData.platformOrderNo,
      patientName: e.searchData.patientName,
      patientIdNo: e.searchData.patientIdNo,
      orderStatus: e.searchData.orderStatus,
      page: e.currentPage,
      rows: e.pageSize
    })

    infoData.listData = data
    infoData.total = recordCount
  } catch (error) {
    console.error('获取订单列表失败：', error)
    appMessage.error('获取订单列表失败')
    infoData.listData = []
    infoData.total = 0
  }
}

// 确定服务
async function handleConfirmService(
  data: {
    orderItem: HospitalSitterItem
    selfLiftingCode: string
  },
  resolve: () => void,
  reject: () => void
) {
  try {
    await requestConfirmService({
      hospitalSitterId: data.orderItem.hospitalSitterId,
      selfPickupCode: data.selfLiftingCode
    })

    resolve()

    await ElMessageBox({
      title: '提示',
      type: 'success',
      message: '确定服务成功',
      callback: () => {
        // 重新加载列表数据
        escortOrdersTableRef.value?.handleSearch()
      }
    })
  } catch (e) {
    console.error('确定服务失败：', e)
    reject()
    appMessage.error('确定服务失败')
  }
}

// 确定归还
function handleConfirmReturn(orderItem: HospitalSitterItem) {
  ElMessageBox.confirm('确定归还吗？')
    .then(async () => {
      try {
        await requestConfirmReturn({
          hospitalSitterId: orderItem.hospitalSitterId
        })

        appMessage.success('确定归还成功')

        escortOrdersTableRef.value?.handleSearch()
      } catch (error) {
        console.error('确定归还失败：', error)
        appMessage.error('确定归还失败')
      }
    })
    .catch(() => {
      // 用户取消操作
    })
}

// 进入详情页面
function handleToDetailPage(orderItem: HospitalSitterItem) {
  router.push({
    path: ROUTER_PATH.ESCORT_ORDER_DETAIL,
    query: { id: orderItem.hospitalSitterId }
  })
}
</script>

<template>
  <el-tabs class="tab-container">
    <el-tab-pane label="全部订单">
      <EscortOrdersTable
        ref="escortOrdersTableRef"
        :list-data="infoData.listData"
        :total="infoData.total"
        @search="handleSearchData"
        @toDetail="handleToDetailPage"
        @confirmService="handleConfirmService"
        @confirmReturn="handleConfirmReturn"
      />
    </el-tab-pane>
  </el-tabs>
</template>

<style scoped lang="scss">
.tab-container {
  margin-top: 20px;
}
</style>
