<script setup lang="ts">
import type { ResExpenseInfoOfAllOrClassificationDTO } from '@/api/dto/discharge-settlement/patient-accounts.dto'
import CommonVirtualTable from '@/components/common-virtual-table/common-virtual-table.vue'
import { h } from 'vue'
import type { SortBy } from 'element-plus'
import { TableV2SortOrder } from 'element-plus'

/**
 * 账务明细
 */

const props = defineProps<{
  // 记帐号
  inpno: string
  // 账务明细
  accountDetailList: ResExpenseInfoOfAllOrClassificationDTO[]
  // 总金额
  totalAmount: number
  // 确认回调
  confirmCallback: (data: {
    selectedData: ResExpenseInfoOfAllOrClassificationDTO[]
    isSelectAllSelfPay: boolean
  }) => Promise<void>
}>()

// 排序状态
const sortState = ref<SortBy>({
  key: 'excutedate',
  order: TableV2SortOrder.ASC
})

// 排序后的数据（用于表格展示）
const sortedAccountList = computed(() => {
  const { key, order } = sortState.value
  if (!key || !order) {
    return props.accountDetailList
  }

  const sortedData = [...props.accountDetailList].sort((a, b) => {
    const valueA = a[key as keyof ResExpenseInfoOfAllOrClassificationDTO]
    const valueB = b[key as keyof ResExpenseInfoOfAllOrClassificationDTO]

    let result = 0
    if (typeof valueA === 'number' && typeof valueB === 'number') {
      result = valueA - valueB
    } else if (typeof valueA === 'string' && typeof valueB === 'string') {
      result = valueA.localeCompare(valueB)
    } else {
      // Fallback for mixed or other types
      result = String(valueA).localeCompare(String(valueB))
    }

    return order === TableV2SortOrder.ASC ? result : -result
  })

  return sortedData
})

// 选中的条目
const selectedData = ref<ResExpenseInfoOfAllOrClassificationDTO[]>([])

const dialogVisible = defineModel({ default: false })

// 是否所选全部按自费处理
const isSelectAllSelfPay = ref(false)

// 计算所选金额
const selectedAmount = computed(() => {
  // 采用"化浮为整"的策略来避免 JavaScript 的浮点数精度问题。
  // 先将每个金额乘以 10000 变为整数，进行累加计算，最后再除以 10000 得到精确结果。
  // 这种方法比在计算后使用 toFixed() 更为健壮。
  const total = selectedData.value.reduce((acc, curr) => acc + curr.totalamount * 10000, 0)
  return total / 10000
})

// 监听排序条件和排序类型变化，执行排序
const onSort = (sortBy: SortBy) => {
  sortState.value = sortBy
}

// 弹窗 - 点击确认
async function handleConfirm() {
  if (selectedData.value.length === 0) {
    ElMessage.warning('请选择账务明细')
    return
  }

  try {
    // 提交
    const tmp = {
      selectedData: JSON.parse(JSON.stringify(selectedData.value)),
      isSelectAllSelfPay: isSelectAllSelfPay.value
    }
    await props.confirmCallback(tmp)
    dialogVisible.value = false
  } catch (e) {
    console.log('提交 弹窗 表单 失败：', e)
  }
}

// 虚拟表格列配置
const tableColumns = [
  {
    key: 'excutedate',
    dataKey: 'excutedate',
    title: '收费日期',
    width: 200,
    sortable: true
  },
  {
    key: 'advtype',
    dataKey: 'advtype',
    title: '医嘱分类',
    width: 120,
    sortable: true
  },
  {
    key: 'advno',
    dataKey: 'advno',
    title: '方号',
    width: 80,
    align: 'center',
    sortable: true
  },
  {
    key: 'itemcode',
    dataKey: 'itemcode',
    title: '项目代码',
    width: 140,
    sortable: true
  },
  {
    key: 'itemname',
    dataKey: 'itemname',
    title: '项目名称',
    width: 260,
    sortable: true
  },
  {
    key: 'quantity',
    dataKey: 'quantity',
    title: '数量',
    width: 80,
    align: 'center',
    sortable: true
  },
  {
    key: 'totalamount',
    dataKey: 'totalamount',
    title: '金额',
    width: 120,
    align: 'right',
    sortable: true
  },
  {
    key: 'price',
    dataKey: 'price',
    title: '价格',
    width: 120,
    align: 'right',
    sortable: true
  },
  {
    key: 'rate',
    dataKey: 'rate',
    title: '比例',
    width: 90,
    align: 'right',
    sortable: true,
    cellRenderer: ({ rowData }: any) => {
      return h('div', {}, `${rowData.rate}%`)
    }
  },
  {
    key: 'selfamount',
    dataKey: 'selfamount',
    title: '自付',
    width: 90,
    align: 'right',
    sortable: true
  }
]
</script>

<template>
  <CommonFormDialog
    v-model="dialogVisible"
    class="select-detailed-billing-dialog"
    :close-on-click-modal="false"
    title="账务明细"
    width="1260px"
    :show-footer="false"
  >
    <div class="form-item">
      <el-checkbox style="margin-right: 11px" v-model="isSelectAllSelfPay">所选全部按自费处理</el-checkbox>

      <div>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleConfirm">确定</el-button>
      </div>
    </div>

    <CommonVirtualTable
      v-model="selectedData"
      style="margin-bottom: 20px"
      :data="sortedAccountList"
      height="518px"
      width="1226px"
      row-key="detailsn"
      :columns="tableColumns"
      :selectable="true"
      :multiple="true"
      :sort-by="sortState"
      @column-sort="onSort"
    />

    <div class="form-item amount-box">
      <div>所选金额：</div>
      <el-input :value="selectedAmount" class="input-box" disabled />

      <div style="margin-left: 20px">总金额：</div>
      <el-input :value="totalAmount" class="input-box" disabled />
    </div>
  </CommonFormDialog>
</template>

<style scoped lang="scss">
.select-detailed-billing-dialog {
  :deep(.el-radio-group) {
    display: unset;
    font-size: unset;
  }

  :deep(.el-radio) {
    margin-right: 20px;
  }
}

.input-box {
  width: 160px;
}

.form-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.form-item-flex {
  display: flex;
  align-items: center;
  position: relative;
}

.amount-box {
  display: flex;
  align-items: baseline;
  margin-bottom: 0;
  justify-content: flex-end;

  :deep(.el-input.is-disabled .el-input__wrapper) {
    background-color: white !important;
  }

  :deep(.el-input.is-disabled .el-input__inner) {
    color: #303133 !important;
    -webkit-text-fill-color: #303133 !important;
    text-align: right !important;
  }
}
</style>
