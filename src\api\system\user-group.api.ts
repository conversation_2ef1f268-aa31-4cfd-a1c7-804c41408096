import type { ApiFunc } from 'axios'
import type {
  AddOrUpdateSysManagerGroupInput,
  GetManagerForPageInput,
  GetManagerForPageOutput,
  GetManagerGroupForPageInput,
  GetManagerGroupForPageOutput,
  GetRelationForPageByIdInput,
  GetRelationForPageByIdOutput,
  GetRolesForListInput,
  GetRolesForListOutput,
  GetSysManagerGroupForUpdateInput,
  GetSysManagerGroupForUpdateOutput,
  deleteSysManagerGroupInput,
} from '../dto/system/user-group.dto'
import { authRequest } from '../utils.api'

/**获取用户组列表 */
export const getManagerGroupForPageApi: ApiFunc<GetManagerGroupForPageInput, GetManagerGroupForPageOutput> = (
  data,
  options
) => {
  return authRequest({ url: 'getManagerGroupForPage', data, ...options })
}

/**删除用户组 */
export const deleteSysManagerGroupApi: ApiFunc<deleteSysManagerGroupInput, undefined> = (data, options) => {
  return authRequest({ url: 'deleteSysManagerGroup', data, ...options })
}

/**获取角色列表 */
export const getRolesForListApi: ApiFunc<GetRolesForListInput, GetRolesForListOutput> = (data, options) => {
  return authRequest({ url: 'getRolesForList', data, ...options })
}

/**获取用户列表 */
export const getManagerForPageApi: ApiFunc<GetManagerForPageInput, GetManagerForPageOutput> = (data, options) => {
  return authRequest({ url: 'getManagerForPage', data, ...options })
}

/**获取用户组详情（修改） */
export const getSysManagerGroupForUpdateApi: ApiFunc<
  GetSysManagerGroupForUpdateInput,
  GetSysManagerGroupForUpdateOutput
> = (data, options) => {
  return authRequest({ url: 'getSysManagerGroupForUpdate', data, ...options })
}

/**创建用户组 */
export const addSysManagerGroupApi: ApiFunc<AddOrUpdateSysManagerGroupInput, undefined> = (data, options) => {
  return authRequest({ url: 'addSysManagerGroup', data, ...options })
}

/**修改用户组 */
export const updateSysManagerGroupApi: ApiFunc<AddOrUpdateSysManagerGroupInput, undefined> = (data, options) => {
  return authRequest({ url: 'updateSysManagerGroup', data, ...options })
}

/**修改用户组 */
export const getRelationForPageByIdApi: ApiFunc<GetRelationForPageByIdInput, GetRelationForPageByIdOutput> = (
  data,
  options
) => {
  return authRequest({ url: 'getRelationForPageById', data, ...options })
}
