<script setup lang="ts">
/**
 * 租赁订单 详情
 */
import { ResRentalOrderDetail, ReqRentalOrderDetail } from '@/api/dto/rental-management/rental-order-management.dto.ts'
import {
  requestInpatientCaregiverOrderInfo,
  requestServiceCompleted,
  requestConfirmLease,
  requestModifyReview,
  requestPersonnelReplacementReview,
  requestInpatientCaregiverCancelOrder
} from '@/api/rental-management.api.ts'
import { ROUTER_PATH } from '@/router/router-path.ts'
import RentalDetailPanel from '@/views/rental-management/rental-order-management/order-detail/components/rental-detail-panel.vue'

const router = useRouter()
const route = useRoute()

let inpatientCaregiverId = ''
const loading = ref(false)

onMounted(() => {
  console.log('服务订单列表传入 -> 详情：', route.query)
  if (!route.query.id) {
    console.error('获取服务订单详情失败：未获取到服务订单 ID')
    ElMessage.error('获取服务订单详情失败：服务订单 ID 为空')
    return
  }
  inpatientCaregiverId = route.query.id as string
  fetchOrderDetail()
})

// 表格数据
const detailInfo = reactive<ResRentalOrderDetail>({} as ResRentalOrderDetail)

// 获取订单详情
const fetchOrderDetail = async () => {
  try {
    loading.value = true
    const params: ReqRentalOrderDetail = {
      inpatientCaregiverId
    }
    const res = await requestInpatientCaregiverOrderInfo(params)
    Object.assign(detailInfo, res)
  } catch (error) {
    console.error('获取订单详情失败：', error)
    ElMessage.error('获取订单详情失败')
  } finally {
    loading.value = false
  }
}

function handleBack() {
  router.push({
    path: ROUTER_PATH.RENTAL_ORDERS
  })
}

// 确定租赁
async function handleConfirmService(
  data: {
    /* ID */
    inpatientCaregiverId: string
    /* 陪护人ID */
    inpatientCaregiverPersonId: string
  },
  resolve: () => void,
  reject: () => void
) {
  console.log('确定租赁 弹窗，点击确认：', data)
  try {
    await requestConfirmLease({
      inpatientCaregiverId: data.inpatientCaregiverId,
      inpatientCaregiverPersonId: data.inpatientCaregiverPersonId
    })

    console.log('确定租赁成功')

    // 调用 resolve 来关闭弹窗
    resolve()

    await ElMessageBox({
      title: '提示',
      type: 'success',
      message: '确定租赁成功',
      callback: () => {
        fetchOrderDetail()
      }
    })
  } catch (e) {
    console.error('确定租赁失败：', e)
    reject()
  }
}

// 换人审核
async function handleReplacement(
  data: {
    inpatientCaregiverId: string
    inpatientCaregiverPersonId: string
    reviewOperate: boolean
  },
  resolve: () => void,
  reject: () => void
) {
  console.log('换人审核 弹窗，点击确认：', data)
  try {
    await requestPersonnelReplacementReview({
      inpatientCaregiverId: data.inpatientCaregiverId,
      inpatientCaregiverPersonId: data.inpatientCaregiverPersonId,
      reviewOperate: data.reviewOperate
    })

    // 调用 resolve 来关闭弹窗
    resolve()

    await ElMessageBox({
      title: '提示',
      type: 'success',
      message: '换人审核成功',
      callback: () => {
        fetchOrderDetail()
      }
    })
  } catch (e) {
    console.error('换人审核失败：', e)
    reject()
  }
}

// 修改审核
async function handleModifyAudit(
  data: {
    inpatientCaregiverUpdateId: string
    inpatientCaregiverId: string
    reviewOperate: boolean
  },
  resolve: () => void,
  reject: () => void
) {
  console.log('修改审核 弹窗，点击确认：', data)
  try {
    await requestModifyReview({
      inpatientCaregiverUpdateId: data.inpatientCaregiverUpdateId,
      inpatientCaregiverId: data.inpatientCaregiverId,
      reviewOperate: data.reviewOperate
    })

    // 调用 resolve 来关闭弹窗
    resolve()

    await ElMessageBox({
      title: '提示',
      type: 'success',
      message: '修改审核成功',
      callback: () => {
        fetchOrderDetail()
      }
    })
  } catch (e) {
    console.error('修改审核失败：', e)
    reject()
  }
}

// 取消订单
async function handleCancelOrder(data: { inpatientCaregiverId: string }) {
  ElMessageBox.confirm('确定取消订单吗？')
    .then(async () => {
      try {
        await requestInpatientCaregiverCancelOrder({ inpatientCaregiverId: data.inpatientCaregiverId })
        ElMessage.success('取消订单成功')
        fetchOrderDetail()
      } catch (error) {
        console.error('取消订单失败：', error)
        ElMessage.error('取消订单失败')
      }
    })
    .catch(() => {
      // catch error
    })
}

// 服务完成
function handleFinishedService(data: { inpatientCaregiverId: string }) {
  ElMessageBox.confirm('确定服务完成吗？')
    .then(async () => {
      try {
        await requestServiceCompleted({ inpatientCaregiverId: data.inpatientCaregiverId })
        ElMessage.success('服务完成操作成功')
        fetchOrderDetail()
      } catch (error) {
        console.error('服务完成操作失败：', error)
        ElMessage.error('服务完成操作失败')
      }
    })
    .catch(() => {
      // catch error
    })
}
</script>

<template>
  <div class="page-container">
    <div class="header">
      <el-button class="header-back-btn" type="warning" @click="handleBack">返回</el-button>
    </div>

    <RentalDetailPanel
      :detail-info="detailInfo"
      @confirm="handleConfirmService"
      @replacement="handleReplacement"
      @finished="handleFinishedService"
      @modify="handleModifyAudit"
      @cancel="handleCancelOrder"
    />
  </div>
</template>

<style scoped lang="scss">
.page-container {
  height: calc(100vh - 50px - 34px);
  display: flex;
  flex-direction: column;
}

.header {
  height: 50px;
  background: #f4f4f5;
  display: flex;
  justify-content: right;
  align-items: center;
  padding: 0 20px;
  position: sticky;
  flex: none;
  z-index: 2;

  & .header-back-btn {
    width: 68px;
    height: 36px;
  }
}
</style>
