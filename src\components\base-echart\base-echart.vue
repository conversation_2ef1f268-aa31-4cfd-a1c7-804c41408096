<script lang="ts" setup>
import { useGlobalStore } from '@/stores/global.store'
import * as echarts from 'echarts'
import { storeToRefs } from 'pinia'

export type EChartsOption = echarts.EChartsOption

defineProps<{
  className?: string | { [k: string]: boolean }
  errorDescription?: string
}>()

const { isDark } = storeToRefs(useGlobalStore())

const echartNavRef = ref<HTMLDivElement>()
const echart = shallowRef<echarts.ECharts>()

watch(isDark, (flag) => {
  echart.value?.dispose()
  echart.value = echarts.init(echartNavRef.value!, flag ? 'dark' : 'light')
})

const echartResizeObserver = new ResizeObserver(([entry]) => {
  const { width, height } = entry.contentRect
  echart.value!.resize({ width, height })
})

const emit = defineEmits<{
  /**重试 */
  retry: []
}>()

const handleRetry = () => {
  emit('retry')
}

onMounted(async () => {
  await nextTick()

  if (!echart.value) echart.value = echarts.init(echartNavRef.value!, isDark.value ? 'dark' : 'light')

  echartResizeObserver.observe(echartNavRef.value!)
})

onBeforeUnmount(() => {
  echartResizeObserver.disconnect()
})

defineExpose({
  getEchartNavRef: () => echartNavRef.value,
  getEchart: () => echart.value,
})
</script>

<template>
  <el-card shadow="hover" v-bind="$attrs" class="echart-container">
    <div ref="echartNavRef" class="echart-nav" :class="className"></div>

    <!-- 错误提示 -->
    <XyzTransition appear xyz="fade duration-3">
      <div v-if="errorDescription" class="error">
        <el-alert
          title="错误"
          :description="errorDescription"
          type="error"
          show-icon
          close-text="重试"
          @close="handleRetry"
        />
      </div>
    </XyzTransition>

    <slot></slot>
  </el-card>
</template>

<style lang="scss" scoped>
.echart-container {
  :deep(.el-card__body) {
    position: relative;
    width: 100%;
    height: 100%;
    padding: 0;
  }

  .echart-nav {
    height: 100%;
  }

  .error {
    position: absolute;
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 100%;
    top: 0;
    background: var(--el-mask-color);
    font-size: 16px;
    font-weight: bold;
    :deep(.el-alert) {
      width: 60%;
    }
  }
}
</style>
