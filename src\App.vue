<script setup lang="ts">
import { PrintMode, usePrinterStore } from '@/stores/printer.store.ts'
import ElConfig from './components/el-config'

// 设置打印机状态监听，动态改变打印机连接状态
usePrinterStore().printMode === PrintMode.SILENT && usePrinterStore().setPrinterClientStatusCallback()
</script>

<template>
  <ElConfig>
    <router-view v-slot="{ Component }">
      <transition name="fade">
        <component :is="Component" />
      </transition>
    </router-view>
  </ElConfig>
</template>

<style lang="scss">
#app {
  width: 100vw;
  height: 100vh;
}
</style>
