<script setup lang="ts">
import '@wangeditor/editor/dist/css/style.css'
import { IEditorConfig, IToolbarConfig } from '@wangeditor/editor'
import { Editor, Toolbar } from '@wangeditor/editor-for-vue'

// 编辑器实例，必须用 shallowRef
const editorRef = shallowRef()

// 内容 HTML
const valueHtml = ref('')

const toolbarConfig: Partial<IToolbarConfig> = {
  // 隐藏部分工具：https://github.com/wangeditor-team/wangEditor/blob/master/packages/editor/src/init-default-config/config/toolbar.ts
  excludeKeys: [
    'numberedList',
    'bulletedList',
    'code',
    'codeBlock',
    'todo',
    'group-video',
    'insertLink',
    'insertTable',
    'divider',
    'insertImage'
  ]
}

const editorConfig: Partial<IEditorConfig> = { placeholder: '请输入内容...', maxLength: 0, autoFocus: false }

const handleCreated = (editor: any) => {
  setUploadImageConfig(editor)
  editorRef.value = editor // 记录 editor 实例，重要！
}

const setUploadImageConfig = (editor: any) => {
  editor.getConfig().MENU_CONF['uploadImage'] = {
    // 因为不需要真的上传，这里随便写一个，不能为空
    server: '123',
    fieldName: '123',
    // 后端要求 Base64 直接传入，所以这里直接设置最大 3M 大小，加上 Base64 最大也设置 3M，就能满足需求
    maxFileSize: 3 * 1024 * 1024, // 3M
    base64LimitSize: 3 * 1024 * 1024, // 3M
    maxNumberOfFiles: 10,
    allowedFileTypes: ['image/*'],
    // 上传错误
    onError(file: File, err: any, res: any) {
      console.log(`${file.name} 上传出错`, err, res)

      // 如果包含 maximum allowed size，则提示
      if (err.message.includes('maximum allowed size')) {
        console.error('图片大小超过限制，请重新上传')
        ElMessage.error('图片大小超过限制(3M)，请重新上传')
      }
    }
  }
}

// 组件销毁时，也及时销毁编辑器
onBeforeUnmount(() => {
  const editor = editorRef.value
  if (editor == null) return
  editor.destroy()
})

/**
 * 插入 html
 */
const insertHtml = (html: string) => {
  const editor = editorRef.value
  if (editor == null) return
  editor.setHtml(html)
}

/**
 * 导出 html
 */
const exportHtml = () => {
  const editor = editorRef.value
  if (editor == null || editor.isEmpty()) return ''
  return editor.getHtml()
}

defineExpose({
  insertHtml,
  exportHtml
})
</script>

<template>
  <div class="editor-container">
    <Toolbar class="tools" :editor="editorRef" :defaultConfig="toolbarConfig" mode="default" />
    <Editor
      class="w-editor editor-content"
      v-model="valueHtml"
      :defaultConfig="editorConfig"
      mode="default"
      @onCreated="handleCreated"
    />
  </div>
</template>
<style lang="scss" scoped>
.editor-container.w-e-full-screen-container {
  z-index: 10000;
}

.editor-container {
  border: 1px solid #707070;
  line-height: 1.5;
  width: 100%;
  word-break: break-all;

  .tools {
    border-bottom: 1px solid #ccc;
  }

  .editor-content {
    width: 100%;
    height: 320px !important;
    overflow-y: hidden;
  }
}
</style>
