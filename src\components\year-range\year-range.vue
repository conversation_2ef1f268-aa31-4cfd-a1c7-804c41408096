<script lang="ts" setup>
import { parseTime } from '@/utils'
import { ElDatePicker } from 'element-plus'

const modelValue = defineModel<[string, string]>({ required: true })

/**开始时间 */
const startDate = ref('')
/**结束时间 */
const endDate = ref('')
const endDateRef = ref<(InstanceType<typeof ElDatePicker> & { focus: () => void }) | null>(null)

watch(startDate, () => {
  if (endDateRef.value) {
  }
})

/**开始时间可选范围 */
const disabledStartDate = (time: Date) => {
  return time.getTime() > Date.now()
}
/**结束时间可选范围 */
const disableEndDate = (time: Date) => {
  return time.getTime() > Date.now() || time.getTime() < new Date(startDate.value).getTime()
}

watch(
  modelValue,
  ([start, end]) => {
    startDate.value = start
    endDate.value = end
  },
  { immediate: true }
)

/**选择开始时间时候触发 */
const handlePanelChangeStartDate = async (time: Date) => {
  startDate.value = parseTime('{Y}-{M}-{D}', time)
  modelValue.value = [startDate.value, endDate.value]
  await nextTick()
  /**选择了开始时间之后，自动打开结束时间选择器 */
  endDateRef.value!.focus()
}

/**选择结束时间时候触发 */
const handlePanelChangeEndDate = (time: Date) => {
  const endDateYear = new Date(time).getFullYear()
  const toYear = new Date().getFullYear()
  if (endDateYear === toYear) {
    endDate.value = parseTime('{Y}-{M}-{D}')
  }
  if (endDateYear < toYear) {
    endDate.value = `${endDateYear}-12-31`
  }

  modelValue.value = [startDate.value, endDate.value]
}
</script>

<template>
  <el-date-picker
    :model-value="startDate"
    :disabled-date="disabledStartDate"
    type="year"
    placeholder="开始时间"
    :editable="false"
    :clearable="false"
    class="year-picker"
    @panel-change="handlePanelChangeStartDate"
  />
  <span>至</span>
  <el-date-picker
    ref="endDateRef"
    :model-value="endDate"
    :disabled-date="disableEndDate"
    type="year"
    placeholder="结束时间"
    :editable="false"
    :disabled="!startDate"
    :clearable="false"
    class="year-picker"
    @panel-change="handlePanelChangeEndDate"
  />
</template>

<style>
.year-picker {
  width: 80px !important;
}
</style>

<style lang="scss" scoped>
span {
  padding: 0 8px;
}
</style>
