<script setup lang="ts">
import { AdmissionRegistrationStatus, admissionRegistrationStatusOptions } from '@/api/dto/emun.dto.ts'
import { OperaRow } from '@/api/dto/index.dto'
import {
  AdmissionRegistrationExtra,
  ResPrintingOfApplicationForm,
  ResPrintingOfWristStrap
} from '@/api/dto/inpatient-registration.dto.ts'
import {
  requestAcceptInpatientRegistration,
  requestInpatientRegistrationByPage,
  requestPrintingOfApplicationForm,
  requestPrintingOfWristStrap
} from '@/api/inpatient-registration.api.ts'
import { requestRentalSelectDepartmentList } from '@/api/rental-management.api.ts'
import { ResRentalQuerydeptDTO } from '@/api/dto/rental-management/rental-order-management.dto.ts'
import { useFormHook } from '@/hooks/useForm'
import { useMountDialog } from '@/hooks/useMountDialog.ts'
import { appMessage } from '@/hooks/useNaiveApi.ts'
import { usePaginationHook } from '@/hooks/usePagination'
import { ROUTER_PATH } from '@/router/router-path'
import ReceiveFormDialog from '@/views/inpatient-registration/components/receive-form-dialog.vue'
import {
  useInpatientFormPrinter,
  useWristbandPrinter
} from '@/views/inpatient-registration/utils/inpatient-printer-helper.ts'
import { maskIdCard } from '@/utils/index.ts'
import { getUniqueRouteName } from '@/utils/router-utils.ts'
import { useInpatientNotificationStore } from '@/stores/inpatient-notification.store'

const router = useRouter()
const inpatientNotificationStore = useInpatientNotificationStore()

const { formRef, resetForm } = useFormHook()
const { currentPage, pageSize, total, resetPagination } = usePaginationHook()
const { printWristband, wristbandPrintError } = useWristbandPrinter()
const { printInpatientForm, inpatientFormPrintError } = useInpatientFormPrinter()
const { open } = useMountDialog(ReceiveFormDialog)

defineOptions({
  name: getUniqueRouteName(ROUTER_PATH.INPATIENT_REGISTRATION_LIST)
})

// 搜索表单数据
const formData = reactive({
  // 住院人姓名
  patientName: '',
  // 住院号
  admissionNo: '',
  // 状态
  status: '',
  // 身份证号码
  patientIdNo: '',
  // 住院科室
  hospitalizationDepartmentName: '',
  // 开卡科室
  applyDepartmentName: '',
  // 开卡医生
  applyDoctorName: '',
  // 入院日期
  indate: '',
  // 开卡时间
  applyTime: ''
})

// 科室列表
const departmentOptions = ref<ResRentalQuerydeptDTO[]>([] as ResRentalQuerydeptDTO[])

onMounted(async () => {
  const res = await requestRentalSelectDepartmentList()
  departmentOptions.value = res || []
})

// 点击搜索按钮后，设置 hasActivated = true，再次进入页面时，会触发搜索
let _hasActivated = false
// 用于 keep-alive 组件重新激活时，重新触发搜索，一开始进入页面时候不触发
// 适用于从详情页返回列表页，或者切换 tab 的场景
onActivated(() => {
  if (_hasActivated) {
    handleSearch()
  }
})

const loading = ref(false)
const tableData = ref<AdmissionRegistrationExtra[]>([])
// 默认状态下不显示空数据图标，点击搜索之后才显示空数据图标
const showEmptyIcon = ref(false)
// 默认状态下的空数据文本
const emptyText = ref('暂无数据，请在上方输入用户信息点击搜索进行查询')

watch([currentPage], () => {
  handleSearch()
})

function setTableEmptyConfig() {
  showEmptyIcon.value = true
  emptyText.value = '查询不到符合条件的用户，请重新输入用户信息进行查询'
}

async function handleSearch() {
  _hasActivated = true
  setTableEmptyConfig()

  try {
    const {
      data,
      pageSize: newPageSize,
      pageIndex,
      recordCount
    } = await requestInpatientRegistrationByPage({
      patientName: formData.patientName,
      admissionNo: formData.admissionNo,
      status: formData.status ?? '',
      patientIdNo: formData.patientIdNo ?? '',
      hospitalizationDepartmentName: formData.hospitalizationDepartmentName ?? '',
      applyDepartmentName: formData.applyDepartmentName ?? '',
      applyDoctorName: formData.applyDoctorName ?? '',
      indateStart: formData.indate?.[0] ? `${formData.indate?.[0]} 00:00:00` : '',
      indateEnd: formData.indate?.[1] ? `${formData.indate?.[1]} 23:59:59` : '',
      applyTimeStart: formData.applyTime?.[0] ? `${formData.applyTime?.[0]} 00:00:00` : '',
      applyTimeEnd: formData.applyTime?.[1] ? `${formData.applyTime?.[1]} 23:59:59` : '',
      page: currentPage.value,
      rows: pageSize.value
    })
    console.log('搜索住院登记列表：', data)
    tableData.value = data
    currentPage.value = pageIndex
    pageSize.value = newPageSize
    total.value = recordCount
  } catch (e) {
    console.error('搜索住院登记列表失败：', e)
    tableData.value = []
    resetPagination()
  }
}

function handleReset() {
  resetForm()
  resetPagination()
  tableData.value = []
  // 医院要求不自动触发
  // handleSearch()
}

function handleDetail(row: OperaRow<AdmissionRegistrationExtra>) {
  router.push({
    path: ROUTER_PATH.INPATIENT_REGISTRATION_DETAIL,
    query: {
      admissionRegistrationId: row.admissionRegistrationId
    }
  })
}

const openReceiveDialog = (registrationInfo: OperaRow<AdmissionRegistrationExtra>) => {
  open({
    patientName: registrationInfo.patientName,
    hosNumber: registrationInfo.regNo,
    prepay: registrationInfo.prepay,
    finished: async (selectedDate: string) => {
      console.log('选择的入院登记日期：', selectedDate)
      await acceptInpatientRegistration(selectedDate, registrationInfo.admissionRegistrationId)
    }
  })
}

async function acceptInpatientRegistration(admissionTime: string, admissionRegistrationId: string) {
  try {
    const { msg } = await requestAcceptInpatientRegistration({
      admissionRegistrationId,
      admissionTime
    })
    console.log('入院登记处理结果：', msg)

    ElMessageBox({
      title: '提示',
      type: 'success',
      message: '入院登记接受处理成功',
      callback: () => {
        handleSearch()
        inpatientNotificationStore.triggerPendingAdmissionUpdate()
      }
    })
  } catch (e) {
    console.error('入院登记处理失败：', e)
    throw e
  }
}

// 打印手腕带模板
async function handlePrintWristband(rowData: OperaRow<AdmissionRegistrationExtra>) {
  console.log('打印手腕带模板', rowData)

  let printFormData: ResPrintingOfWristStrap

  try {
    printFormData = await requestPrintingOfWristStrap({
      admissionRegistrationId: rowData.admissionRegistrationId
    })
    console.log('获取到手腕带模板数据：', printFormData)
  } catch (e) {
    console.error('获取手腕带模板数据失败：', e)
    return
  }

  // 增加记帐号判空，避免打印的手腕带二维码为默认值产生误导
  if (!printFormData.inpno) {
    appMessage.error('打印失败，记帐号为空')
    return
  }

  // 记帐号
  const billingAccountValue = printFormData.inpno

  rowData.isWristbandPrintingLoading = true

  await printWristband({
    // 患者名
    patientName: printFormData.name,
    // 年龄
    age: printFormData.age,
    // 性别
    gender: printFormData.sex,
    // 科别
    department: printFormData.curdptnm,
    // 住院号
    hospitalizationNumber: printFormData.patno,
    // 记帐号
    billingAccount: billingAccountValue,
    // 二维码值 - 对应记帐号
    billingAccountBar: billingAccountValue
  })

  rowData.isWristbandPrintingLoading = false
  if (wristbandPrintError.value) {
    console.error(wristbandPrintError.value)
    appMessage.error(wristbandPrintError.value)
    return
  }
  console.log('手腕带打印成功')
  appMessage.success('手腕带打印成功')
}

// 打印入院卡模板
async function handlePrintInpatientForm(rowData: OperaRow<AdmissionRegistrationExtra>) {
  console.log('打印入院卡模板', rowData)

  let printFormData: ResPrintingOfApplicationForm

  try {
    printFormData = await requestPrintingOfApplicationForm({
      admissionRegistrationId: rowData.admissionRegistrationId
    })
    console.log('获取到入院卡模板数据：', printFormData)
  } catch (e) {
    console.error('获取入院卡模板数据失败：', e)
    appMessage.error('获取入院卡模板数据失败')
    return
  }

  rowData.isInpatientFormPrintingLoading = true
  await printInpatientForm(printFormData)
  rowData.isInpatientFormPrintingLoading = false

  if (inpatientFormPrintError.value) {
    console.error(inpatientFormPrintError.value)
    appMessage.error(inpatientFormPrintError.value)
    return
  }

  console.log('入院卡打印成功')
  appMessage.success('入院卡打印成功')
}
</script>

<template>
  <div class="layout-page-pd">
    <el-form ref="formRef" :model="formData" inline>
      <el-form-item prop="patientName" label="住院人姓名：">
        <el-input
          class="search-input"
          v-model="formData.patientName"
          size="large"
          clearable
          placeholder="请输入"
          @keydown.enter="handleSearch"
        />
      </el-form-item>
      <el-form-item prop="admissionNo" label="住院号：">
        <el-input
          class="search-input"
          v-model="formData.admissionNo"
          size="large"
          clearable
          placeholder="请输入"
          @keydown.enter="handleSearch"
        />
      </el-form-item>
      <el-form-item prop="patientIdNo" label="身份证号码：">
        <el-input
          class="search-input"
          v-model="formData.patientIdNo"
          size="large"
          clearable
          placeholder="请输入"
          @keydown.enter="handleSearch"
        />
      </el-form-item>
      <el-form-item prop="applyDepartmentName" label="开卡科室：">
        <el-input
          class="search-input"
          v-model="formData.applyDepartmentName"
          size="large"
          clearable
          placeholder="请输入"
          @keydown.enter="handleSearch"
        />
      </el-form-item>
      <el-form-item prop="hospitalizationDepartmentName" label="住院科室：">
        <el-select
          class="search-input"
          v-model="formData.hospitalizationDepartmentName"
          clearable
          size="large"
          placeholder="请选择"
          filterable
        >
          <el-option
            v-for="item in departmentOptions"
            :key="item.deptcode"
            :label="item.deptname"
            :value="item.deptname"
          />
        </el-select>
      </el-form-item>

      <el-form-item prop="indate" label="入院日期：">
        <el-date-picker
          class="search-input"
          v-model="formData.indate"
          @keydown.enter="handleSearch"
          range-separator="至"
          clearable
          size="large"
          type="daterange"
          value-format="YYYY-MM-DD"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        />
      </el-form-item>
      <el-form-item prop="applyDoctorName" label="开卡医生：">
        <el-input
          class="search-input"
          v-model="formData.applyDoctorName"
          size="large"
          clearable
          placeholder="请输入"
          @keydown.enter="handleSearch"
        />
      </el-form-item>
      <el-form-item prop="applyTime" label="开卡时间：">
        <el-date-picker
          class="search-input"
          v-model="formData.applyTime"
          @keydown.enter="handleSearch"
          range-separator="至"
          clearable
          size="large"
          type="daterange"
          value-format="YYYY-MM-DD"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        />
      </el-form-item>
      <el-form-item prop="status" label="状态：">
        <el-select class="search-input--wider" v-model="formData.status" size="large" clearable>
          <el-option
            v-for="(status, index) in admissionRegistrationStatusOptions"
            :key="index"
            :label="status.label"
            :value="status.value"
          ></el-option>
        </el-select>
      </el-form-item>

      <el-form-item class="search-btn-group">
        <el-button
          v-auth.disabled="'admissionRegistration_getAdmissionRegistrationByPage'"
          type="primary"
          size="large"
          @click="handleSearch"
        >
          搜索
        </el-button>
        <el-button
          v-auth.disabled="'admissionRegistration_getAdmissionRegistrationByPage'"
          size="large"
          @click="handleReset"
          >重置</el-button
        >
      </el-form-item>
    </el-form>

    <BaseTable
      v-loading="loading"
      :data="tableData"
      border
      height="590"
      :show-empty-icon="showEmptyIcon"
      :empty-text="emptyText"
    >
      <el-table-column prop="patientName" label="住院人姓名" min-width="130" fixed="left" />
      <el-table-column prop="inpno" label="记帐号" min-width="160">
        <template #default="{ row }: { row: OperaRow<AdmissionRegistrationExtra> }">
          {{ row.inpno || '-' }}
        </template>
      </el-table-column>
      <el-table-column prop="patientIdNo" label="身份证号" min-width="200">
        <template #default="{ row }: { row: OperaRow<AdmissionRegistrationExtra> }">
          {{ maskIdCard(row.patientIdNo) }}
        </template>
      </el-table-column>
      <el-table-column prop="hospitalizationDepartmentName" label="住院科室" min-width="180" />
      <el-table-column prop="applyDepartmentName" label="开卡科室" min-width="180" />
      <el-table-column prop="applyDoctorName" label="开卡医生" min-width="160" />
      <el-table-column prop="status" label="状态" min-width="220">
        <template #default="{ row }: { row: OperaRow<AdmissionRegistrationExtra> }">
          {{ admissionRegistrationStatusOptions.find((item) => item.value === row.status)?.label ?? '-' }}
        </template>
      </el-table-column>
      <el-table-column prop="indate" label="入院日期" width="200">
        <template #default="{ row }: { row: OperaRow<AdmissionRegistrationExtra> }">
          {{ row.indate || '-' }}
        </template>
      </el-table-column>
      <el-table-column prop="applyTime" label="开卡时间" width="200" />
      <el-table-column prop="registrationTime" label="登记时间" width="200">
        <template #default="{ row }: { row: OperaRow<AdmissionRegistrationExtra> }">
          {{ row.registrationTime || '-' }}
        </template>
      </el-table-column>

      <el-table-column label="操作" min-width="400" fixed="right">
        <template #default="{ row }: { row: OperaRow<AdmissionRegistrationExtra> }">
          <el-button
            v-auth.disabled="'admissionRegistration_getAdmissionRegistrationForDetail'"
            plain
            @click="handleDetail(row)"
          >
            详情
          </el-button>
          <el-button
            v-auth.disabled="'admissionRegistration_accept'"
            v-if="row.status === AdmissionRegistrationStatus.COMPLETED"
            type="success"
            @click="openReceiveDialog(row)"
          >
            接受处理
          </el-button>
          <el-button
            v-auth.disabled="'admissionRegistration_printingOfApplicationForm'"
            v-if="row.status === AdmissionRegistrationStatus.PROCESSED"
            type="primary"
            :loading="row.isInpatientFormPrintingLoading"
            @click="handlePrintInpatientForm(row)"
          >
            入院卡打印
          </el-button>
          <el-button
            v-auth.disabled="'admissionRegistration_printingOfWristStrap'"
            v-if="row.status === AdmissionRegistrationStatus.PROCESSED"
            color="#00B798"
            :loading="row.isWristbandPrintingLoading"
            @click="handlePrintWristband(row)"
          >
            手腕带打印
          </el-button>
        </template>
      </el-table-column>
    </BaseTable>

    <BasePagination v-model:current-page="currentPage" v-model:page-size="pageSize" :total="total" />
  </div>
</template>

<style lang="scss" scoped>
:deep(.el-date-editor--daterange) {
  --el-date-editor-daterange-width: 280px;
}

.search-input {
  width: 200px;

  &--wider {
    width: 230px;
  }
}

:deep(.search-btn-group.el-form-item) {
  margin-right: 0;

  .el-form-item__content {
    min-width: 100px;
  }
}
</style>
