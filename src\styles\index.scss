@use './init.scss' as *;
@use './transition.scss' as *;
@use './variables.scss' as *;
@use './layout.scss' as *;
@use './el-table.scss' as *;
@use './el-dialog.scss' as *;
@use './el-checkbox.scss' as *;
@use './el-tabs.scss' as *;
@use './el-button.scss' as *;
@use './el-form.scss' as *;
@use './el-tree.scss' as *;
@use './el-divider.scss' as *;
@use './el-tag.scss' as *;
@use './el-descriptions.scss' as *;
@use './el-message-box.scss' as *;
@use './element-variables.scss' as *;
@import 'element-plus/theme-chalk/display.css';
@import 'element-plus/theme-chalk/dark/css-vars.css';
@import 'element-plus/theme-chalk/el-tree.css';
@import 'element-plus/theme-chalk/el-tree-select.css';

a,
.green {
  text-decoration: none;
  color: hsla(160, 100%, 37%, 1);
  transition: 0.4s;
}

.singleton-tooltip {
  transition: transform 0.28s var(--el-transition-function-fast-bezier);
}

.layout-page-pd {
  padding: 30px 20px;
}

.back-bar {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  height: 52px;
  padding: 0 20px;
  background: var(--el-color-info-light-9);
}

.scrollbar-animation {
  scroll-behavior: smooth;
}

$baseMargin: 2px;

@for $i from 1 through 15 {
  .mt-#{$i} {
    margin-top: $baseMargin * $i;
  }
  .mr-#{$i} {
    margin-right: $baseMargin * $i;
  }
  .mb-#{$i} {
    margin-bottom: $baseMargin * $i;
  }
  .ml-#{$i} {
    margin-left: $baseMargin * $i;
  }
}

.common-panel-title {
  font-weight: 700;
  font-size: 16px;
  display: flex;
  align-items: center;

  &::before {
    content: '';
    display: inline-block;
    width: 4px;
    height: 16px;
    background: #1890ef;
    margin-right: 8px;
  }
}
