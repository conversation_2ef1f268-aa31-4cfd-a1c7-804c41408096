import type { UserConfig } from 'vite'

export default (): UserConfig['server'] => {
  // https://vitejs.cn/config/#server-options
  return {
    // 指定服务器应该监听哪个 IP 地址。 如果将此设置为 0.0.0.0 或者 true 将监听所有地址，包括局域网和公网地址。
    host: true,
    // 指定开发服务器端口。注意：如果端口已经被使用，Vite 会自动尝试下一个可用的端口，所以这可能不是开发服务器最终监听的实际端口。
    port: 1027,
    // 设为 true 时若端口已被占用则会直接退出，而不是尝试下一个可用端口
    strictPort: false,
    // 启用 TLS + HTTP/2。注意：当 server.proxy 选项 也被使用时，将会仅使用 TLS。
    // 这个值也可以是一个传递给 https.createServer() 的 选项对象。
    // https: false,
    // 在开发服务器启动时自动在浏览器中打开应用程序。当此值为字符串时，会被用作 URL 的路径名。若你想指定喜欢的浏览器打开服务器，你可以设置环境变量 process.env.BROWSER（例如：firefox）。查看 这个 open 包 获取更多细节。
    open: false,
    // 为开发服务器配置自定义代理规则。期望接收一个 { key: options } 对象。如果 key 值以 ^ 开头，将会被解释为 RegExp。configure 可用于访问 proxy 实例。
    proxy: {
      '/api': {
        target: 'http://192.168.130.131:28081/FoYing-Web',
        changeOrigin: true,
        rewrite: (path) => path.replace('/api', '')
      }
    },
    // 为开发服务器配置 CORS。默认启用并允许任何源，传递一个 选项对象 来调整行为或设为 false 表示禁用。
    cors: true,
    // 启用HMR
    hmr: {
      // 设置较长的超时时间，避免频繁断开连接
      timeout: 10000,
      // 设置较高的超时时间
      overlay: false
    },
    // 禁用文件观察限制，可能有助于解决某些文件系统的问题
    watch: {
      usePolling: false,
      // 增加文件监听限制
      ignored: ['**/node_modules/**', '**/.git/**']
    }
  }
}
