<script lang="ts" setup>
import { useFormHook } from '@/hooks/useForm'
import BasePagination from '@/components/base-pagination'
import { useLoadingHook } from '@/hooks/useLoading'
import { usePaginationHook } from '@/hooks/usePagination'
import type {
  GetSysRoleByPageInput,
  GetSysRoleByPageOutput,
  SysRoleByPageItemModel,
} from '@/api/dto/system/roles-manage.dto'
import { deleteSysRoleApi, getSysRoleByPageApi } from '@/api/system/roles-manage.api'
import { ROUTER_PATH } from '@/router/router-path'
import type { OperaRow } from '@/api/dto/index.dto'
import { useEditRoleAuthDialog } from './hook/edit-role-auth-dialog'
import { appMessage } from '@/hooks/useNaiveApi'
import { useVisitBindUserDialog } from './hook/visit-bind-user-dialog'

type FormData = Omit<GetSysRoleByPageInput, 'page' | 'rows'>

const { formRef, resetForm } = useFormHook()

const formData = reactive<FormData>({ roleName: '', enabledMark: '' })

const tableData = ref<GetSysRoleByPageOutput>([])
const { currentPage, pageSize, total } = usePaginationHook()
const { loading, loadingFunc } = useLoadingHook()

/**获取角色列表 */
const getSysRoleByPage = async () => {
  try {
    const { data, recordCount } = await getSysRoleByPageApi(
      { ...formData, page: currentPage.value, rows: pageSize.value },
      { loading: loadingFunc, retonly: false, showNoData: false }
    )
    tableData.value = data
    total.value = recordCount!
  } catch (error) {
    tableData.value = []
    total.value = 1
  }
}
/**搜索 */
const handleSearch = () => {
  currentPage.value = 1
  getSysRoleByPage()
}
watch([currentPage, pageSize], () => {
  getSysRoleByPage()
})
/**重置 */
const handleReset = () => {
  resetForm()
  handleSearch()
}
onMounted(getSysRoleByPage)

const router = useRouter()
/**新增角色 */
const handleNewRole = () => {
  router.push(ROUTER_PATH.ROLES_MANAGE_DETAILS)
}

/**权限设置 */
const handleEditRoleAuth = async (target: OperaRow<SysRoleByPageItemModel>) => {
  await useEditRoleAuthDialog(target)
  getSysRoleByPage()
}

/**编辑角色 */
const handleEditRoleDetails = (target: OperaRow<SysRoleByPageItemModel>) => {
  const { roleId } = target
  router.push({ path: ROUTER_PATH.ROLES_MANAGE_DETAILS, query: { roleId } })
}

/**查看已关联用户 */
const handleVisitBindUser = (target: OperaRow<SysRoleByPageItemModel>) => {
  try {
    useVisitBindUserDialog(target)
  } catch (error) {}
}

/**删除角色 */
const handleDeleteRole = async (target: OperaRow<SysRoleByPageItemModel>) => {
  const { roleName, roleId } = target
  try {
    await ElMessageBox.confirm(`是否确认删除该【${roleName}】角色`, '警告', { type: 'warning' })
    await deleteSysRoleApi({ id: roleId }, { loading: (flag) => (target.loading = flag) })
    appMessage.success('删除成功')
    getSysRoleByPage()
  } catch (error) {}
}
</script>

<template>
  <div class="role_manage-container layout-page-pd">
    <el-form ref="formRef" :model="formData" inline>
      <el-form-item prop="roleName" label="菜单名称：">
        <el-input v-model="formData.roleName" @keydown.enter="handleSearch"></el-input>
      </el-form-item>

      <el-form-item prop="enabledMark" label="是否启用：">
        <el-select v-model="formData.enabledMark">
          <el-option label="全部" value=""></el-option>
          <el-option label="已启用" :value="true"></el-option>
          <el-option label="未启用" :value="false"></el-option>
        </el-select>
      </el-form-item>

      <el-form-item>
        <el-button v-auth.disabled="'getSysRoleByPage'" type="primary" @click="handleSearch">搜索</el-button>
        <el-button v-auth.disabled="'getSysRoleByPage'" @click="handleReset">重置</el-button>
        <el-button type="success" @click="handleNewRole"> 新增角色 </el-button>
      </el-form-item>
    </el-form>

    <BaseTable v-loading="loading" :data="tableData" border height="630">
      <el-table-column prop="roleName" label="角色名称" min-width="120"></el-table-column>
      <el-table-column prop="roleTypeDes" label="角色类型" min-width="120"> </el-table-column>
      <el-table-column prop="createTime" label="创建时间" width="200"></el-table-column>
      <el-table-column prop="modifyTime" label="更新时间" width="200"></el-table-column>

      <el-table-column label="是否启用" align="center" width="90">
        <template #default="{ row }: { row: SysRoleByPageItemModel }">
          <el-tag :type="row.enabledMark ? 'success' : 'danger'">{{ row.enabledMark ? '启用' : '停用' }}</el-tag>
        </template>
      </el-table-column>

      <el-table-column label="操作" min-width="360">
        <template #default="{ row }: { row: OperaRow<SysRoleByPageItemModel> }">
          <el-button v-auth="['updateSysRolePermission', 'getSysNavigationForShow']" @click="handleEditRoleAuth(row)">
            权限
          </el-button>
          <el-button v-auth="'getSysRoleForEdit'" @click="handleEditRoleDetails(row)"> 编辑 </el-button>
          <el-button v-auth="'getSysManagerPageByRoleId'" type="success" @click="handleVisitBindUser(row)">
            查看已关联用户
          </el-button>
          <el-button v-auth="'deleteSysRole'" :loading="row.loading" type="danger" @click="handleDeleteRole(row)">
            删除
          </el-button>
        </template>
      </el-table-column>
    </BaseTable>

    <BasePagination v-model:current-page="currentPage" v-model:page-size="pageSize" :total="total" />
  </div>
</template>
