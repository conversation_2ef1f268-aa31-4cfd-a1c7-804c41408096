<script setup lang="ts">
import {
  DepositStatusConfig,
  DepositStatusOptions,
  HospitalizationDepositDataItem,
  ReqHospitalizationDepositOrderByPage
} from '@/api/dto/deposit-management.dto.ts'

const emits = defineEmits<{
  (e: 'search', data: { currentPage: number; pageSize: number; searchData: ReqHospitalizationDepositOrderByPage }): void
  (e: 'viewDetail', data: HospitalizationDepositDataItem): void
}>()

defineProps<{
  listData: HospitalizationDepositDataItem[]
  total: number
}>()

// 搜索表单
const searchFormData = reactive<ReqHospitalizationDepositOrderByPage>({} as ReqHospitalizationDepositOrderByPage)

// 分页
const paginationData = reactive({
  currentPage: 1,
  pageSize: 10
})

onMounted(() => {
  // 默认加载页面时搜索
  emitSearch()
})

// 搜索
function handleSearch() {
  paginationData.currentPage = 1
  emitSearch()
}

// 重置
function handleReset() {
  searchFormData.patientName = ''
  searchFormData.admissionNo = ''
  searchFormData.platformOrderNo = ''
  searchFormData.hisOrderNo = ''
  searchFormData.orderStatus = undefined
  emitSearch()
}

function emitSearch() {
  emits('search', {
    currentPage: paginationData.currentPage,
    pageSize: paginationData.pageSize,
    searchData: toRaw(searchFormData)
  })
}

function handleViewDetail(row: HospitalizationDepositDataItem) {
  emits('viewDetail', row)
}

function handleSizeChange() {
  // 切换每页条数时把当前页重置回 1
  paginationData.currentPage = 1
  emitSearch()
}

function handleCurrentChange() {
  emitSearch()
}
</script>

<template>
  <div>
    <el-form inline :model="searchFormData">
      <el-form-item label="平台单号：">
        <el-input
          v-model="searchFormData.platformOrderNo"
          class="input-container"
          placeholder="请输入"
          size="large"
          clearable
        />
      </el-form-item>
      <el-form-item label="医院单号：">
        <el-input
          v-model="searchFormData.hisOrderNo"
          class="input-container"
          placeholder="请输入"
          size="large"
          clearable
        />
      </el-form-item>
      <el-form-item label="住院人姓名：">
        <el-input
          v-model="searchFormData.patientName"
          class="input-container"
          placeholder="请输入"
          size="large"
          clearable
        />
      </el-form-item>
      <el-form-item label="住院号：">
        <el-input
          v-model="searchFormData.admissionNo"
          class="input-container"
          placeholder="请输入"
          size="large"
          clearable
        />
      </el-form-item>
      <el-form-item label="状态：">
        <el-select
          v-model="searchFormData.orderStatus"
          class="input-container"
          placeholder="请选择"
          size="large"
          clearable
        >
          <el-option v-for="item in DepositStatusOptions" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button
          v-auth.disabled="'hospitalizationDepositOrder_orders'"
          class="operation-btn"
          size="large"
          type="primary"
          @click="handleSearch"
        >
          搜索
        </el-button>
        <el-button
          v-auth.disabled="'hospitalizationDepositOrder_orders'"
          class="operation-btn"
          size="large"
          @click="handleReset"
        >
          重置
        </el-button>
      </el-form-item>
    </el-form>

    <BaseTable
      class="table-container"
      :data="listData"
      border
      width="100%"
      height="608"
      :tooltip-options="{ placement: 'top-start' }"
    >
      <el-table-column prop="platformOrderNo" label="平台单号" width="320" show-overflow-tooltip />
      <el-table-column prop="hisOrderNo" label="医院单号" width="240" show-overflow-tooltip />
      <el-table-column prop="patientName" label="住院人姓名" width="160" />
      <el-table-column prop="admissionNo" label="住院号" width="160" />
      <el-table-column prop="departmentName" label="住院科室" width="200" />
      <el-table-column prop="payFee" label="支付金额（元）" width="160">
        <template #default="scope">
          {{ scope.row.payFee?.toFixed(2) }}
        </template>
      </el-table-column>
      <el-table-column prop="orderStatus" label="状态" width="160">
        <template #default="scope">
          <el-tag :type="DepositStatusConfig[scope.row.orderStatus]?.tagType || DepositStatusConfig.default.tagType">
            {{ DepositStatusConfig[scope.row.orderStatus]?.label || DepositStatusConfig.default.label }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="payTime" label="支付时间" width="180" />

      <el-table-column label="操作" fixed="right" width="160">
        <template #default="scope">
          <el-button
            v-auth.disabled="'hospitalizationDepositOrder_detail'"
            size="small"
            @click="handleViewDetail(scope.row)"
          >
            详情
          </el-button>
        </template>
      </el-table-column>
    </BaseTable>

    <base-pagination
      v-model:current-page="paginationData.currentPage"
      v-model:page-size="paginationData.pageSize"
      :total="total"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />
  </div>
</template>

<style scoped lang="scss">
:deep(.el-form--inline .el-form-item) {
  margin-right: 22px;
}

:deep(.el-form .el-form-item__content) {
  min-width: 0 !important;
}

.input-container {
  width: 186px;
}

.table-container {
  margin: 12px 0 30px;
}

.operation-btn {
  width: 68px;
}
</style>
