<script setup lang="ts">
import { requestHospitalizationDepositOrderForDetail } from '@/api/deposit-management.api.ts'
import { ResHospitalizationDepositOrderForDetail } from '@/api/dto/deposit-management.dto.ts'
import { appMessage } from '@/hooks/useNaiveApi.ts'

/**
 * 住院预交金管理 / 住院预交金信息 详情
 */
import { ROUTER_PATH } from '@/router/router-path.ts'
import DepositInfo from '@/views/deposit-management/detail/components/deposit-info.vue'

const router = useRouter()
const route = useRoute()

// 已登记表格数据
const detailInfo = ref<ResHospitalizationDepositOrderForDetail>({} as ResHospitalizationDepositOrderForDetail)

let _hospitalizationDepositOrderId = ''

onMounted(() => {
  console.log('住院预交金列表传入 -> 详情：', route.query)
  if (!route.query.hospitalizationDepositOrderId) {
    console.error('获取住院预交金详情失败：未获取到住院预交金 ID')
    appMessage.error('获取住院预交金详情失败：住院预交金 ID 为空')
    return
  }
  _hospitalizationDepositOrderId = route.query.hospitalizationDepositOrderId as string
  requestDetailData(_hospitalizationDepositOrderId)
})

async function requestDetailData(hospitalizationDepositOrderId: string) {
  try {
    detailInfo.value = await requestHospitalizationDepositOrderForDetail({
      hospitalizationDepositOrderId
    })
    console.log('住院预交金详情：', detailInfo.value)
  } catch (e) {
    console.error('获取住院预交金详情失败：', e)
  }
}

function handleBack() {
  router.push({
    path: ROUTER_PATH.DEPOSIT_MANAGEMENT_LIST
  })
}
</script>

<template>
  <div class="page-container">
    <div class="header">
      <el-button class="header-back-btn" type="warning" @click="handleBack">返回</el-button>
    </div>

    <DepositInfo :detail-info="detailInfo" />
  </div>
</template>

<style scoped lang="scss">
.page-container {
  height: calc(100vh - 50px - 34px);
  display: flex;
  flex-direction: column;
}

.header {
  height: 50px;
  background: #f4f4f5;
  display: flex;
  justify-content: right;
  align-items: center;
  padding: 0 20px;
  position: sticky;
  flex: none;
  z-index: 2;

  & .header-back-btn {
    width: 68px;
    height: 36px;
  }
}
</style>
