import { AdmissionRegistrationStatus } from '@/api/dto/emun.dto.ts'

/**
 * 住院登记模块
 */

/** 字典数据项类型 */
export interface ResDictionaryDTO {
  /** 字典名称 */
  dictionaryDetailName: string
  /** 字典编码 */
  encode: string
}

/**
 * 枚举字典数据结构
 */
export interface EnumDictionaryItem {
  /** 描述文本 */
  describe: string
  /** 序号/编码值 */
  ordinal: string | number
}

/**
 * 枚举字典响应数据
 */
export interface ResAdmissionEnumsDTO {
  /** 婚姻状况枚举 */
  marital: EnumDictionaryItem[]
  /** 职业枚举 */
  career: EnumDictionaryItem[]
  /** 地区类型枚举 */
  placeType: EnumDictionaryItem[]
  /** 性别枚举 */
  gender: EnumDictionaryItem[]
  /** 联系人关系枚举 */
  contactRelationship: EnumDictionaryItem[]
}


// 分页获取列表请求参数
export interface ReqGetAdmissionRegistrationByPage {
  patientName?: string // 患者名称
  admissionNo?: string // 住院号
  status?: string // 状态：已处理、已提交登记
  patientIdNo?: string // 身份证号码
  hospitalizationDepartmentName?: string // 住院科室
  applyDepartmentName?: string // 开卡科室
  applyDoctorName?: string // 开卡医生
  indateStart?: string // 入院日期开始时间
  indateEnd?: string // 入院日期结束时间
  applyTimeStart?: string // 开卡时间开始时间
  applyTimeEnd?: string // 开卡时间结束时间
  page: number // 当前页
  rows: number // 每页的记录数
  keyword?: string // 关键词搜索
  sidx?: string // 排序字段
  sord?: string // 升降序
}

// 入院登记信息
export interface AdmissionRegistration {
  admissionRegistrationId: string // 入院登记ID
  medicalcardId: string // 卡号ID
  hospitalizationDepartmentName: string // 住院科室名
  applyDepartmentName: string // 开卡科室名
  applyDoctorName: string // 开卡医生名
  applyTime: string // 开卡时间
  patientName: string // 患者名称
  genderType: string // 性别类型（1.男 2.女 3.未知）
  patientIdNo: string // 患者证件卡号
  mobilephone: string // 联系手机号码
  birthday: string // 出生日期
  insuranceType: string // 医保类型类别
  insuranceName: string // 医保类型名称
  maritalStatus: string // 婚姻状况
  nationality: string // 国籍
  nation: string // 名族
  birthPlace: string // 出生地
  birthPlaceDetail?: string // 出生地详情,从出生地中拆分出来的详细地址
  domicileAddress: string // 户口地址
  domicileAddressDetail?: string // 户口地址详情,从户口地址中拆分出来的详细地址
  currentResidentialAddress: string // 现居住地址
  currentResidentialAddressDetail?: string // 现居住地址详情
  employerName: string // 工作单位名称
  career: string // 职业
  contactName: string // 紧急联系人名字
  contactRelationship: string // 紧急联系人关系
  contactMobilephone: string // 紧急联系人电话
  contactAddress: string // 紧急联系地址
  contactAddressDetail?: string // 紧急联系地址详情,从紧急联系地址中拆分出来的详细地址
  description: string // 业务描述
  remark: string // 备注
  createTime: string // 创建时间
  deleteMark: boolean // 是否删除
  admissionNo: string // 住院号
  status: AdmissionRegistrationStatus // 状态：已处理、已提交登记
  admissionTime: string // 入院时间
  registrationTime: string // 登记时间
  processTime: string // 处理时间
  depositPaid: string // 已缴纳押金
  regNo: string // 预入院流水号
  age: string // 年龄
  inpno: string // 记帐号
  curdptnm: string // 手腕带打印的科室
}

export interface AdmissionRegistrationExtra extends AdmissionRegistration {
  birthPlaceDetail?: string // 出生地详情
  domicileAddressDetail?: string // 户口地址详情
  currentResidentialAddressDetail?: string // 现居住地址详情
  contactAddressDetail?: string // 紧急联系人地址详情
  isInpatientFormPrintingLoading?: boolean // 住院申请单打印中
  isWristbandPrintingLoading?: boolean // 手腕带打印中
  idCardImgurlFront?: string // 身份证国徽页
  idCardImgurlBack?: string // 身份证头像页
  indate?: string // 入院日期
  state?: string // 入院情况(0未办理入院 1已提交登记信息 2已办理入院)
  admway?: string // 入院途径
  diag?: string // 门诊诊断
  drname?: string // 门诊医生
  setmeth?: string // 结算方式
  pricetype?: string // 价类
  rate?: number // 自付
  fluctuationStandard?: string // 起伏标准
  medicalCertificateNumber?: string // 医疗证号
  bedStandard?: string // 床位标准
  medicalRegistrationNumber?: string // 就医登记号
  isCommunityPatients?: boolean // 是否社区病人
  healthCardNumber?: string // 健康卡号
  codeOfInsuredPlace?: string // 参保地编码
  prepay?: number // 押金余额
  notes?: string // 备注
}

// 分页获取列表响应
export interface ResGetAdmissionRegistrationByPage {
  data: AdmissionRegistration[] // 实体列表
  pageIndex: number // 当前页
  pageSize: number // 每页记录数
  recordCount: number // 总记录数
  pageCount: number // 总页数
}

// 获取详情请求参数
export interface ReqGetAdmissionRegistrationForDetail {
  admissionRegistrationId: string // 入院登记ID
}

// 获取详情响应
export type ResGetAdmissionRegistrationForDetail = AdmissionRegistrationExtra

// 接受处理请求参数
export interface ReqAcceptAdmissionRegistration {
  admissionRegistrationId: string // 入院登记ID
  admissionTime: string // 入院时间
}

// 接受处理响应
export interface ResAcceptAdmissionRegistration {
  msg: string // 处理结果消息
}

// 更新请求参数
export interface ReqUpdateAdmissionRegistration {
  admissionRegistrationId: string // 入院登记ID
  mobilephone: string // 联系手机号码
  insuranceType: string // 医保类型类别
  maritalStatus: string // 婚姻状况
  nationality: string // 国籍
  nation: string // 民族
  birthPlace: string // 出生地
  domicileAddress: string // 户口地址
  currentResidentialAddress: string // 现居住地址
  employerName: string // 工作单位名称
  career: string // 职业
  contactName: string // 紧急联系人名字
  contactRelationship: string // 紧急联系人关系
  contactMobilephone: string // 紧急联系人电话
  contactAddress: string // 紧急联系地址
  hospitalizationDepartmentName: string // 住院科室
  patientName: string // 患者姓名
  patientIdNo: string // 患者证件卡号
  genderType: string // 性别类型（1.男 2.女 3.未知）
  birthday: string // 出生日期
  indate: string // 入院日期
  idCardImgurlFront: string // 身份证头像页
  idCardImgurlBack: string // 身份证国徽页
}

// 更新响应
export interface ResUpdateAdmissionRegistration {
  msg: string // 处理结果消息
}

// 入院卡打印请求参数
export interface ReqPrintingOfApplicationForm {
  admissionRegistrationId: string // 入院登记ID
}

// 入院卡打印响应
export interface ResPrintingOfApplicationForm {
  admissionNo: string // 住院号
  admway: string // 入院途径
  birthday: string // 出生日期
  career: string // 职业
  contactAddress: string // 联系人地址
  contactMobilephone: string // 联系人电话
  contactName: string // 联系人名字
  contactRelationship: string // 联系人关系
  currentResidentialAddress: string // 现居住地址
  diag: string // 门诊诊断（目前返回的是西医诊断）
  doctorName: string // 医生名字
  doctorNo: string // 医生工号
  domicileAddress: string // 户口地址
  employerName: string // 公司名称
  genderType: string // 性别
  inpno: string // 记账号
  insuranceType: string // 保险类型
  maritalStatus: string // 婚姻状况
  mobilephone: string // 电话
  patientIdNo: string // 患者身份证
  patientName: string // 患者姓名
  age: string // 年龄
  nationality: string // 国籍
  nation: string // 民族
  birthPlace: string // 出生地
}

// 手腕带打印请求参数
export interface ReqPrintingOfWristStrap {
  admissionRegistrationId: string // 入院登记ID
}

// 手腕带打印响应
export interface ResPrintingOfWristStrap {
  age: number // 年龄
  curdptnm: string // 科别
  inpno: string // 记账号
  name: string // 姓名
  patno: string // 住院号
  sex: string // 性别
}
