import type { RunTimeOptions } from '@/runTime'
import type { RouterGuards } from './guards'
import { isFunction } from 'lodash-es'

/**
 * @description: 加载全局路由守卫
 */
export const loadGuards = (guards: RouterGuards, options: RunTimeOptions) => {
  const { beforeEach, afterEach } = guards
  const { router } = options
  beforeEach &&
    beforeEach.forEach((beforeEachGuards) => {
      if (isFunction(beforeEachGuards)) {
        router.beforeEach((to, from, next) => beforeEachGuards(to, from, next, options))
      }
    })
  afterEach &&
    afterEach.forEach((afterEachGuards) => {
      if (isFunction(afterEachGuards)) {
        router.afterEach((to, from) => afterEachGuards(to, from, options))
      }
    })
}
