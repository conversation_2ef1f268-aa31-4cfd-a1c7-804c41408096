import { defineStore } from 'pinia'
import { useDark } from '@vueuse/core'

interface AppConfig {
  appName: string
  appTitle: string
  version: string
  baseURL: string
}

interface UseGloablStoreState {
  appConfig: AppConfig
  isDark: ReturnType<typeof useDark>
}

export const useGlobalStore = defineStore('globalStore', {
  state(): UseGloablStoreState {
    return {
      appConfig: {
        appName: '',
        appTitle: '',
        version: '',
        baseURL: '',
      },

      isDark: useDark(),
    }
  },

  actions: {},
})
