<script setup lang="ts">
import { useMountDialog } from '@/hooks/useMountDialog.ts'
import RefundAmountDialog from './refund-amount-dialog.vue'
import { formatPrice } from '@/utils'
import type { SummaryMethodProps } from '@/components/common-highlight-table/types'
import type { InvoiceAdjustmentsItem } from '@/api/dto/discharge-settlement/invoice-adjustments.dto'

const { open: openRefundAmountDialog } = useMountDialog(RefundAmountDialog)

defineProps<{
  invoiceAdjustmentsData: InvoiceAdjustmentsItem[]
}>()

const currentSelectedItem = ref<InvoiceAdjustmentsItem>({} as InvoiceAdjustmentsItem)

// 全退
function handleAllRefund() {
  console.log('全退')
  // TODO 传参按实际对接情况而定
  openRefundAmountDialog({
    confirmCallback: async (data: any) => {
      console.log('全退 确认回调：', data)
    }
  })
}

// 当前行改变
function handleCurrentChange(row: InvoiceAdjustmentsItem) {
  currentSelectedItem.value = row
}

const getSummaries = (param: SummaryMethodProps) => {
  if (param.columns.length === 0) {
    return []
  }
  const { data } = param
  const sums: (string | VNode)[] = []

  // 项目合计金额
  const totalAmount = formatPrice(data.reduce((acc, item) => acc + item.amount, 0))

  sums[0] = ''
  sums[1] = `总计：`
  sums[2] = `${totalAmount}元`
  return sums
}
</script>

<template>
  <div class="invoice-info-panel">
    <CommonHighlightTable
      class="invoice-info-table"
      :data="invoiceAdjustmentsData"
      :height="440"
      show-finger-icon
      show-summary
      :get-summaries="getSummaries"
      @current-change="handleCurrentChange"
    >
      <el-table-column prop="no" label="No." width="120" align="center" />
      <el-table-column prop="costType" label="费用类别" width="560" />
      <el-table-column prop="amount" label="金额" width="280" align="right" />
      <el-table-column />
    </CommonHighlightTable>

    <div class="invoice-info-operation-container">
      <div class="common-panel-title">结算操作</div>
      <div class="fee-operation-button" @click="handleAllRefund">全退</div>

      <div class="common-panel-title">注销、退费原因</div>
      <div class="reason-input-container">
        {{ currentSelectedItem.reason }}
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.invoice-info-panel {
  display: flex;
  gap: 20px;

  :deep(.el-table__footer) {
    // 第三列的效果
    .el-table__cell {
      text-align: end;
    }
  }
}

.invoice-info-table {
  flex: 1;
  width: 1px;
}

.invoice-info-operation-container {
  display: flex;
  gap: 20px;
  flex-direction: column;
  min-width: 320px;

  .fee-operation-button {
    height: 40px;
    width: 320px;
    line-height: 40px;
    text-align: center;
    border-radius: 4px;
    background: #fff;
    border: 1px solid var(--el-border-color-primary);
    color: var(--el-color-primary);
    cursor: pointer;
  }

  .reason-input-container {
    width: 320px;
    height: 160px;
    border-radius: 4px;
    background: #fafafa;
    border: 1px solid #dcdfe6;
    padding: 10px;
  }
}
</style>
