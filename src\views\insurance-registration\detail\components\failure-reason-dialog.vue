<script setup lang="ts">
import { FormInstance, FormRules } from 'element-plus'

interface RuleForm {
  reason: string
}

defineProps<{
  name: string
  hospitalizationNumber: string
}>()

const emits = defineEmits<{
  (e: 'confirm', data: { reason: string; done: () => void }): void
}>()

const failureReasonInputFormRef = ref<FormInstance>()
// 登记失败原因
const failureReasonInputForm = reactive({
  reason: ''
})
const failureReasonDialogVisible = defineModel({ default: false })
const failureReasonInputFormRules = reactive<FormRules<RuleForm>>({
  reason: [{ required: true, message: '请输入原因', trigger: 'blur' }]
})

// 登记失败原因弹窗 - 点击确认
async function handleConfirmFailureReason(done: (flag?: boolean) => void, formEl: FormInstance | undefined) {
  if (!formEl) return
  await formEl.validate((valid, fields) => {
    if (valid) {
      // 提交登记失败原因
      emits('confirm', { reason: failureReasonInputForm.reason, done })
    } else {
      console.log('提交登记失败 表单校验不通过：', fields)
    }
  })
}
</script>

<template>
  <CommonFormDialog
    v-model="failureReasonDialogVisible"
    title="登记失败"
    width="600px"
    show-loading
    :disabled-confirm="!failureReasonInputForm.reason"
    @confirm="handleConfirmFailureReason($event, failureReasonInputFormRef)"
  >
    <div class="patient-info">住院人姓名：{{ name }} 丨 住院号：{{ hospitalizationNumber }}</div>
    <el-form
      ref="failureReasonInputFormRef"
      class="mt-20"
      size="large"
      :model="failureReasonInputForm"
      :rules="failureReasonInputFormRules"
    >
      <el-form-item label="失败原因：" prop="reason">
        <el-input
          v-model="failureReasonInputForm.reason"
          type="textarea"
          clearable
          placeholder="请输入"
          :autosize="{ minRows: 1, maxRows: 10 }"
        />
      </el-form-item>
    </el-form>
  </CommonFormDialog>
</template>

<style scoped lang="scss">
.patient-info {
  height: 40px;
  border-radius: 4px;
  background: #ecf5ff;
  border: 1px solid #d9ecff;
  color: #1890ef;
  line-height: 40px;
  padding: 0 16px;
}

.mt-20 {
  margin-top: 20px;
}
</style>
