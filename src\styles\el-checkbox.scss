.el-checkbox {
  .el-checkbox__input {
    .el-checkbox__inner {
      width: 20px;
      height: 20px;
    }

    &.is-checked .el-checkbox__inner {
      background-color: var(--el-color-primary);
      border-color: var(--el-color-primary);

      &::after {
        transform: rotate(45deg) scale(1.8) translate(2.2px, 0);
      }
    }

    &.is-indeterminate .el-checkbox__inner {
      background-color: var(--el-color-danger);
      border-color: var(--el-color-danger);
      &::before {
        height: 3px;
        top: 8px;
        transform: scale(0.8);
      }
    }
  }
}
