/**
 * 租赁订单管理
 */

/**
 * 退款状态
 */
export enum RentalRefundStatus {
  /* 待退款 */
  PENDING = 0,
  /* 退款成功 */
  SUCCESS = 1,
  /* 退款失败 */
  FAILED = 2
}

// 退款状态映射配置
export const RentalRefundStatusConfig = {
  [RentalRefundStatus.PENDING]: { label: '待退款', tagType: '' },
  [RentalRefundStatus.SUCCESS]: { label: '退款成功', tagType: '' },
  [RentalRefundStatus.FAILED]: { label: '退款失败', tagType: '' },
  default: { label: '-', tagType: '' }
}

/**
 * 订单支付状态
 */
export enum RentalPayStatus {
  /* 待支付 */
  PENDING = 0,
  /* 支付成功 */
  SUCCESS = 1,
  /* 支付失败 */
  FAILED = 2,
  /* 支付异常 */
  EXCEPTION = 3
}

/**
 * 订单支付状态映射配置
 */
export const RentalPayStatusConfig = {
  [RentalPayStatus.PENDING]: { label: '待支付', tagType: '' },
  [RentalPayStatus.SUCCESS]: { label: '支付成功', tagType: '' },
  [RentalPayStatus.FAILED]: { label: '支付失败', tagType: '' },
  [RentalPayStatus.EXCEPTION]: { label: '支付异常', tagType: '' },
  default: { label: '-', tagType: '' }
}

/**
 * 审核状态
 */
export enum AuditStatus {
  /* 待审核 */
  PENDING = 0,
  /* 审核通过 */
  APPROVED = 1,
  /* 审核不通过 */
  REJECTED = 2
}

// 审核状态映射配置
export const AuditStatusConfig = {
  [AuditStatus.PENDING]: { label: '待审核', tagType: 'warning' },
  [AuditStatus.APPROVED]: { label: '审核通过', tagType: 'success' },
  [AuditStatus.REJECTED]: { label: '审核不通过', tagType: 'danger' },
  default: { label: '-', tagType: 'info' }
}

/**
 * 租赁订单 - 状态
 */
export enum RentalOrderStatus {
  /* 已预约 */
  RESERVED = 0,
  /* 待补缴 */
  PENDING_PAYMENT = 1,
  /* 待支付 */
  AWAITING_PAYMENT = 2,
  /* 陪护中 */
  ESCORTING = 3,
  /* 已取消 */
  CANCELLED = 4,
  /* 已完成 */
  COMPLETED = 5
}

// 租赁订单 - 状态映射配置
export const RentalOrderStatusConfig = {
  [RentalOrderStatus.RESERVED]: { label: '已预约', tagType: 'success' },
  [RentalOrderStatus.PENDING_PAYMENT]: { label: '待补缴', tagType: 'warning' },
  [RentalOrderStatus.AWAITING_PAYMENT]: { label: '待支付', tagType: 'warning' },
  [RentalOrderStatus.ESCORTING]: { label: '陪护中', tagType: 'success' },
  [RentalOrderStatus.CANCELLED]: { label: '已取消', tagType: 'info' },
  [RentalOrderStatus.COMPLETED]: { label: '已完成', tagType: 'info' },
  default: { label: '-', tagType: 'info' }
}

/**
 * 租赁订单 - 状态 可选项
 */
export const RentalOrderStatusOptions = Object.entries(RentalOrderStatusConfig)
  .filter(([key]) => key !== 'default')
  .map(([value, config]) => ({
    label: config.label,
    value: parseInt(value)
  }))

/**
 * 租赁订单管理 - 列表 - 搜索数据类型
 */
export interface ReqRentalOrdersByPage {
  /* 平台单号 */
  platFormOrderNo: string
  /* 住院人姓名 */
  patientName: string
  /* 住院科室 */
  department: string
  /** 证件号 */
  patientIdNo: string
  /** 状态 */
  orderStatus?: number
  /** 订单修改 tab 时传入 0 */
  tabNo?: number
  /* 当前页 */
  page: number
  /* 每页的记录数 */
  rows: number
}

/**
 * 租赁订单管理 - 搜索 - 响应
 */
export interface ResRentalOrdersByPage {
  data: RentalOrderItem[] // 实体列表
  pageIndex: number // 当前页
  pageSize: number // 每页记录数
  recordCount: number // 总记录数
  pageCount: number // 总页数
}

/**
 * 租赁订单管理 列表 item 数据
 */
export interface RentalOrderItem {
  /* ID */
  inpatientCaregiverId: string
  /* 住院人姓名 */
  patientName: string
  /** 证件号 */
  patientIdNo: string
  /* 住院科室 */
  department: string
  /* 租赁时间 */
  leaseTime: string
  /* 订单金额（元） */
  payFee: number
  /* 下单时间 */
  createTime: string
  /* 状态 */
  orderStatus: number
  /* 陪护类型 */
  serverType: string
  /* 平台单号 */
  platformOrderNo: string
  /* 陪护人名称 */
  caregiverPersonName: string
  /* 是否有修改订单待审核 */
  isUpdateReviewing: boolean
  /* 是否有换人申请待审核 */
  isReplacePersonReviewing: boolean
}

/**
 * 租赁信息详情 请求
 */
export interface ReqRentalOrderDetail {
  /* ID */
  inpatientCaregiverId: string
}

/**
 * 租赁信息详情 响应
 */
export interface ResRentalOrderDetail {
  /* ID */
  inpatientCaregiverId: string
  /* 陪护床名称 */
  serverName: string
  /* 住院人姓名 */
  patientName: string
  /* 证件号 */
  patientIdNo: string
  /* 住院科室 */
  department: string
  /* 入院时间 */
  indate: string
  /* 租赁时间 */
  leaseTime: string
  /* 联系人姓名 */
  contactName: string
  /* 联系人电话 */
  contactPhone: string
  /* 订单金额 */
  payFee: number
  /* 补缴金额 */
  pendingPayment: number
  /* 退费金额 */
  pendingRefund: number
  /* 价格 */
  serverPrice: number
  /* 支付时间 */
  payTime: string
  /* 支付状态 */
  payStatus: number
  /* 状态 */
  orderStatus: number
  /* 服务类型 */
  serverType: string
  /* 业务类型 */
  businessType: string
  /* 支付流水号 */
  payTradeNo: string
  /* 平台订单号 */
  platformOrderNo: string
  /* 陪护人姓名 */
  caregiverPersonName: string
  /* 陪护人年龄 */
  caregiverPersonAge: number
  /* 陪护人性别 */
  caregiverPersonGender: string
  /* 陪护人身份证 */
  caregiverPersonIdCard: string
  /* 陪护人电话 */
  caregiverPersonPhone: string
  /* 确认时间 */
  confirmTime?: string
  /* 完成时间 */
  completeTime?: string
  /* 取消时间 */
  cancelTime?: string
  /* 服务评价（1-5，表示星数） */
  serverEvaluate?: number
  /* 评价时间 */
  evaluateTime?: string
  /* 评价内容 */
  evaluateContent?: string
  /* 订单修改信息 */
  orderUpdateInfoDTOList?: OrderModifyInfo[]
  /* 换人申请信息 */
  substitutionInfoDTOList?: ChangeEscortInfo[]
}

/* 租赁信息详情 - 订单修改信息 */
export interface OrderModifyInfo {
  /* ID */
  inpatientCaregiverId: string
  /* 修改类型 */
  updateServerType: string
  /* 价格 */
  serverPrice: number
  /* 租赁时间 */
  leaseTime: string
  /* 费用类型 */
  feeType: string
  /* 金额 */
  totalAmount: number
  /* 状态 */
  reviewStatus: number
  /* 审核时间 */
  reviewTime: string
}

/* 租赁信息详情 - 换人申请信息 */
export interface ChangeEscortInfo {
  /* ID */
  inpatientCaregiverId: string
  /* 换人原因 */
  substitutionReason: string
  /* 提交时间 */
  submitTime: string
  /* 状态 */
  reviewStatus: number
  /* 审核时间 */
  reviewTime: string
}

/* 补缴支付信息 - 退款记录 */
export interface RefundRecord {
  /* 平台退款订单号 */
  outRefundNo: string
  /* 退款状态 */
  refundStatus: number
  /* 退款时间 */
  successTime?: string
  /* 退款金额 */
  refund: number
  /* 费用类型 */
  feeType: string
  /* 创建时间 */
  createTime: string
}

/* 补缴支付信息 - 补缴记录 */
export interface SupplementaryPaymentRecord {
  /* 补缴金额 */
  payFee: number
  /* 支付时间 */
  payTime?: string
  /* 支付流水号 */
  payTradeNo?: string
  /* 支付状态 */
  payStatus: boolean
  /* 费用类型 */
  feeType: string
  /* 创建时间 */
  createTime: string
}

/* 补缴支付信息 */
export interface RentalPaymentInfo {
  /* 补缴记录 */
  supplementaryPaymentRecordList: SupplementaryPaymentRecord[]
  /* 退款记录 */
  refundRecordList: RefundRecord[]
}

/* 服务类型信息 */
export interface ServiceTypeInfo {
  /* 服务类型 */
  serverType: string
  /* 服务价格 */
  serverPrice: number
}

/* 科室信息 */
export interface ResRentalQuerydeptDTO {
  /* 科室代码 */
  deptcode: string
  /* 科室名称 */
  deptname: string
}

/* 选择住院人请求 */
export interface ReqSelectInpatientByPage {
  /* 当前页 */
  page: number
  /* 每页记录数 */
  rows: number
  /* 姓名关键字模糊查询 */
  keyword?: string
  /* 住院号 */
  patno?: string
  /* 科室代码，从选择科室列表后得到值 */
  deptcode: string
}

/* 选择住院人列表项 */
export interface ResSelectInpatientByPageDTO {
  /* 床号 */
  bedno?: string
  /* 姓名 */
  name: string
  /* 证件号 */
  certno: string
  /* 科室 */
  curdptnm: string
  /* 医生 */
  curdname: string
}

/* 选择住院人列表响应 */
export interface ResSelectInpatientByPage {
  /* 结果列表 */
  data: ResSelectInpatientByPageDTO[]
  pageIndex: number
  pageSize: number
  recordCount: number
  pageCount: number
}

/* 创建订单信息 */
export interface CreateRentalOrderInfo {
  /* 住院人姓名 */
  patientName: string
  /* 证件号 */
  patientIdNo: string
  /* 住院科室 */
  department: string
  /* 开始时间 */
  startTime: string
  /* 结束时间 */
  endTime: string
  /* 联系人姓名 */
  contactName: string
  /* 联系人电话 */
  contactPhone: string
  /* 核算金额 */
  payFee: number | undefined
  /* 服务类型 */
  serverType: string
  /* 服务价格 */
  serverPrice: number
}

/* 确认租赁-查询陪护人请求 */
export interface ReqConfirmLeaseOfCaregiverPerson {
  /* 姓名 */
  name?: string
  /* 是否陪护中 */
  isCaregiver?: boolean
  /* 当前页 */
  page: number
  /* 每页记录数 */
  rows: number
}

/* 确认租赁-陪护人列表项 */
export interface ConfirmLeaseOfCaregiverPerson {
  /* ID */
  inpatientCaregiverPersonId: string
  /* 姓名 */
  name: string
  /* 性别 */
  gender: string
  /* 年龄 */
  age: number
  /* 电话 */
  phone: string
  /* 是否陪护中 */
  isCaregiver: boolean
}

/* 确认租赁-陪护人列表响应 */
export interface ResConfirmLeaseOfCaregiverPerson {
  data: ConfirmLeaseOfCaregiverPerson[]
  pageIndex: number
  pageSize: number
  recordCount: number
  pageCount: number
}

/* 确认租赁请求 */
export interface ReqConfirmLease {
  /* ID */
  inpatientCaregiverId: string
  /* 陪护人ID */
  inpatientCaregiverPersonId: string
}

/* 修改审核信息响应 */
export interface ResModifyReviewInfo {
  /* 患者姓名 */
  patientName: string
  /* 患者身份证号 */
  patientIdNo: string
  /* 修改ID */
  inpatientCaregiverUpdateId: string
  /* 原服务类型 */
  originalServerType: string
  /* 原价格 */
  originalPrice: number
  /* 原租赁时间 */
  originalLeaseTime: string
  /* 原支付金额 */
  originalPayFee: number
  /* 新服务类型 */
  newServerType: string
  /* 新价格 */
  newPrice: number
  /* 新租赁时间 */
  newLeaseTime: string
  /* 补缴金额 */
  pendingPayment: number
  /* 退费金额 */
  pendingRefund: number
}

/* 修改审核请求 */
export interface ReqModifyReview {
  /* 修改ID */
  inpatientCaregiverUpdateId: string
  /* ID */
  inpatientCaregiverId: string
  /* 审核操作 */
  reviewOperate: boolean
}

/* 换人审核信息响应 */
export interface ResPersonnelReplacementReviewInfo {
  /* ID */
  inpatientCaregiverId: string
  /* 原因 */
  substitutionReason: string
  /* 提交时间 */
  createTime: string
}

/* 换人审核请求 */
export interface ReqPersonnelReplacementReview {
  /* 审核操作 */
  reviewOperate: boolean
  /* 陪护人ID */
  inpatientCaregiverPersonId: string
  /* ID */
  inpatientCaregiverId: string
}
