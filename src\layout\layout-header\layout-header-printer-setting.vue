<script lang="ts" setup>
import { useFormHook } from '@/hooks/useForm.ts'
import { appMessage } from '@/hooks/useNaiveApi'
import { PrintMode, usePrinterStore } from '@/stores/printer.store.ts'
import { QuestionFilled } from '@element-plus/icons-vue'
import { FormRules } from 'element-plus'
import { storeToRefs } from 'pinia'

const popupVisible = ref(false)

const printerStore = usePrinterStore()

const { formRef } = useFormHook()

// 已连接的打印机列表
const printerList = ref<any[]>([])

// 打印客户端连接状态
const { printerClientConnected, silentPrinterConfig, printMode } = storeToRefs(printerStore)

// 打印方式，静默打印模式 | 打印预览模式
const printModeSelected = ref<PrintMode>()

// 是否显示设置右上角的异常图标，在静默打印情况下，未连接 或者 已连接但没有配置打印机的时候显示
const showPrinterWarningIcon = computed(() => {
  return (
    printMode.value === PrintMode.SILENT &&
    (!printerClientConnected.value ||
      !silentPrinterConfig.value.inpatientFormPrinter ||
      !silentPrinterConfig.value.wristbandPrinter)
  )
})

const printerSettingForm = reactive({
  // 入院卡打印机
  inpatientFormPrinter: '',
  // 手腕带打印机
  wristbandPrinter: ''
})

watch(
  () => printerClientConnected.value,
  (isConnect) => {
    if (isConnect) {
      // 打印机状态从 未连接 变为 已连接，获取 打印机列表
      setTimeout(async () => {
        // 加一点延迟，不然在未连接变化到已连接时候会出现打印机列表为空的情况
        await updateSilentPrinterConfig()
        console.log('打印客户端连接状态发生变化，刷新打印机列表：', printerList.value)
      }, 200)
    } else {
      printerList.value = []
      formRef.value!.resetFields()
    }
  }
)

watch(printModeSelected, (value) => {
  nextTick(() => {
    if (value === PrintMode.SILENT) {
      console.log('选择静默打印，设置自动连接')
      printerStore.setPrinterClientStatusCallback()
    } else {
      console.log('选择打印预览，关闭自动连接')
      printerStore.closePrinterClient()
    }
  })
})

const rules = reactive<FormRules>({
  inpatientFormPrinter: [{ required: true, message: '请选择入院卡打印机', trigger: 'change' }],
  wristbandPrinter: [{ required: true, message: '请选择手腕带打印机', trigger: 'change' }]
})

// 弹窗点击确定
async function handlePopupConfirm(done: (keepVisible?: boolean) => void) {
  if (!printModeSelected.value) {
    appMessage.warning('请选择打印模式')
    return
  }
  console.log('打印机配置====', printModeSelected.value, PrintMode.SILENT, PrintMode.PREVIEW)

  if (printModeSelected.value === PrintMode.SILENT) {
    const isValid = await formRef.value!.validate(() => {})
    if (!isValid) {
      if (printerStore.printerClientConnected) {
        appMessage.error('请完善打印机设置')
      } else {
        appMessage.error('请等待打印客户端连接后完善打印机设置')
      }
      done(true)
      return
    }

    // 保存静默打印配置
    printerStore.setSilentPrinterConfig(JSON.parse(JSON.stringify(printerSettingForm)))
  } else {
    printerStore.closePrinterClient()
    console.log('选择打印预览模式，则关闭打印客户端连接')
  }

  printerStore.setPrintMode(printModeSelected.value)
  appMessage.success('打印机设置成功')
  // 关闭弹窗
  done()
}

async function updateSilentPrinterConfig() {
  printerList.value = await getPrinterList()
  console.log('打印机列表：', printerList.value, printerStore.silentPrinterConfig.inpatientFormPrinter)

  printerSettingForm.inpatientFormPrinter = printerStore.silentPrinterConfig.inpatientFormPrinter
  printerSettingForm.wristbandPrinter = printerStore.silentPrinterConfig.wristbandPrinter
}

// 打开弹窗
async function handleOpenPrintSetting() {
  popupVisible.value = true
  await nextTick()
  formRef.value!.resetFields()
  printModeSelected.value = printMode.value

  if (printModeSelected.value === PrintMode.SILENT) {
    // 静默模式才更新打印机数据
    await updateSilentPrinterConfig()
  }
}

async function getPrinterList() {
  try {
    return (await printerStore.getPrinterList()) as any[]
  } catch (e) {
    console.error(e)
    appMessage.warning(`获取打印机列表失败：${e}`)
    return []
  }
}
</script>

<template>
  <div class="printer-setting">
    <el-tooltip content="打印机设置" placement="bottom">
      <div class="printer-setting-icon-wrapper" @click="handleOpenPrintSetting">
        <svg-icon name="svg-ic_printer" class="printer-setting-icon" />
        <svg-icon v-if="showPrinterWarningIcon" name="svg-ic_warning" class="printer-setting-icon-warning" />
      </div>
    </el-tooltip>

    <CommonFormDialog v-model="popupVisible" title="打印机设置" width="600px" showLoading @confirm="handlePopupConfirm">
      <div class="print-mode">
        <span>打印模式：</span>
        <el-radio-group v-model="printModeSelected">
          <el-radio :value="PrintMode.PREVIEW" size="large">打印预览</el-radio>
          <el-radio :value="PrintMode.SILENT" size="large">
            <div>
              静默打印
              <el-tooltip content="该模式需安装打印客户端" placement="right">
                <el-icon>
                  <QuestionFilled />
                </el-icon>
              </el-tooltip>
            </div>
          </el-radio>
        </el-radio-group>
      </div>

      <el-divider v-show="printModeSelected === PrintMode.SILENT" />

      <el-form
        ref="formRef"
        v-show="printModeSelected === PrintMode.SILENT"
        :rules="rules"
        size="large"
        :model="printerSettingForm"
        label-width="130"
        label-position="left"
      >
        <el-form-item label="打印客户端状态：">
          <el-text v-if="printerClientConnected" type="success">已连接</el-text>
          <el-text v-else type="danger">等待连接中...</el-text>
        </el-form-item>
        <el-form-item label="入院卡打印机：" required prop="inpatientFormPrinter">
          <el-select v-model="printerSettingForm.inpatientFormPrinter" placeholder="请选择 A4 纸打印机" clearable>
            <el-option v-for="printerInfo in printerList" :label="printerInfo.displayName" :value="printerInfo.name" />
          </el-select>
        </el-form-item>
        <el-form-item label="手腕带打印机：" required prop="wristbandPrinter">
          <el-select v-model="printerSettingForm.wristbandPrinter" placeholder="请选择手腕带专用打印机" clearable>
            <el-option v-for="printerInfo in printerList" :label="printerInfo.displayName" :value="printerInfo.name" />
          </el-select>
        </el-form-item>
      </el-form>
    </CommonFormDialog>
  </div>
</template>

<style lang="scss" scoped>
.printer-setting {
  height: 22px;
  width: 22px;
}

.printer-setting-icon-wrapper {
  height: 100%;
  width: 100%;
  position: relative;
}

.printer-setting-icon {
  height: 100% !important;
  width: 100% !important;
  cursor: pointer;

  &-warning {
    color: #f56c6c;
    height: 16px !important;
    width: 16px !important;
    position: absolute;
    top: -8px;
    right: -10px;
  }
}

.print-mode {
  display: flex;
  align-items: center;
}
</style>
