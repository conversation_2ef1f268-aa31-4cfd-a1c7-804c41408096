import type { ApiFunc } from 'axios'
import { authRequest } from '../utils.api'
import { ROLE_TYPE_EUNM } from '../dto/system/roles-manage.dto'
import type {
  AddSysRoleInput,
  DeleteSysRoleInput,
  GetSysManagerPageByRoleIdInput,
  GetSysManagerPageByRoleIdOutput,
  GetSysNavigationForShowInput,
  GetSysNavigationForShowOutput,
  GetSysRoleByPageInput,
  GetSysRoleByPageOutput,
  GetSysRoleForEditInput,
  GetSysRoleForEditOutput,
  UpdateSysRoleInput,
  UpdateSysRolePermissionInput,
} from '../dto/system/roles-manage.dto'

/**角色类型 */
export const roleTypeOptions = [
  { label: '系统用户', value: ROLE_TYPE_EUNM.SYSTEM_MANAGE },
  { label: '超级用户', value: ROLE_TYPE_EUNM.SUPER_MANAGE },
]

/**角色分页 */
export const getSysRoleByPageApi: ApiFunc<GetSysRoleByPageInput, GetSysRoleByPageOutput> = (data, options) => {
  return authRequest({ url: 'getSysRoleByPage', data, ...options })
}

/**获取角色拥有的菜单列表 */
export const getSysNavigationForShowApi: ApiFunc<GetSysNavigationForShowInput, GetSysNavigationForShowOutput> = (
  data,
  options
) => {
  return authRequest({ url: 'getSysNavigationForShow', data, ...options })
}

/**修改角色拥有的菜单列表 */
export const updateSysRolePermissionApi: ApiFunc<UpdateSysRolePermissionInput, undefined> = (data, options) => {
  return authRequest({ url: 'updateSysRolePermission', data, ...options })
}

/**添加角色 */
export const addSysRoleApi: ApiFunc<AddSysRoleInput, undefined> = (data, options) => {
  return authRequest({ url: 'addSysRole', data, ...options })
}

/**获取角色信息 */
export const getSysRoleForEditApi: ApiFunc<GetSysRoleForEditInput, GetSysRoleForEditOutput> = (data, options) => {
  return authRequest({ url: 'getSysRoleForEdit', data, ...options })
}

/**编辑角色 */
export const updateSysRoleApi: ApiFunc<UpdateSysRoleInput, undefined> = (data, options) => {
  return authRequest({ url: 'updateSysRole', data, ...options })
}

/**删除角色 */
export const deleteSysRoleApi: ApiFunc<DeleteSysRoleInput, undefined> = (data, options) => {
  return authRequest({ url: 'deleteSysRole', data, ...options })
}

/**查看已经绑定的账户 */
export const getSysManagerPageByRoleIdApi: ApiFunc<GetSysManagerPageByRoleIdInput, GetSysManagerPageByRoleIdOutput> = (
  data,
  options
) => {
  return authRequest({ url: 'getSysManagerPageByRoleId', data, ...options })
}
