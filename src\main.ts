import { registerHiPrintPlugin } from '@/utils/hi-print-plugin-utils.ts'
import { createApp } from 'vue'
import { createPinia } from 'pinia'
import piniaPluginPersistedState from 'pinia-plugin-persistedstate'
import SvgIcon from '~virtual/svg-component'
import VueAnimXyz from '@animxyz/vue3'

// @ts-ignore
import '@animxyz/core'

import App from './App.vue'
import router from './router'

import './styles/index.scss'
import runTime from './runTime'

const app = createApp(App)

app.component(SvgIcon.name!, SvgIcon)

const pinia = createPinia()
window.__pinia = pinia
app.use(pinia)
pinia.use(piniaPluginPersistedState)

app.use(router)

app.use(VueAnimXyz)

// 注册 hiPrint 插件
registerHiPrintPlugin(app)

runTime({ router }, app)

app.mount('#app')
