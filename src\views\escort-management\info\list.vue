<script setup lang="ts">
/**
 * 留人陪护申请管理 - 留人陪护信息管理
 */
import {
  EscortInfoStatus,
  HospitalSitterServerItem,
  ReqGetHospitalSitterServerByPage,
  escortInfoStatusToBoolean
} from '@/api/dto/escort-management.dto.ts'
import {
  requestAddHospitalSitterServer,
  requestDeleteHospitalSitterServer,
  requestDisableHospitalSitterServer,
  requestEnableHospitalSitterServer,
  requestGetDepartmentList,
  requestGetHospitalSitterServerByPage,
  requestUpdateHospitalSitterServer
} from '@/api/escort-management.api.ts'
import { appMessage } from '@/hooks/useNaiveApi.ts'
import EscortInformation from '@/views/escort-management/info/components/escort-information.vue'

// 科室选项列表（从接口获取）
const departmentOptions = ref<{ label: string; value: string }[]>([])

// 获取科室列表
async function getDepartmentList() {
  try {
    const departments = await requestGetDepartmentList()
    // 将字符串数组转换为选项格式
    departmentOptions.value = departments.map((department) => ({
      label: department,
      value: department
    }))
    console.log('获取科室列表成功：', departmentOptions.value)
  } catch (e) {
    console.error('获取科室列表失败：', e)
    appMessage.error('获取科室列表失败')
  }
}

const escortInformationRef = ref<InstanceType<typeof EscortInformation>>()

// 表格数据
const infoData = reactive({
  listData: [] as HospitalSitterServerItem[],
  total: 0
})

onMounted(async () => {
  // 先获取科室列表
  await getDepartmentList()
})

// 获取列表数据
async function getHospitalSitterServerList(params: {
  currentPage: number
  pageSize: number
  searchData: Partial<ReqGetHospitalSitterServerByPage>
}) {
  try {
    const { data, recordCount } = await requestGetHospitalSitterServerByPage({
      department: params.searchData.department || '',
      status: params.searchData.status,
      page: params.currentPage,
      rows: params.pageSize
    })
    console.log('获取留人陪护信息列表成功：', data)
    infoData.listData = data
    infoData.total = recordCount
  } catch (e) {
    console.error('获取留人陪护信息列表失败：', e)
    infoData.listData = []
    infoData.total = 0
  }
}

// 搜索
function handleSearchData(e: { currentPage: number; pageSize: number; searchData: ReqGetHospitalSitterServerByPage }) {
  console.log('搜索：', e)
  getHospitalSitterServerList(e)
}

// 新增
async function handleAddInfo(data: any, resolve: () => void, reject: () => void) {
  console.log('新增陪护信息，点击确认：', data)
  try {
    await requestAddHospitalSitterServer({
      department: data.departmentName,
      serverName: data.serviceName,
      price: parseFloat(data.price),
      totalNumber: data.total,
      status: escortInfoStatusToBoolean(data.status),
      remark: data.remark
    })

    // 完成操作
    resolve()

    // 提示成功并刷新列表
    await ElMessageBox({
      title: '提示',
      type: 'success',
      message: '新增陪护信息成功',
      callback: () => {
        escortInformationRef.value?.handleSearch()
      }
    })
  } catch (e) {
    console.error('新增陪护信息失败：', e)
    reject()
    appMessage.error('新增陪护信息失败，请稍后重试')
  }
}

// 编辑
async function handleEditInfo(data: any, resolve: () => void, reject: () => void) {
  console.log('编辑陪护信息：', data)
  try {
    await requestUpdateHospitalSitterServer({
      medicalHospitalSitterServerId: data.id,
      department: data.departmentName,
      serverName: data.serviceName,
      price: parseFloat(data.price),
      totalNumber: data.total,
      status: escortInfoStatusToBoolean(data.status),
      remark: data.remark
    })

    // 完成操作
    resolve()

    // 提示成功并刷新列表
    await ElMessageBox({
      title: '提示',
      type: 'success',
      message: '修改陪护信息成功',
      callback: () => {
        escortInformationRef.value?.handleSearch()
      }
    })
  } catch (e) {
    console.error('编辑陪护信息失败：', e)
    reject()
    appMessage.error('修改陪护信息失败，请稍后重试')
  }
}

// 启用/禁用
async function handleChangeInfoStatus(e: { id: string; status: EscortInfoStatus }) {
  console.log('启用/禁用：', e)
  try {
    if (e.status === EscortInfoStatus.ENABLE) {
      // 启用
      await requestEnableHospitalSitterServer({
        medicalHospitalSitterServerId: e.id
      })
      appMessage.success('启用成功')
    } else {
      // 禁用
      await requestDisableHospitalSitterServer({
        medicalHospitalSitterServerId: e.id
      })
      appMessage.success('禁用成功')
    }

    // 刷新列表
    escortInformationRef.value?.handleSearch()
  } catch (e) {
    console.error('修改状态失败：', e)
    appMessage.error('修改状态失败，请稍后重试')
  }
}

// 删除
async function handleDeleteInfo(e: any) {
  console.log('删除：', e)
  try {
    // 确认删除
    await ElMessageBox.confirm('确认删除该陪护信息吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    // 调用删除接口
    await requestDeleteHospitalSitterServer({
      medicalHospitalSitterServerId: e.medicalHospitalSitterServerId
    })

    appMessage.success('删除成功')

    // 刷新列表
    escortInformationRef.value?.handleSearch()
  } catch (e) {
    console.error('删除失败：', e)
    if (e !== 'cancel') {
      // 如果不是弹窗按钮点击取消，则提示错误
      appMessage.error('删除失败，请稍后重试')
    }
  }
}
</script>

<template>
  <EscortInformation
    ref="escortInformationRef"
    class="container"
    :list-data="infoData.listData"
    :total="infoData.total"
    :department-options="departmentOptions"
    @search="handleSearchData"
    @add="handleAddInfo"
    @edit="handleEditInfo"
    @changeStatus="handleChangeInfoStatus"
    @delete="handleDeleteInfo"
  />
</template>

<style scoped lang="scss">
.container {
  padding: 30px 20px;
}
</style>
