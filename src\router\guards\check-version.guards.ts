import axios from 'axios'
import type { AppBeforeEach } from './guards'
import { useGlobalStore } from '@/stores/global.store'

export const checkVersion: AppBeforeEach = async (_, __, next) => {
  try {
    const version = localStorage.getItem('version')
    const { data } = await axios.get(`./version.json?t=${Date.now()}`)
    useGlobalStore().appConfig = data
    if (version !== data.version) {
      localStorage.setItem('version', data.version)
      window.location.reload()
    }
  } finally {
    next()
  }
}
