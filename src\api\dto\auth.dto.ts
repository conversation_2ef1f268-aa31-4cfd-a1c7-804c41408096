/**用户名密码登录入参 */
export interface LoginInput {
  /**用户名 */
  account: string
  /**密码 */
  password: string
  /**用户输入的验证码 */
  code: string
}

/**用户名密码登录出参 */
export interface LoginOutput {
  /**用于用户请求接口 */
  authToken: string
  /**用于刷新获取最新AccessToken */
  refreshAuthToken: string
  /**登录提示，比如是否需要修改密码 */
  expireTip: string
}

/**刷新token入参 */
export interface RefreshAuthTokenInput {
  /**用户主键 */
  managerId: string
  /**用于刷新获取最新AccessToken */
  refreshAuthToken: string
}

/**刷新token出参 */
export interface RefreshAuthTokenOutput {
  /**用于用户请求接口 */
  authToken: string
  /**用于刷新获取最新AccessToken */
  refreshAuthToken: string
}

/**注销登录入参 */
export interface LogoutInput {
  /**用户主键 */
  managerId: string
}

export interface UpdatePasswordInput {
  oldPassword: string
  newPassword: string
}
