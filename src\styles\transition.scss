// global transition css

/* fade */
.fade-enter-active,
.fade-leave-active {
  transition: all 0.28s;
  width: 100%;
}

.fade-enter-from,
.fade-leave-active {
  opacity: 0;
}

.fade-leave-active {
  position: absolute;
}

/* fade-transform */
.fade-transform-enter-active,
.fade-transform-leave-active {
  left: 0;
  transition: all 0.28s;
}

.fade-transform-enter-from,
.fade-transform-leave-to {
  opacity: 0;
  transform: translateX(30px);
}

/* breadcrumb transition */
.breadcrumb-enter-active,
.breadcrumb-leave-active {
  transition: all 0.56s;
}

.breadcrumb-enter-from,
.breadcrumb-leave-active {
  opacity: 0;
  transform: translateX(20px);
}

.breadcrumb-leave-active {
  position: absolute;
}
