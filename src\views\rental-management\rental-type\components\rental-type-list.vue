<script setup lang="ts">
/**
 * 陪护人租赁管理 - 陪护类型管理
 */
import {
  ReqAddInpatientCaregiverType,
  ReqUpdateInpatientCaregiverType,
  ResInpatientCaregiverTypeItem,
  ReqInpatientCaregiverTypeId
} from '@/api/dto/rental-management/rental-type.dto.ts'
import { useMountDialog } from '@/hooks/useMountDialog.ts'
import RentalTypeEditDialog from '@/views/rental-management/rental-type/components/rental-type-edit-dialog.vue'
import { toRaw, onMounted, reactive } from 'vue'

enum ItemActionType {
  /* 编辑 */
  EDIT = 'edit',
  /* 启用 */
  ENABLE = 'enable',
  /* 禁用 */
  DISABLE = 'disable',
  /* 删除 */
  DELETE = 'delete'
}

const rentalTypeStatusOptions = [
  { label: '启用', value: true },
  { label: '禁用', value: false }
]

const emits = defineEmits<{
  (
    e: 'search',
    data: { currentPage: number; pageSize: number; searchData: { serverName?: string; status?: boolean } }
  ): void
  (e: 'add', data: ReqAddInpatientCaregiverType, resolve: () => void, reject: () => void): void
  (e: 'edit', data: ReqUpdateInpatientCaregiverType, resolve: () => void, reject: () => void): void
  (e: 'changeStatus', data: { id: string; status: boolean }): void
  (e: 'delete', data: ReqInpatientCaregiverTypeId): void
}>()

defineProps<{
  listData: ResInpatientCaregiverTypeItem[]
  total: number
}>()

const { open: openEditDialog } = useMountDialog(RentalTypeEditDialog)

// 搜索表单
const searchFormData = reactive({
  serverName: '',
  status: undefined as boolean | undefined
})

// 分页
const paginationData = reactive({
  currentPage: 1,
  pageSize: 10
})

onMounted(() => {
  // 默认加载页面时搜索
  emitSearch()
})

// 搜索
function handleSearch() {
  paginationData.currentPage = 1
  emitSearch()
}

// 重置
function handleReset() {
  searchFormData.serverName = ''
  searchFormData.status = undefined
  emitSearch()
}

// 新增
function handleAdd() {
  openEditDialog({
    title: '新增',
    confirmCallback: async (data: ReqAddInpatientCaregiverType) => {
      return new Promise<void>((resolve, reject) => {
        // 父组件可能是异步操作，把 resolve 和 reject 传给子组件，子组件可以调用
        emits('add', data, resolve, reject)
      })
    }
  })
}

function emitSearch() {
  emits('search', {
    currentPage: paginationData.currentPage,
    pageSize: paginationData.pageSize,
    searchData: toRaw(searchFormData)
  })
}

function handleItemAction(itemAction: ItemActionType, itemData: ResInpatientCaregiverTypeItem) {
  switch (itemAction) {
    case ItemActionType.EDIT:
      openEditDialog({
        detailInfo: itemData,
        title: '编辑',
        confirmCallback: async (data: ReqAddInpatientCaregiverType | ReqUpdateInpatientCaregiverType) => {
          return new Promise<void>((resolve, reject) => {
            // 父组件可能是异步操作，把 resolve 和 reject 传给子组件，子组件可以调用
            emits('edit', data as ReqUpdateInpatientCaregiverType, resolve, reject)
          })
        }
      })
      break
    case ItemActionType.ENABLE:
      // 启用
      emits('changeStatus', {
        id: itemData.inpatientCaregiverTypeId,
        status: true
      })
      break
    case ItemActionType.DISABLE:
      // 禁用
      emits('changeStatus', {
        id: itemData.inpatientCaregiverTypeId,
        status: false
      })
      break
    case ItemActionType.DELETE:
      // 删除
      emits('delete', { inpatientCaregiverTypeId: itemData.inpatientCaregiverTypeId })
      break
    default:
      break
  }
}

function handleSizeChange() {
  // 切换每页条数时把当前页重置回 1
  paginationData.currentPage = 1
  emitSearch()
}

function handleCurrentChange() {
  emitSearch()
}

defineExpose({
  handleSearch
})
</script>

<template>
  <div class="page-container">
    <el-form inline :model="searchFormData">
      <el-form-item label="类型名称：">
        <el-input
          v-model="searchFormData.serverName"
          class="input-container"
          placeholder="请输入"
          size="large"
          clearable
        />
      </el-form-item>
      <el-form-item label="状态：">
        <el-select v-model="searchFormData.status" class="input-container" placeholder="请选择" size="large" clearable>
          <el-option
            v-for="item in rentalTypeStatusOptions"
            :key="item.label"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button v-auth.disabled="'InpatientCaregiverTypeByPage'" size="large" type="primary" @click="handleSearch"
          >搜索
        </el-button>
        <el-button v-auth.disabled="'InpatientCaregiverTypeByPage'" size="large" @click="handleReset">重置</el-button>
        <el-button v-auth.disabled="'InpatientCaregiverTypeAdd'" size="large" type="success" @click="handleAdd"
          >新增
        </el-button>
      </el-form-item>
    </el-form>

    <BaseTable class="table-container" :data="listData" border width="100%" height="608">
      <el-table-column prop="serverName" label="类型名称" width="200" />
      <el-table-column prop="price" label="单价" width="200">
        <template #default="scope"> {{ scope.row.price }}元/天</template>
      </el-table-column>
      <el-table-column prop="status" label="状态" width="200">
        <template #default="scope">
          <el-tag :type="scope.row.status ? 'success' : 'warning'">
            {{ scope.row.status ? '启用' : '禁用' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="remark" label="备注" width="200">
        <template #default="scope"> {{ scope.row.remark ?? '-' }}</template>
      </el-table-column>
      <el-table-column prop="createTime" label="创建时间" width="200" />
      <el-table-column prop="modifyTime" label="更新时间" width="200" />

      <el-table-column label="操作" fixed="right" min-width="280">
        <template #default="scope">
          <el-button
            v-auth.disabled="'InpatientCaregiverTypeUpdate'"
            size="small"
            @click="handleItemAction(ItemActionType.EDIT, scope.row)"
            >编辑
          </el-button>
          <el-button
            v-if="!scope.row.status"
            v-auth.disabled="'InpatientCaregiverTypeEnable'"
            size="small"
            type="success"
            @click="handleItemAction(ItemActionType.ENABLE, scope.row)"
            >启用
          </el-button>
          <el-button
            v-if="scope.row.status"
            v-auth.disabled="'InpatientCaregiverTypeDisable'"
            size="small"
            type="warning"
            @click="handleItemAction(ItemActionType.DISABLE, scope.row)"
            >禁用
          </el-button>
          <el-button
            v-auth.disabled="'InpatientCaregiverTypeDelete'"
            size="small"
            type="danger"
            @click="handleItemAction(ItemActionType.DELETE, scope.row)"
            >删除
          </el-button>
        </template>
      </el-table-column>
    </BaseTable>

    <base-pagination
      v-model:current-page="paginationData.currentPage"
      v-model:page-size="paginationData.pageSize"
      :total="total"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />
  </div>
</template>

<style scoped lang="scss">
.page-container {
  padding: 20px 0;
}

.input-container {
  width: 200px;
}

.table-container {
  margin: 12px 0 30px;
}
</style>
