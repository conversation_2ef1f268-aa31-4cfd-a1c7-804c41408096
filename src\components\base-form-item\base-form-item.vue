<script lang="ts" setup>
const props = defineProps<{
  label: string
  labelWidth?: string
}>()

const lableWidth = computed(() => {
  if (props.labelWidth) {
    return `${props.labelWidth}px`
  } else {
    const { length } = props.label
    return `${length + 0.1 * length}em`
  }
})
</script>

<template>
  <div class="base_form_item-container flex-center">
    <span class="base_form_item-label">{{ label }}</span>
    <span class="base_form_item-colon">:</span>
    <slot></slot>
  </div>
</template>

<style lang="scss" scoped>
.base_form_item {
  &-label {
    flex-shrink: 0;
    width: v-bind(lableWidth);
    font-size: 14px;
    text-align: right;
    color: var(--el-text-color-regular);
  }
  &-colon {
    margin: 0 4px;
  }
}
</style>
