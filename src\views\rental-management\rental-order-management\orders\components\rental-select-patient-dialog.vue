<script setup lang="ts">
import { useFormHook } from '@/hooks/useForm.ts'
import { reactive, ref, watch } from 'vue'
import { requestSelectInpatientByPage } from '@/api/rental-management.api.ts'
import { ResSelectInpatientByPageDTO } from '@/api/dto/rental-management/rental-order-management.dto.ts'
import { maskIdCard } from '@/utils'

const props = defineProps<{
  // 科室代码
  deptcode: string
  // 确认回调
  confirmCallback: (data: ResSelectInpatientByPageDTO) => void
}>()

const { formRef } = useFormHook()

const dialogVisible = defineModel({ default: false })

const searchParams = reactive({
  patientName: '',
  patno: ''
})

const patientList = ref<ResSelectInpatientByPageDTO[]>([])

// 分页
const paginationData = reactive({
  currentPage: 1,
  pageSize: 10
})

const total = ref(0)

watch(
  dialogVisible,
  (v) => {
    if (v) {
      formRef.value?.resetFields()
      setTimeout(() => {
        fetchPatientData()
      })
    }
  },
  {
    immediate: true
  }
)

const fetchPatientData = async () => {
  try {
    const res = await requestSelectInpatientByPage({
      keyword: searchParams.patientName || undefined,
      patno: searchParams.patno || undefined,
      page: paginationData.currentPage,
      rows: paginationData.pageSize,
      deptcode: props.deptcode
    })

    patientList.value = res.data
    total.value = res.recordCount
    console.log('获取住院人列表成功：', res)
  } catch (error) {
    console.error('获取住院人列表失败：', error)
  }
}

const handleSearch = () => {
  fetchPatientData()
}

const handleReset = () => {
  searchParams.patientName = ''
  searchParams.patno = ''
  fetchPatientData()
}

const selectPerson = (row: ResSelectInpatientByPageDTO) => {
  dialogVisible.value = false
  props.confirmCallback && props.confirmCallback(row)
}

const handleCurrentChange = (page: number) => {
  paginationData.currentPage = page
  fetchPatientData()
}

function handleSizeChange() {
  // 切换每页条数时把当前页重置回 1
  paginationData.currentPage = 1
  fetchPatientData()
}
</script>

<template>
  <el-dialog class="rental-select-patient-dialog" v-model="dialogVisible" title="选择住院人" width="1107px">
    <el-form ref="formRef" :model="searchParams" :inline="true">
      <el-form-item label="住院人姓名：" prop="patientName">
        <el-input class="search-input" v-model="searchParams.patientName" placeholder="请输入" clearable />
      </el-form-item>
      <el-form-item label="住院号：" prop="patno">
        <el-input class="search-input" v-model="searchParams.patno" placeholder="请输入" clearable />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="handleSearch">搜索</el-button>
        <el-button @click="handleReset">重置</el-button>
      </el-form-item>
    </el-form>

    <BaseTable :data="patientList" border height="480">
      <el-table-column prop="name" label="住院人姓名" min-width="180" />
      <el-table-column prop="certno" label="证件号" min-width="180">
        <template #default="{ row }">
          {{ maskIdCard(row.certno) }}
        </template>
      </el-table-column>
      <el-table-column prop="curdptnm" label="住院科室" min-width="180" />
      <el-table-column prop="curdname" label="主治医生" min-width="180" />
      <el-table-column prop="bedno" label="床号" min-width="180" />
      <el-table-column label="操作" min-width="160" fixed="right">
        <template #default="{ row }">
          <el-button type="primary" size="small" @click="selectPerson(row)">选择</el-button>
        </template>
      </el-table-column>
    </BaseTable>

    <div class="pagination-wrapper">
      <BasePagination
        v-model:current-page="paginationData.currentPage"
        v-model:page-size="paginationData.pageSize"
        size="small"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </el-dialog>
</template>

<style lang="scss">
.rental-select-patient-dialog {
  .el-dialog__body {
    padding: 16px 0 !important;
  }
}
</style>

<style scoped lang="scss">
.pagination-wrapper {
  margin-top: 20px;
}

.search-input {
  width: 200px;
}
</style>
