<script setup lang="ts">
import {
  CreateRentalOrderInfo,
  ServiceTypeInfo,
  ResSelectInpatientByPageDTO
} from '@/api/dto/rental-management/rental-order-management.dto.ts'
import { useFormHook } from '@/hooks/useForm.ts'
import { useMountDialog } from '@/hooks/useMountDialog.ts'
import RentalSelectPatientDialog from '@/views/rental-management/rental-order-management/orders/components/rental-select-patient-dialog.vue'
import { FormRules } from 'element-plus'
import { requestGetServiceType } from '@/api/rental-management.api.ts'
import RentalSelectDepartmentDialog from '@/views/rental-management/rental-order-management/orders/components/rental-select-department-dialog.vue'
import { maskIdCard } from '@/utils'

const { open: openSelectPatientDialog } = useMountDialog(RentalSelectPatientDialog)
const { open: openSelectDepartmentDialog } = useMountDialog(RentalSelectDepartmentDialog)

const props = defineProps<{
  confirmCallback: (orderData: CreateRentalOrderInfo) => Promise<void>
}>()

const isConfirmLoading = ref(false)

const { formRef } = useFormHook()

const formData = reactive<CreateRentalOrderInfo>({} as CreateRentalOrderInfo)

// 服务类型列表
const serviceTypeList = ref<ServiceTypeInfo[]>([])

const drawerVisible = defineModel({ default: false })

watch(
  drawerVisible,
  (v) => {
    if (v) {
      // 重置表单，避免在点击编辑后再点击新增时候，因为弹窗组件没有销毁导致数据不清空
      formRef.value?.resetFields()
      getServiceType()
    }
  },
  {
    immediate: true
  }
)

// 观察当服务类型、开始时间和结束时间变化时，计算核算金额
watch(
  () => [formData.serverType, formData.startTime, formData.endTime],
  () => {
    setTimeout(() => {
      calculatePayFee()
    }, 100)
  },
  {
    deep: true
  }
)

const validateStartTime = (_: any, value: any, callback: any) => {
  if (value > formData.endTime) {
    callback(new Error('开始时间不能大于结束时间'))
  } else {
    callback()
  }
}

const validateEndTime = (_: any, value: any, callback: any) => {
  if (value < formData.startTime) {
    callback(new Error('结束时间不能小于开始时间'))
  } else {
    callback()
  }
}

const rules = reactive<FormRules>({
  patientName: [{ required: true, message: '请选择住院人', trigger: 'change' }],
  patientIdNo: [{ required: true, message: '请选择住院人', trigger: 'change' }],
  department: [{ required: true, message: '请选择住院人', trigger: 'change' }],
  serverType: [{ required: true, message: '请选择服务类型', trigger: 'change' }],
  // 开始时间不能大于结束时间
  startTime: [
    { required: true, message: '请选择开始时间', trigger: 'change' },
    { validator: validateStartTime, trigger: 'change' }
  ],
  endTime: [
    { required: true, message: '请选择结束时间', trigger: 'change' },
    { validator: validateEndTime, trigger: 'change' }
  ],
  contactName: [{ required: true, message: '请输入联系人姓名', trigger: 'blur' }],
  contactPhone: [{ required: true, message: '请输入联系人电话', trigger: 'blur' }]
})

const handleConfirm = async () => {
  await formRef.value!.validate(async (valid) => {
    if (valid) {
      try {
        isConfirmLoading.value = true
        await props.confirmCallback(formData)
        drawerVisible.value = false
        setTimeout(() => {
          formRef.value!.resetFields()
        })
      } catch (error) {
        // 结束按钮 loading，但是不关闭弹窗
      } finally {
        isConfirmLoading.value = false
      }
    } else {
      console.log('表单数据验证失败:', formRef.value!)
    }
  })
}

const handleCancel = () => {
  drawerVisible.value = false
  formRef.value?.resetFields()
}

const handleSelectPatient = () => {
  openSelectDepartmentDialog({
    confirmCallback: (departmentCode: string) => {
      console.log('选择科室', departmentCode)

      openSelectPatientDialog({
        deptcode: departmentCode,
        confirmCallback: (data: ResSelectInpatientByPageDTO) => {
          console.log('选择住院人', data)
          formData.patientName = data.name
          formData.patientIdNo = data.certno
          formData.department = data.curdptnm
        }
      })
    }
  })
}

// 获取服务类型
async function getServiceType() {
  try {
    const res = await requestGetServiceType()
    serviceTypeList.value = res || []
  } catch (error) {
    console.error('获取服务类型失败：', error)
    return null
  }
}

const handleServiceTypeChange = (value: any) => {
  const selectedServiceType = serviceTypeList.value.find((item) => item.serverType === value)
  if (selectedServiceType) {
    formData.serverPrice = selectedServiceType.serverPrice
  }
}

// 计算核算金额
const calculatePayFee = () => {
  if (!formData.startTime || !formData.endTime || !formData.serverPrice) {
    formData.payFee = undefined
    return
  }
  const startTime = new Date(formData.startTime)
  const endTime = new Date(formData.endTime)
  if (startTime > endTime) {
    formData.payFee = undefined
    return
  }
  const days = (endTime.getTime() - startTime.getTime()) / (1000 * 60 * 60 * 24) + 1
  formData.payFee = days * formData.serverPrice
}
</script>

<template>
  <el-drawer
    class="rental-create-container"
    v-model="drawerVisible"
    title="创建订单"
    size="870px"
    @close="handleCancel"
  >
    <el-button
      v-auth.disabled="'InpatientCaregiverSelectInpatientByPage'"
      class="select-patient-btn"
      type="primary"
      size="large"
      @click="handleSelectPatient"
    >
      选择住院人
    </el-button>

    <el-form ref="formRef" :model="formData" :rules="rules" label-width="auto" label-position="right" size="large">
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="住院人姓名：" prop="patientName">
            <el-input :value="formData.patientName" placeholder="选择后填充" disabled />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="证件号：" prop="patientIdNo">
            <el-input :value="maskIdCard(formData.patientIdNo)" placeholder="选择后填充" disabled />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="住院科室：" prop="department">
            <el-input :value="formData.department" placeholder="选择后填充" disabled />
          </el-form-item>
        </el-col>
      </el-row>

      <el-divider border-style="dashed" />

      <div>填写信息</div>

      <el-row>
        <el-col :span="24">
          <el-form-item label="服务类型：" prop="serverType">
            <el-radio-group v-model="formData.serverType" @change="handleServiceTypeChange">
              <el-radio-button v-for="item in serviceTypeList" :key="item.serverType" :value="item.serverType">
                <div class="service-type">
                  <div class="service-type-value">¥{{ item.serverPrice }}/天</div>
                  <div class="service-type-label">{{ item.serverType }}</div>
                </div>
              </el-radio-button>
            </el-radio-group>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="开始时间：" prop="startTime">
            <el-date-picker
              v-model="formData.startTime"
              type="date"
              placeholder="请选择"
              style="width: 100%"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="结束时间：" prop="endTime">
            <el-date-picker
              v-model="formData.endTime"
              type="date"
              placeholder="请选择"
              style="width: 100%"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="联系人姓名：" prop="contactName">
            <el-input v-model="formData.contactName" placeholder="请输入" clearable />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="联系人电话：" prop="contactPhone">
            <el-input v-model="formData.contactPhone" placeholder="请输入" clearable />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <div class="amount">
      核算金额： <span class="amount-value">¥{{ formData.payFee }}</span>
    </div>

    <el-button
      v-auth.disabled="'CreateInpatientCaregiverOrder'"
      :loading="isConfirmLoading"
      class="confirm-btn"
      type="success"
      size="large"
      @click="handleConfirm"
      >确定创建</el-button
    >
  </el-drawer>
</template>

<style lang="scss">
.rental-create-container {
  .el-divider.el-divider--horizontal {
    margin: 10px 0 30px 0 !important;
  }

  .el-drawer__header {
    margin-bottom: 0 !important;
    background: #f5f5f5 !important;
    padding: 17px 20px !important;
    font-size: 16px !important;
    color: #303133 !important;
  }

  .el-radio-button__inner {
    border: 0 !important;
    padding: 0 !important;
    margin-right: 30px !important;
    box-shadow: none !important;
  }

  .el-radio-button.is-active .el-radio-button__inner {
    border-radius: 4px !important;
    background-color: rgba(#1890ff, 0.04) !important;
    border: 1px solid #1890ff !important;
    color: #1890ff !important;
    width: 200px;
    height: 80px;

    &::after {
      content: '';
      position: absolute;
      bottom: 0;
      right: 0;
      border-bottom: 28px solid #1890ff;
      border-left: 28px solid transparent;
      width: 0;
      height: 0;
      display: block;
      z-index: 1;
    }

    &::before {
      content: '\2713';
      position: absolute;
      bottom: 2px;
      right: 4px;
      color: white;
      font-size: 12px;
      z-index: 2;
    }

    .service-type {
      &-value {
        color: #1890ff;
      }

      &-label {
        color: #1890ff;
      }
    }
  }
}
</style>

<style scoped lang="scss">
.select-patient-btn {
  margin-bottom: 30px;
}

.service-type {
  width: 200px;
  height: 80px;
  border-radius: 4px;
  border: 1px solid #dcdfe6;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  flex-direction: column;

  &-value {
    font-weight: 700;
    font-size: 16px;
    text-align: left;
    color: #303133;
  }

  &-label {
    font-size: 14px;
    text-align: left;
    color: #606266;
  }
}

.amount {
  border-radius: 4px;
  background: #fafafa;
  border: 1px solid #dfe6ec;
  font-weight: 700;
  font-size: 16px;
  color: #303133;
  padding: 19px 16px;
  margin-left: 110px;

  &-value {
    color: #e6a23c;
  }
}

.confirm-btn {
  margin-left: 110px;
  margin-top: 30px;
}
</style>
