/**路由守卫 */
import nProgress from 'nprogress'
import 'nprogress/nprogress.css' // 进度条样式文件
import type { AppAfterEach, AppBeforeEach, RouterGuards } from './guards'

import { useAxiosStore } from '@/stores/axios.store'
import { checkVersion } from './check-version.guards'
import { checkLoginStatus } from './check-login-status'

import { addTabs, deleteTabs } from './tabs'
import { authorityVerification } from './authority-verification'
import { getNavigationId } from './get-navigationId'

nProgress.configure({ showSpinner: false })

/**加载进度条 */
const progressStart: AppBeforeEach = (to, __, next) => {
  document.title = to.meta.title || ''
  !nProgress.isStarted() && nProgress.start()
  next()
}

/**进度条结束 */
export const progressDone: AppAfterEach = () => {
  nProgress.done()
}

const removeAxiosPending: AppBeforeEach = (_, __, next) => {
  const axiosStore = useAxiosStore()
  axiosStore.removeAxiosPending()
  next()
}

const guards: RouterGuards = {
  beforeEach: [
    progressStart,
    checkVersion,
    checkLoginStatus,
    authorityVerification,
    getNavigationId,
    removeAxiosPending
  ],
  afterEach: [progressDone, addTabs, deleteTabs]
}

export default guards
