<script lang="ts" setup>
import { debounce } from 'lodash-es'

// TODO: 需要新增虚拟滚动

const props = withDefaults(
  defineProps<{
    /**列间距 */
    rowSpacing?: number
    /**行间距 */
    columnSpacing?: number
    /**列宽 */
    columnWidth: number
  }>(),
  {
    rowSpacing: 18,
    columnSpacing: 18
  }
)

/**容器 */
const containerRef = ref<HTMLDivElement>()
/**子元素 */
const childrenRef = ref<HTMLElement[]>([])
/**子元素高度集合 */
const childHeights = ref<number[]>([])

/**容器左边距 */
const containerLeft = ref(0)
/**容器宽度 */
const containerWidth = ref(0)
const containerWidthStyle = computed(() => `${containerWidth.value}px`)

/**容器左边距 */
const useContainerWidth = () => {
  if (containerRef.value) {
    const { paddingLeft, paddingRight, width } = window.getComputedStyle(containerRef.value)
    containerLeft.value = parseFloat(paddingLeft)
    containerWidth.value = parseInt(width) - parseFloat(paddingLeft) - parseFloat(paddingRight)
  }
}

/**获取容器子元素 */
const handleChild = () => {
  if (containerRef.value) {
    childrenRef.value = Array.from(containerRef.value.children as any)

    childrenRef.value.forEach((child) => {
      if (!child.className.includes('waterfall_flow-item')) {
        child.classList.add('waterfall_flow-item')
      }
      childHeights.value.push(child.offsetHeight)
    })
  }
}

/**列数 */
const column = ref(0)
/**每列高度 */
const columnHeights = ref<number[]>([])
/**获取列数 */
const useColumn = () => {
  column.value =
    Math.floor((containerWidth.value + props.columnSpacing) / (props.columnWidth + props.columnSpacing)) || 1

  columnHeights.value = Array(column.value).fill(0)
}

const useMinHeight = () => {
  const minHeight = Math.min(...columnHeights.value)
  const minHeightIndex = columnHeights.value.indexOf(minHeight)

  return { minHeight, minHeightIndex }
}

/**获取左距离 */
const getItemLeft = () => {
  const { minHeightIndex } = useMinHeight()
  return (props.columnWidth + props.columnSpacing) * minHeightIndex + containerLeft.value
}

/**获取上距离 */
const getItemTop = () => {
  return useMinHeight().minHeight
}

/**更新每列高度 */
const increaseColumnHeight = (index: number) => {
  const { minHeightIndex } = useMinHeight()
  columnHeights.value[minHeightIndex] += childHeights.value[index] + props.rowSpacing
}

const usePosition = () => {
  for (let index = 0; index < childrenRef.value.length; index++) {
    const child = childrenRef.value[index]
    if (column.value === 1) {
      child.style.transform = `translate(0,${props.rowSpacing * index}px)`
    } else {
      const left = getItemLeft() + 'px'
      const top = getItemTop() + 'px'
      child.style.transform = `translate(${left}, ${top})`
    }

    increaseColumnHeight(index)
  }

  if (containerRef.value) {
    containerRef.value.style.height = `${Math.max(...columnHeights.value) - props.rowSpacing}px`
  }
}

onMounted(async () => {
  await nextTick()
  useContainerWidth()

  handleChild()

  useColumn()
})

watch(column, () => {
  usePosition()
})

/**容器变化监听器 */
const containerResizeObserver = new ResizeObserver(
  debounce(() => {
    useContainerWidth()
    useColumn()
  }, 100)
)

/**容器新增子元素数量变化监听器 */
const containerAddChildObserver = new MutationObserver(() => {
  handleChild()
  usePosition()
})

/**子元素变化监听器 */
const childResizeObserver = new ResizeObserver(
  debounce((entrys: ResizeObserverEntry[]) => {
    entrys.forEach((entry) => {
      const target: any = entry.target
      const index = childrenRef.value.indexOf(target)
      if (index > -1) {
        childHeights.value[index] = target.offsetHeight
      }
    })

    useColumn()
    usePosition()
  }, 100)
)

watch(childrenRef, (pre, old) => {
  old.forEach((item: any) => {
    childResizeObserver.unobserve(item)
  })

  pre.forEach((item: any) => {
    childResizeObserver.observe(item)
  })
})

onMounted(() => {
  containerResizeObserver.observe(containerRef.value!)
  containerAddChildObserver.observe(containerRef.value!, { childList: true })
})

onBeforeUnmount(() => {
  containerResizeObserver.disconnect()
  containerAddChildObserver.disconnect()
})
</script>

<template>
  <div ref="containerRef" class="waterfall_flow-container" :class="{ full: column === 1 }">
    <slot></slot>
  </div>
</template>

<style lang="scss" scoped>
.waterfall_flow-container {
  position: relative;
  height: 100px;
  transition: height 0.28s;

  :deep(.waterfall_flow-item) {
    position: absolute;
    transition: all 0.28s;
  }
}

.waterfall_flow-container.full {
  :deep(.waterfall_flow-item) {
    position: initial;
    width: v-bind(containerWidthStyle);
  }
}
</style>
