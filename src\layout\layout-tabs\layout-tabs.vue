<script lang="tsx" setup>
import { storeToRefs } from 'pinia'
import { useTabsStore } from '@/stores/tabs.store'
import type { RouteLocationNormalized } from 'vue-router'
import type { TabPaneName, TabsPaneContext } from 'element-plus'
import type { SvgName } from '~virtual/svg-component'
import { appMessage } from '@/hooks/useNaiveApi'
import { ROUTER_PATH } from '@/router/router-path'
import { useGlobalStore } from '@/stores/global.store'
import { darkTheme } from 'naive-ui'

enum CONTEXT_MENU_KEY {
  /**刷新 */
  REFRESH = 'refresh',
  /**关闭 */
  CLOSE = 'close',
  /**关闭其他 */
  CLOSE_OTHER = 'closeOther',
  /**关闭所有 */
  CLOSE_ALL = 'closeAll',
}

const createDropdownOptionIcon = (icon: SvgName) => {
  return () => <svg-icon name={icon} style="font-size:18px"></svg-icon>
}

const contextMenuOptions = [
  {
    label: '刷新本页',
    key: CONTEXT_MENU_KEY.REFRESH,
    icon: createDropdownOptionIcon('svg-refresh'),
  },
  {
    label: '关闭本页',
    key: CONTEXT_MENU_KEY.CLOSE,
    icon: createDropdownOptionIcon('svg-close'),
  },
  {
    label: '关闭其他',
    key: CONTEXT_MENU_KEY.CLOSE_OTHER,
    icon: createDropdownOptionIcon('svg-crop'),
  },
  {
    label: '关闭所有',
    key: CONTEXT_MENU_KEY.CLOSE_ALL,
    icon: createDropdownOptionIcon('svg-delete'),
  },
]

const route = useRoute()

const router = useRouter()

const { isDark } = storeToRefs(useGlobalStore())

/**高亮tab */
const modelValue = computed(() => route.path)

const tabsStore = useTabsStore()

const { visitedViews } = storeToRefs(tabsStore)

/**点击tab */
const handleTabClick = (item: TabsPaneContext) => {
  router.push(item.props.name as RouteLocationNormalized['path'])
}

/**判断页面是否已经被keepalive缓存 */
const isCached = (tab: RouteLocationNormalized) => {
  const index = tabsStore.getCachedViewIndex(tab)
  return index !== null ? index > -1 : false
}

/**右击对象 */
const contextMenuTarget = ref<RouteLocationNormalized | null>(null)

/**右击菜单数据 */
const contextMenuData = reactive({ x: 0, y: 0 })

/**查找是否点击了tabspane */
const getTabFullPath = (target: HTMLElement): string => {
  if (target.id.startsWith('tab-')) {
    return target.id.replace('tab-', '')
  } else if (target.parentElement) {
    if (target.parentElement.id.startsWith('tab-')) {
      return target.parentElement.id.replace('tab-', '')
    } else {
      return getTabFullPath(target.parentElement)
    }
  } else {
    return ''
  }
}

/**查找tab对象 */
const getTabPane = (path: string) => {
  return visitedViews.value.find((tab) => tab.path === path)!
}

/**右击tab的时候 */
const handleContextMenu = (e: PointerEvent) => {
  handleClickoutside()
  const eventTarget = e.target as HTMLElement

  if (Array.from(eventTarget.classList).includes('el-tabs__nav-scroll')) {
    return
  }

  setTimeout(() => {
    const targetFullPath = getTabFullPath(eventTarget)
    if (targetFullPath) {
      contextMenuTarget.value = getTabPane(targetFullPath)
      contextMenuData.x = e.clientX
      contextMenuData.y = e.clientY
    }
  }, 150)
}

/**点击其他地方的时候 */
const handleClickoutside = () => {
  contextMenuTarget.value = null
}

const contextMenuMethods = {
  refresh: () => {
    tabsStore.delCachedView(contextMenuTarget.value!)
    const { path, query } = contextMenuTarget.value!
    router.replace({ path: `${ROUTER_PATH.REFRESH}${path}`, query })
  },
  close: (name?: TabPaneName) => {
    if (name) {
      contextMenuTarget.value = getTabPane(name as string)
    }
    const {
      meta: { affix },
    } = contextMenuTarget.value!
    if (affix) {
      appMessage.warning('此页面不可关闭')
    } else {
      tabsStore.delView(contextMenuTarget.value!)
      if (modelValue.value === contextMenuTarget.value?.path) {
        toLastView()
      }
    }
    name && handleClickoutside()
  },
  closeOther: () => {
    tabsStore.delOthersViews(contextMenuTarget.value!)
    router.push(contextMenuTarget.value!.fullPath)
  },
  closeAll: () => {
    tabsStore.delAllViews()
    toLastView()
  },
}

const toLastView = () => {
  const latestView = tabsStore.visitedViews.slice(-1)[0]
  if (latestView) {
    router.push(latestView.fullPath)
  } else {
    router.push(ROUTER_PATH.HOME)
  }
}

/**tabs右击菜单被选择的时候 */
const handleContextMenuSelect = async (target: CONTEXT_MENU_KEY) => {
  contextMenuMethods[target]()
  handleClickoutside()
}
</script>

<template>
  <div class="layout_tabs-container">
    <el-tabs
      :model-value="modelValue"
      type="card"
      @tab-click="handleTabClick"
      @tab-remove="contextMenuMethods.close"
      @contextmenu.prevent="handleContextMenu"
    >
      <el-tab-pane v-for="item in visitedViews" :key="item.path" :name="item.path" :closable="!item.meta.affix">
        <template #label>
          <span class="keep-alive-status" :class="{ 'is-keep-alive': isCached(item) }"></span>
          <span>{{ item.meta.title }}</span>
        </template>
      </el-tab-pane>
    </el-tabs>

    <n-config-provider :theme="isDark ? darkTheme : null">
      <n-dropdown
        placement="bottom-start"
        trigger="manual"
        :x="contextMenuData.x"
        :y="contextMenuData.y"
        :options="contextMenuOptions"
        :show="!!contextMenuTarget"
        show-arrow
        :on-clickoutside="handleClickoutside"
        @select="handleContextMenuSelect"
      />
    </n-config-provider>
  </div>
</template>

<style lang="scss" scoped>
.layout_tabs-container {
  width: 100%;
  background-color: var(--el-bg-color-overlay);

  :deep(.el-tabs__nav-prev),
  :deep(.el-tabs__nav-next) {
    background-color: $menuBg;
    i {
      color: $subMenuActiveText;
    }
  }

  :deep(.el-tabs__item) {
    & > .keep-alive-status {
      content: '';
      display: block;
      width: 0;
      height: 0;
      margin-right: 5px;
      background-color: var(--el-color-danger);
      border-radius: 50%;
      transition: all var(--el-transition-duration) var(--el-transition-function-ease-in-out-bezier);
      &.is-keep-alive {
        background-color: var(--el-color-white);
      }
    }
    &.is-active > .keep-alive-status,
    &:hover > .keep-alive-status {
      width: 6px;
      height: 6px;
    }
  }
}

.layout_tabs-container {
  width: 100%;
  :deep(.el-tabs__header) {
    height: $layoutTabsHeight;
    margin: 0;
    .el-tabs__nav.is-top {
      height: 24px;
      margin: 5px 20px;
      border: none;
    }

    .el-tabs__item.is-top {
      box-sizing: border-box;
      height: 24px;
      padding: 0 10px;
      margin-left: 10px;
      border: 1px solid var(--el-border-color);
      font-size: 12px;

      &:first-child {
        margin: 0;
      }

      &.is-active,
      &:hover {
        background-color: #42b983;
        border: none;
        color: var(--el-color-white);
      }
    }
  }
}
</style>
