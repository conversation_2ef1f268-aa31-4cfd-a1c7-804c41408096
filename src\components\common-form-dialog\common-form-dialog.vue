<script setup lang="ts">
import { useLoadingHook } from '@/hooks/useLoading'
import { DialogInstance } from 'element-plus'

interface Props {
  title: string
  width?: string
  // 关闭窗口时是否弹窗提示
  showCancelTips?: boolean
  // 弹窗提示的内容
  tipsText?: string
  // 点击确认是按钮是否显示loading状态
  showLoading?: boolean
  // 是否禁用确认按钮
  disabledConfirm?: boolean
  // 是否关闭点击弹窗时是否关闭弹窗
  closeOnClickModal?: boolean
  // 是否显示底部操作按钮
  showFooter?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  closeOnClickModal: true,
  showFooter: true
})

const emits = defineEmits<{
  // done：confirm的回调函数，入参为是否关闭弹窗，不传则默认关闭
  (e: 'confirm', done: (flag?: boolean) => void): void
}>()
const dialogRef = ref<DialogInstance | null>(null)
const visible = defineModel({ default: false })
const { loading, loadingFunc } = useLoadingHook()

const handleBeforeClose = (finished?: () => void) => {
  const callClose = () => {
    if (finished) {
      finished()
    } else {
      visible.value = false
    }
  }
  if (props.showCancelTips) {
    ElMessageBox.confirm(props.tipsText ?? '是否确认关闭弹窗？')
      .then(() => {
        callClose()
      })
      .catch(() => {
        // visible.value = true
      })
  } else {
    callClose()
  }
}

const handleConfirm = () => {
  if (props.showLoading) {
    loading.value = true
  }

  // keepVisible 传 true 时 不关闭弹窗，只取消 loading 状态
  emits('confirm', (keepVisible: boolean = false) => {
    loadingFunc(false)
    visible.value = keepVisible
  })
}

defineExpose({
  dialogRef,
  open: () => (visible.value = true),
  close: () => (visible.value = false)
})
</script>

<template>
  <div class="dialog-wrapper">
    <el-dialog
      ref="dialogRef"
      v-model="visible"
      class="custom-dialog"
      lock-scroll
      align-center
      :width="width"
      :close-on-click-modal="closeOnClickModal"
      :before-close="handleBeforeClose"
    >
      <template #header>
        <div class="dialog-header">{{ title }}</div>
      </template>

      <slot></slot>

      <template v-if="showFooter" #footer>
        <div class="dialog-footer">
          <el-button @click="handleBeforeClose()">取消</el-button>
          <el-button :loading="loading" :disabled="disabledConfirm" type="primary" @click="handleConfirm">
            确认
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<style lang="scss" scoped>
.dialog-wrapper :deep(.custom-dialog) {
  padding: 0;
}
.dialog-wrapper :deep(.el-dialog__footer) {
  padding-top: 0;
}
.dialog-header {
  height: 44px;
  line-height: 44px;
  padding: 0 16px;
  font-size: 18px;
  color: #303133;
}
.dialog-footer {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  padding: 0 16px;
  height: 48px;
}
</style>
