<script lang="ts" setup>
import RoleAuthTree from '@/components/role-auth-tree'
import { addSysRoleApi, getSysRoleForEditApi, roleTypeOptions, updateSysRoleApi } from '@/api/system/roles-manage.api'
import { useFormHook } from '@/hooks/useForm'
import type { FormRules } from 'element-plus'
import { ROUTER_PATH } from '@/router/router-path'
import {
  ROLE_TYPE_EUNM,
  type AddSysRoleInput,
  type UpdateSysRoleAuthNavListModel,
} from '@/api/dto/system/roles-manage.dto'
import { appMessage } from '@/hooks/useNaiveApi'

export type FormData = AddSysRoleInput & {}

const route = useRoute()
const router = useRouter()

const roleId = computed(() => route.query.roleId as string)

const { formRef, validate } = useFormHook()

const formData = reactive<FormData>({
  roleType: ROLE_TYPE_EUNM.SYSTEM_MANAGE,
  enCode: '',
  roleName: '',
  navigationList: [],
  enabledMark: true,
  isSys: false,
  sortCode: 0,
  description: '',
})
/**表单验证规则 */
const rules = reactive<FormRules<FormData>>({
  roleType: [{ required: true, message: '请选择角色类型', trigger: 'blur' }],
  enCode: [{ required: true, message: '请输入角色编号', trigger: 'blur' }],
  roleName: [{ required: true, message: '请输入角色名称', trigger: 'blur' }],
})

const handleBack = () => {
  router.push(ROUTER_PATH.ROLES_MANAGE)
}

const handleSave = async () => {
  try {
    await validate()

    if (roleId.value) {
      // 修改
      await updateSysRole()
      appMessage.success('编辑成功')
    } else {
      // 新增
      await addSysRole()
      appMessage.success('新增成功')
    }
  } catch (error) {}
}

/**新增 */
const addSysRole = async () => {
  await addSysRoleApi(formData)
}

/**获取角色信息 */
onMounted(async () => {
  if (!roleId.value) return

  try {
    const res = await getSysRoleForEditApi({ id: roleId.value })
    formData.roleType = res.roleType
    formData.enCode = res.enCode
    formData.roleName = res.roleName
    formData.enabledMark = res.enabledMark
    formData.isSys = res.isSys
    formData.sortCode = res.sortCode
    formData.description = res.description
  } catch (error) {
    handleBack()
  }
})

/**编辑 */
const updateSysRole = async () => {
  await updateSysRoleApi({
    description: formData.description,
    enCode: formData.enCode,
    enabledMark: formData.enabledMark,
    id: roleId.value!,
    isSys: formData.isSys,
    roleName: formData.roleName,
    roleType: formData.roleType,
    sortCode: formData.sortCode,
  })
}

/**权限设置 */
const changeRoleAuthNavList = (list: UpdateSysRoleAuthNavListModel[]) => {
  formData.navigationList = list
}
</script>

<template>
  <div class="roles_manage_details-container">
    <div class="back-bar flex">
      <el-button type="primary" @click="handleSave">确定{{ roleId ? '保存' : '新增' }}</el-button>
      <el-button type="warning" @click="handleBack">返回</el-button>
    </div>

    <el-form ref="formRef" :model="formData" :rules="rules" label-width="100" class="layout-page-pd">
      <el-form-item v-if="!roleId" label="角色类型：" prop="roleType">
        <el-select v-model="formData.roleType">
          <el-option v-for="item in roleTypeOptions" :label="item.label" :value="item.value"></el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="角色编号：" prop="enCode">
        <el-input
          v-model.trim="formData.enCode"
          clearable
          class="inputClass"
          placeholder="请输入角色编号：(不能为中文，只能输入英文字母、数字)"
        />
      </el-form-item>

      <el-form-item label="角色名称：" prop="roleName">
        <el-input v-model.trim="formData.roleName" clearable class="inputClass" placeholder="请输入角色名称：" />
      </el-form-item>

      <n-collapse-transition appear :show="formData.roleType === ROLE_TYPE_EUNM.SYSTEM_MANAGE">
        <el-form-item v-if="!roleId" label="角色权限：" prop="navigationList">
          <RoleAuthTree
            :role-id="roleId"
            :role-type="formData.roleType"
            @change-role-auth-nav-list="changeRoleAuthNavList"
          />
        </el-form-item>
      </n-collapse-transition>

      <el-form-item label="是否启用：" prop="enabledMark">
        <el-switch v-model="formData.enabledMark" active-text="启用后角色才能被选择"> </el-switch>
      </el-form-item>

      <el-form-item label="系统管理：" prop="isSys">
        <el-switch v-model="formData.isSys" active-text="开启后无法删除"> </el-switch>
      </el-form-item>

      <el-form-item label="排序：" prop="sortCode">
        <el-input-number v-model="formData.sortCode" controls-position="right" :min="0"></el-input-number>
      </el-form-item>

      <el-form-item label="备注：" prop="description">
        <el-input
          v-model="formData.description"
          :autosize="{ minRows: 3, maxRows: 4 }"
          type="textarea"
          class="textareaClass"
          placeholder="请输入"
        />
      </el-form-item>
    </el-form>
  </div>
</template>
