<script setup lang="ts">
import { FormInstance, FormRules } from 'element-plus'
import {
  BankCardRefundAuditStatusConfig,
  BankCardRefundAuditStatus
} from '@/api/dto/discharge-settlement-refunds/bank-card-refund-records.dto.ts'
/**
 * 结算审核 - 银行卡退费审核弹窗
 */

interface RuleForm {
  // 审核状态
  auditStatus: number | undefined
  // 不通过原因
  rejectReason: string
}

const props = defineProps<{
  id: string
  confirmCallback?: (data: { auditStatus: number; rejectReason: string; done: () => void }) => void
}>()

const bankCardRefundAuditFormRef = ref<FormInstance>()

const bankCardRefundAuditForm = reactive<RuleForm>({
  auditStatus: undefined,
  rejectReason: ''
})

const bankCardRefundAuditDialogVisible = defineModel({ default: false })

const bankCardRefundAuditFormRules = reactive<FormRules<RuleForm>>({
  auditStatus: [{ required: true, message: '请选择审核状态', trigger: 'change' }],
  rejectReason: [{ required: true, message: '请填写不通过原因', trigger: 'blur' }]
})

watch(bankCardRefundAuditDialogVisible, (val) => {
  if (val) {
    bankCardRefundAuditFormRef.value?.resetFields()
  }
})

// 结算审核  - 点击确认
async function handleConfirmBankCardRefundAudit(done: (flag?: boolean) => void, formEl: FormInstance | undefined) {
  if (!formEl) return
  await formEl.validate((valid, fields) => {
    if (valid) {
      // 提交结算审核
      props.confirmCallback?.({
        auditStatus: bankCardRefundAuditForm.auditStatus!,
        rejectReason: bankCardRefundAuditForm.rejectReason,
        done
      })
    } else {
      console.log('提交结算审核 表单校验不通过：', fields)
      done(true)
    }
  })
}
</script>

<template>
  <CommonFormDialog
    v-model="bankCardRefundAuditDialogVisible"
    title="结算审核"
    width="600px"
    show-loading
    @confirm="handleConfirmBankCardRefundAudit($event, bankCardRefundAuditFormRef)"
  >
    <el-form
      ref="bankCardRefundAuditFormRef"
      size="large"
      label-width="110px"
      :model="bankCardRefundAuditForm"
      :rules="bankCardRefundAuditFormRules"
    >
      <el-form-item label="审核状态：" prop="auditStatus">
        <el-select v-model="bankCardRefundAuditForm.auditStatus" placeholder="请选择审核状态">
          <el-option
            :label="BankCardRefundAuditStatusConfig[BankCardRefundAuditStatus.APPROVED].label"
            :value="BankCardRefundAuditStatus.APPROVED"
          />
          <el-option
            :label="BankCardRefundAuditStatusConfig[BankCardRefundAuditStatus.REJECTED].label"
            :value="BankCardRefundAuditStatus.REJECTED"
          />
        </el-select>
      </el-form-item>
      <el-form-item
        v-if="bankCardRefundAuditForm.auditStatus === BankCardRefundAuditStatus.REJECTED"
        label="不通过原因："
        prop="rejectReason"
      >
        <el-input
          v-model="bankCardRefundAuditForm.rejectReason"
          type="textarea"
          clearable
          placeholder="请输入"
          :autosize="{ minRows: 3, maxRows: 10 }"
        />
      </el-form-item>
    </el-form>
  </CommonFormDialog>
</template>

<style scoped lang="scss"></style>
