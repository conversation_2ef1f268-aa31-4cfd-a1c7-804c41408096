/**
 * 出院结算办理 - 出院病人账单
 */

/**
 * 可选病区信息
 */
export interface SelectableWard {
  /** 病区代码 */
  deptcode: string

  /** 病区名称 */
  deptname: string
}

/**
 * 可选床位信息
 */
export interface SelectableBed {
  /** 床位 */
  bedno: string

  /** 住院号 */
  patno: string

  /** 姓名 */
  name: string

  /** 性别 1男2女 */
  sex: string

  /** 年龄 */
  age: string

  /** 结算方式 */
  setmeth: string
}


/**
 * 请求病人基本信息的参数
 */
export interface ReqBaseInfoDTO {
  /** 住院号 */
  patno: string
}

/**
 * 病人基本信息
 */
export interface PatientAccountsBaseInfo {
  /** 住院号 */
  patno: string

  /** 记账号 */
  inpno: string

  /** 病人id */
  patid: string

  /** 姓名 */
  name: string

  /** 性别 */
  sex: string

  /** 身份证号 */
  certno: string

  /** 出生日期 */
  birthday: string

  /** 国籍 */
  naty: string

  /** 民族 */
  caty: string

  /** 婚姻 */
  marry: string

  /** 电话号码 */
  phone: string

  /** 年龄 */
  age: string

  /** 地址 */
  addr: string

  /** 联系人 */
  contact: string

  /** 联系人电话 */
  contel: string

  /** 联系人关系 */
  relation: string

  /** 入院日期 */
  indate: string

  /** 出院日期 */
  outdate: string

  /** 入院科室代码 */
  indptcd: string

  /** 入院科室名称 */
  indptnm: string

  /** 当前科室代码 */
  curdptcd: string

  /** 病区 */
  curdptnm: string

  /** 医保1自费 2医保 3工伤保险 */
  insutype: string

  /** 医保就医类型 */
  medtype: string

  /** 医保地区分类 */
  plctype: string

  /** 结算 */
  setmeth: string

  /** 床位号 */
  bedno: string

  /** 医保登记状态 */
  insustate: string

  /** 预付费 */
  prepay: number

  /** 总费用 */
  sumpay: number

  /** 已清 */
  payment: number

  /** 待清 */
  unpayment: number

  /** 备注 */
  memo: string

  /** 入院科室地点 */
  indptaddr: string

  /** 价类 */
  pricetype: string

  /** 自付 */
  rate: number

  /** 自费百分比 */
  selfFundedPercentage: number

  /** 次数 */
  numberOfTimes: string

  /** 总天数 */
  totalDays: string

  /** 未接天数 */
  numberOfMissedDays: string

  /** 诊断 */
  diagnosis: string

  /** 已清日期 */
  clearedDate: string

  /** 结算日期 */
  settlementDate: string

  /** 特清自费 */
  teqingSelfFunded: string

  /** 待结 */
  pendingSettlement: string

  /** 直结交费 */
  directPayment: string

  /** 欠款 */
  debt: string

  /** 清账日期 */
  clearingDate: string

  /** 提示 */
  prompt: string

  /** 统筹 */
  overallPlanning: string
}

/**
 * 请求病人费用信息的参数
 */
export interface ReqExpenseInfoDTO {
  /** 记账号 */
  inpno: string
}

/**
 * 费用信息项
 */
export interface ExpenseInfo {
  /** 费用类别 */
  costtype: string

  /** 金额 */
  totalAmount: number

  /** 打折优惠前金额 */
  amountBeforeDiscountOffer: number
}

/**
 * 病人费用信息响应
 */
export interface ResExpenseInfoDTO {
  /** 费用信息列表 */
  expenseInfoList: ExpenseInfo[]

  /** 合计项 */
  amountToTerm: number

  /** 合计金额 */
  amountToYuan: number
}

/**
 * 请求分类明细的参数
 */
export interface ReqExpenseInfoOfClassificationDTO {
  /** 记账号 */
  inpno: string

  /** 费用类别 */
  costtype: string
}

/**
 * 费用明细项（全部明细和分类明细共用）
 */
export interface ResExpenseInfoOfAllOrClassificationDTO {
  /** 执行日期 */
  excutedate: string

  /** 费用明细id */
  detailsn?: string

  /** 医嘱分类 */
  advtype: string

  /** 方号 */
  advno: number

  /** 项目代码 */
  itemcode: string

  /** 项目名称 */
  itemname: string

  /** 数量 */
  quantity: number

  /** 金额 */
  totalamount: number

  /** 价格 */
  price: number

  /** 比例 */
  rate: number

  /** 自付 */
  selfamount: number
}

/**
 * 请求押金缴纳信息的参数
 */
export interface ReqDepositPaymentInfoDTO {
  /** 记账号 */
  inpno: string
}

/**
 * 押金缴纳信息项
 */
export interface ResDepositPaymentInfoDTO {
  /** 收款日期 */
  prepaydate: string

  /** 收款员 */
  operater: string

  /** 金额 */
  amount: number

  /** 币种 */
  paytype: number

  /** 预交单号 */
  prepayid: string

  /** 预收印刷号 */
  printno: string

  /** 交款人 */
  clientname: string

  /** 班别 */
  dayShiftOrNightShift: string

  /** 病区 */
  ward: string

  /** 备注 */
  notes: string
}