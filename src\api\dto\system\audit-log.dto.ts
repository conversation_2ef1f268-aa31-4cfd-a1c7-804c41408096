export interface getSysDataAuditInfoLogForPageInput {
  tableName: string
  operationType: string
  page: number
  rows: number
  operTimeBegin: string
  operTimeEnd: string
}

export interface DataAuditInfoLogModel {
  createTime: string
  createUserName: string
  dataAuditInfoLogId: string
  operTime: string
  operationType: string
  sqlParams: string
  sqlStatement: string
  tableName: string
}

export type getSysDataAuditInfoLogForPageOutput = DataAuditInfoLogModel[]
