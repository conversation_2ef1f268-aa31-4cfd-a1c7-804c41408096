import AutoImport from 'unplugin-auto-import/vite'
import Components from 'unplugin-vue-components/vite'
import { ElementPlusResolver } from 'unplugin-vue-components/resolvers'
import { NaiveUiResolver } from 'unplugin-vue-components/resolvers'

export default () => {
  return [
    Components({
      exclude: 'components',
      resolvers: [ElementPlusResolver({ importStyle: 'sass' }), NaiveUiResolver()],
    }),

    AutoImport({
      imports: ['vue', 'vue-router'],
      resolvers: [ElementPlusResolver({ importStyle: 'sass' })],
    }),
  ]
}
