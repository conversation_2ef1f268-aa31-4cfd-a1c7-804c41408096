import type { SysRoleByPageItemModel, UpdateSysRolePermissionInput } from '@/api/dto/system/roles-manage.dto'
import { ROLE_TYPE_EUNM } from '@/api/dto/system/roles-manage.dto'
import { roleTypeOptions, updateSysRolePermissionApi } from '@/api/system/roles-manage.api'
import { useFormHook } from '@/hooks/useForm'
import { ElMessageBox, ElForm, ElFormItem, ElSelect, ElOption, type FormRules } from 'element-plus'
import RoleAuthTree from '@/components/role-auth-tree'
import { appMessage } from '@/hooks/useNaiveApi'
import { NCollapseTransition } from 'naive-ui'

export type OpenParam = SysRoleByPageItemModel & {}

export type DialogData = UpdateSysRolePermissionInput

const { formRef, validate } = useFormHook()

const dialogData = reactive<DialogData>({
  roleId: '',
  roleType: ROLE_TYPE_EUNM.SYSTEM_MANAGE,
  navigationList: [],
})

/**表单验证规则 */
const rules = reactive<FormRules<DialogData>>({
  roleType: [{ required: true, message: '请选中账号类型', trigger: 'blur' }],
})

const dialogBody = () => {
  return (
    <>
      <ElForm ref={(el: any) => (formRef.value = el)} model={dialogData} rules={rules}>
        <ElFormItem label="角色类型：">
          <ElSelect v-model={dialogData.roleType}>
            {roleTypeOptions.map((item) => {
              return <ElOption label={item.label} value={item.value}></ElOption>
            })}
          </ElSelect>
        </ElFormItem>
      </ElForm>

      <NCollapseTransition appear show={dialogData.roleType === ROLE_TYPE_EUNM.SYSTEM_MANAGE}>
        <RoleAuthTree
          roleId={dialogData.roleId}
          roleType={dialogData.roleType}
          onChangeRoleAuthNavList={(list: any) => (dialogData.navigationList = list)}
        />
      </NCollapseTransition>
    </>
  )
}

const customClass = 'edit_role_auth_dialog'
/**修改角色权限弹窗 */
export const useEditRoleAuthDialog = async (openParam: OpenParam) => {
  dialogData.roleId = openParam.roleId
  dialogData.roleType = openParam.roleType

  await ElMessageBox.confirm(dialogBody, `${openParam.roleName} - 权限管理`, {
    customClass,
    customStyle: { width: '992px', maxWidth: 'none', maxHeight: '560px', overflow: 'auto' },
    closeOnHashChange: true,
    async beforeClose(action, instance, done) {
      if (action === 'confirm') {
        try {
          await validate()

          instance.confirmButtonLoading = true
          instance.showCancelButton = false

          await updateSysRolePermissionApi(dialogData, { loading: false })

          appMessage.success('修改成功')

          done()
        } finally {
          instance.confirmButtonLoading = false
          instance.showCancelButton = true
        }
      } else {
        done()
      }
    },
  })
}
