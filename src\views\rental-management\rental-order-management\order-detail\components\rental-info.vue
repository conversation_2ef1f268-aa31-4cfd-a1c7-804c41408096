<script setup lang="ts">
import {
  RentalOrderStatusConfig,
  RentalPayStatusConfig,
  ResRentalOrderDetail
} from '@/api/dto/rental-management/rental-order-management.dto.ts'
import { formatPrice } from '@/utils'

defineProps({
  detailInfo: {
    type: Object as PropType<ResRentalOrderDetail>,
    required: true,
    default: () => ({})
  }
})
</script>

<template>
  <div>
    <div class="common-panel-title">
      <span>租赁信息</span>
    </div>
    <el-descriptions border :column="2">
      <el-descriptions-item label="陪护床名称">{{ detailInfo.serverName }}</el-descriptions-item>
      <el-descriptions-item label="服务类型">{{ detailInfo.serverType }}</el-descriptions-item>
      <el-descriptions-item label="价格">{{ formatPrice(detailInfo.serverPrice) }}元/天</el-descriptions-item>
      <el-descriptions-item label="租赁时间">{{ detailInfo.leaseTime }}</el-descriptions-item>
      <el-descriptions-item label="状态">
        <el-tag
          :type="RentalOrderStatusConfig[detailInfo.orderStatus]?.tagType || RentalOrderStatusConfig.default.tagType"
        >
          {{ RentalOrderStatusConfig[detailInfo.orderStatus]?.label || RentalOrderStatusConfig.default.label }}
        </el-tag>
      </el-descriptions-item>
      <el-descriptions-item label="联系人姓名">{{ detailInfo.contactName }}</el-descriptions-item>
      <el-descriptions-item label="联系人电话">{{ detailInfo.contactPhone }}</el-descriptions-item>
      <el-descriptions-item label="订单金额（元）">{{ formatPrice(detailInfo.payFee) }}</el-descriptions-item>
      <el-descriptions-item label="支付状态">
        {{ RentalPayStatusConfig[detailInfo.payStatus]?.label || RentalPayStatusConfig.default.label }}
      </el-descriptions-item>
      <el-descriptions-item label="业务类型">{{ detailInfo.businessType }}</el-descriptions-item>
      <el-descriptions-item label="平台单号">{{ detailInfo.platformOrderNo }}</el-descriptions-item>
      <el-descriptions-item v-if="detailInfo.payTradeNo" label="支付流水号">{{
        detailInfo.payTradeNo || '-'
      }}</el-descriptions-item>
      <el-descriptions-item v-if="detailInfo.payTime" label="支付时间">{{
        detailInfo.payTime || '-'
      }}</el-descriptions-item>
      <el-descriptions-item v-if="detailInfo.confirmTime" label="确定时间"
        >{{ detailInfo.confirmTime }}
      </el-descriptions-item>
      <el-descriptions-item v-if="detailInfo.completeTime" label="完成时间"
        >{{ detailInfo.completeTime }}
      </el-descriptions-item>
      <el-descriptions-item v-if="detailInfo.cancelTime" label="取消时间"
        >{{ detailInfo.cancelTime }}
      </el-descriptions-item>
    </el-descriptions>
  </div>
</template>

<style scoped lang="scss"></style>
