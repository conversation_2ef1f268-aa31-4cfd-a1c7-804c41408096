import { ROUTER_PATH } from '../router-path'
import type { RouteRecordRaw } from 'vue-router'

const Layout = () => import('@/layout')

export const systemRouter: RouteRecordRaw = {
  path: ROUTER_PATH.SYSTEM,
  name: 'system',
  component: Layout,
  redirect: ROUTER_PATH.USER_GROUP,
  children: [
    {
      path: ROUTER_PATH.USER_GROUP_DETAILS,
      name: 'UserGroupDetails',
      component: () => import('@/views/system/user-group-details'),
      hidden: true,
      meta: {
        title: '新增/编辑用户组',
        activeMenu: ROUTER_PATH.USER_GROUP,
        leaveOff: true,
        noCache: true
      }
    },

    {
      path: ROUTER_PATH.DICTIONARY_DETAILS,
      name: 'DictionaryDetails',
      component: () => import('@/views/system/dictionary-details'),
      hidden: true,
      meta: {
        title: '数据字典详情',
        permissionFrom: 'dictionary',
        activeMenu: ROUTER_PATH.DICTIONARY,
        leaveOff: true,
        noCache: true
      }
    },

    {
      path: ROUTER_PATH.ORGANIZATION_DETAILS,
      name: 'OrganizationDetails',
      component: () => import('@/views/system/organization-details'),
      hidden: true,
      meta: {
        title: '新增/编辑组织机构',
        permissionFrom: 'organization',
        activeMenu: ROUTER_PATH.ORGANIZATION,
        leaveOff: true,
        noCache: true
      }
    },

    {
      path: ROUTER_PATH.ROLES_MANAGE_DETAILS,
      name: 'RolesManageDetails',
      component: () => import('@/views/system/roles-manage-details'),
      hidden: true,
      meta: {
        title: '新增/编辑角色',
        permissionFrom: 'role',
        activeMenu: ROUTER_PATH.ROLES_MANAGE,
        leaveOff: true,
        noCache: true
      }
    },

    {
      path: ROUTER_PATH.ADMIN_MANAGE_DETAILS,
      name: 'AdminManageDetails',
      component: () => import('@/views/system/admin-manage-details'),
      hidden: true,
      meta: {
        title: '新增/编辑账号',
        permissionFrom: 'manager',
        activeMenu: ROUTER_PATH.ADMIN_MANAGE,
        leaveOff: true,
        noCache: true
      }
    }
  ]
}
