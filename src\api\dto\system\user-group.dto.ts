export interface GetManagerGroupForPageInput {
  enabledMark: '' | boolean
  managerGroupName: string
  page: number
  rows: number
}

export interface GetManagerGroupItemModel {
  createTime: string
  description: string
  enabledMark: boolean
  managerGroupId: string
  managerGroupName: string
  modifyTime: string
}

export type GetManagerGroupForPageOutput = GetManagerGroupItemModel[]

export interface deleteSysManagerGroupInput {
  managerGroupId: string
}

export interface GetSysManagerGroupForUpdateInput {
  managerGroupId: string
}
export interface GetSysManagerGroupForUpdateOutput {
  managerGroupName: string
  roleList: { id: string; name: string }[]
  enabledMark: boolean
  isSys: boolean
  sortCode: number
  managerList: { id: string; name: string }[]
  description: string
}

export interface GetRolesForListInput {
  roleName: string
}

export interface RolesItemModel {
  roleId: string
  roleName: string
}

export type GetRolesForListOutput = RolesItemModel[]

export interface GetManagerForPageInput {
  managerKeyword: string
  page: number
  rows: number
}

export interface ManagerItemModel {
  account: string
  managerId: string
  organizationName: string
  realName: string
}

export type GetManagerForPageOutput = ManagerItemModel[]

export interface AddOrUpdateSysManagerGroupInput {
  managerGroupId?: string
  managerGroupName: string
  roleIds: string
  enabledMark: boolean
  isSys: boolean
  sortCode: number
  managerIds: string
  description: string
}

export interface GetRelationForPageByIdInput {
  managerGroupId: string
  name: string
  page: number
  relationName: 'manager' | 'role'
  rows: number
}

export interface RelationItemModel {
  encode: string
  name: string
}

export type GetRelationForPageByIdOutput = RelationItemModel[]
