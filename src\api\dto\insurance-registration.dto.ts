/**
 * 收费员端 - 医保登记模块
 */

/**
 * 医保登记信息 列表请求
 */
export interface ReqGetInsuranceRegistrationByPage extends InsuranceRegistrationSearchData {
  /** 登记情况 - 待登记 false;  已登记 true */
  hasRegistration?: boolean
  /* 当前页 */
  page: number
  /* 每页的记录数 */
  rows: number
}

// 医保登记信息 列表响应 - 未登记
export interface ResWaitingInsuranceRegistrationByPage {
  data: WaitingInsuranceRegistrationInfo[] // 实体列表
  pageIndex: number // 当前页
  pageSize: number // 每页记录数
  recordCount: number // 总记录数
  pageCount: number // 总页数
}

// 医保登记信息 列表响应 - 已登记
export interface ResInsuranceRegistrationByPage {
  data: RegisteredInsuranceInfo[] // 实体列表
  pageIndex: number // 当前页
  pageSize: number // 每页记录数
  recordCount: number // 总记录数
  pageCount: number // 总页数
}

/* 医保登记信息 - 详情 - 请求 */
export interface ReqInsuranceRegistrationInfo {
  /* 医保信息 ID */
  insuranceregistrationId: string
}

/* 医保登记信息 - 详情 - 响应 */
export interface ResInsuranceRegistrationInfo extends InsuranceRegistrationDetailInfo { }

/* 医保登记信息 - 确定登记成功 - 请求 */
export interface ReqRegistrationSuccessful {
  /* 医保信息 ID */
  insuranceregistrationId: string
}

/* 医保登记信息 - 确定登记成功 - 响应 */
export interface ResRegistrationSuccessful {
  msg: string
}

/* 医保登记信息 - 确定登记失败 - 请求 */
export interface ReqRegistrationFailed {
  /* 医保信息 ID */
  insuranceregistrationId: string
  /* 失败原因 */
  failureReason: string
}

/* 医保登记信息 - 确定登记失败 - 响应 */
export interface ResRegistrationFailed {
  msg: string
}

/**
 * 搜索数据类型
 */
export interface InsuranceRegistrationSearchData {
  /** 住院人姓名 */
  admissionName?: string
  /** 住院号 */
  admissionNo?: string
  /** 参保类别 */
  insuredPlaceType?: string
  /** 医保住院类型 */
  insuranceType?: string
  /** 住院科室 */
  hospitalizationDepartmentName?: string
}

/**
 * 待登记医保信息
 */
export interface WaitingInsuranceRegistrationInfo {
  /* 医保信息 ID */
  insuranceregistrationId: string
  /** 住院人姓名 */
  admissionName: string
  /** 住院号 */
  admissionNo: string
  /** 住院科室 */
  hospitalizationDepartmentName: string
  /** 参保类别 */
  insuredPlaceType: string
  /** 医保住院类型 */
  insuranceType: string
  /** 医保参保类型 */
  insuranceName: string
  /** 提交时间 */
  submissionTime: string
}

/**
 * 已登记医保信息
 */
export interface RegisteredInsuranceInfo extends WaitingInsuranceRegistrationInfo {
  // 登记情况
  registrationStatus: string
}

/**
 * 登记信息详情
 */
export interface InsuranceRegistrationDetailInfo extends RegisteredInsuranceInfo {
  /* 身份证图片 */
  idCardImages: string[]
  /* 身份证正面地址 */
  idcardFrontImgurl: string
  /* 身份证反面地址 */
  idcardBackImgurl: string
  /** 有无第三方 */
  isThirdpartyLiability?: string
  /** 流转信息 */
  resCirculationInfoDTOList: CirculationInfoItem[]
  /* 登记时间 */
  registrationTime?: string
  /* 失败原因 */
  failureReason?: string
}

/**
 * 流转信息 item
 */
export interface CirculationInfoItem {
  /* 流转状态 */
  registrationStatus: string
  /** 流转时间 */
  createTime: string
  /** 流转描述 */
  content: string
}

/**
 * 登记情况枚举
 */
export enum InsuredRegistrationStatus {
  /** 待登记 */
  NOT_REGISTERED = '0',
  /** 已登记 */
  REGISTERED = '1',
  /* 登记失败 */
  FAILED = '2'
}

// 状态映射配置
export const InsuredRegistrationStatusConfig = {
  [InsuredRegistrationStatus.NOT_REGISTERED]: { label: '待登记', tagType: 'warning' },
  [InsuredRegistrationStatus.REGISTERED]: { label: '登记成功', tagType: 'success' },
  [InsuredRegistrationStatus.FAILED]: { label: '登记失败', tagType: 'danger' },
  // 默认配置
  default: { label: '未知', tagType: 'info' }
}
