<script setup lang="ts">
import { FormInstance, FormRules } from 'element-plus'
import { ResRentalQuerydeptDTO } from '@/api/dto/rental-management/rental-order-management.dto.ts'
import { requestRentalSelectDepartmentList } from '@/api/rental-management.api.ts'

interface RuleForm {
  departmentCode: string
}

const props = defineProps<{
  confirmCallback: (departmentCode: string) => void
}>()

const inputFormRef = ref<FormInstance>()

// 确定服务
const inputForm = reactive({
  departmentCode: ''
})

const dialogVisible = defineModel({ default: false })

const departmentList = ref<ResRentalQuerydeptDTO[]>([] as ResRentalQuerydeptDTO[])

// 获取科室列表
const getDepartmentList = async () => {
  departmentList.value = await requestRentalSelectDepartmentList()
}

const inputFormRules = reactive<FormRules<RuleForm>>({
  departmentCode: [{ required: true, message: '请选择科室', trigger: 'blur' }]
})

watch(
  dialogVisible,
  (val) => {
    if (val) {
      getDepartmentList()
    }
  },
  {
    immediate: true
  }
)

// 确定服务弹窗 - 点击确认
async function handleConfirm(done: (flag?: boolean) => void, formEl: FormInstance | undefined) {
  if (!formEl) return
  await formEl.validate(async (valid, fields) => {
    if (valid) {
      try {
        // 提交
        await props.confirmCallback(inputForm.departmentCode)
        done(false)
        setTimeout(() => {
          formEl.resetFields()
        })
      } catch (e) {
        done(true)
      }
    } else {
      console.log('提交 确定服务 表单 失败 表单校验不通过：', fields)
    }
  })
}
</script>

<template>
  <CommonFormDialog
    v-model="dialogVisible"
    title="选择科室"
    width="552px"
    show-loading
    :disabled-confirm="!inputForm.departmentCode"
    @confirm="handleConfirm($event, inputFormRef)"
  >
    <div class="tips">请先选择科室后再选择就诊人（下拉选择科室或输入文字搜索）</div>
    <el-form ref="inputFormRef" class="mt-22" size="large" :model="inputForm" :rules="inputFormRules">
      <el-form-item label="选择科室：" prop="departmentCode">
        <el-select v-model="inputForm.departmentCode" filterable clearable placeholder="请选择科室">
          <el-option
            v-for="item in departmentList"
            :key="item.deptcode"
            :label="item.deptname"
            :value="item.deptcode"
          />
        </el-select>
      </el-form-item>
    </el-form>
  </CommonFormDialog>
</template>

<style scoped lang="scss">
.tips {
  height: 40px;
  border-radius: 4px;
  background: #fdf6ec;
  border: 1px solid #fbf0e1;
  color: #e6a23c;
  line-height: 40px;
  padding: 0 16px;
}

.mt-22 {
  margin-top: 22px;
}
</style>
