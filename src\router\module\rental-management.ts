import { ROUTER_PATH } from '@/router/router-path.ts'
import type { RouteRecordRaw } from 'vue-router'

const Layout = () => import('@/layout')

export const rentalManagementRouter: RouteRecordRaw = {
  path: ROUTER_PATH.RENTAL_MANAGEMENT,
  name: 'RentalManagement',
  redirect: ROUTER_PATH.RENTAL_TYPE,
  component: Layout,
  meta: { title: '陪护人租赁管理', icon: 'el-icon-umbrella' },
  children: [
    // 根级路由从动态路由接口获取
    // {
    //   path: ROUTER_PATH.RENTAL_TYPE,
    //   name: 'RentalType',
    //   component: () => import('@/views/rental-management/rental-type'),
    //   meta: { title: '陪护类型管理' }
    // },
    // {
    //   path: ROUTER_PATH.CAREGIVER_MANAGEMENT,
    //   name: 'CaregiverManagement',
    //   component: () => import('@/views/rental-management/caregiver-management'),
    //   meta: { title: '陪护人管理' }
    // },
    // {
    //   path: ROUTER_PATH.CAREGIVER_INTRO_MANAGEMENT,
    //   name: 'CaregiverIntroManagement',
    //   component: () => import('@/views/rental-management/caregiver-intro-management'),
    //   meta: { title: '陪护介绍管理' }
    // },
    // {
    //   path: ROUTER_PATH.RENTAL_ORDERS,
    //   name: 'RentalOrders',
    //   component: () => import('@/views/rental-management/rental-order-management/orders'),
    //   meta: { title: '租赁订单管理' }
    // },
    {
      path: ROUTER_PATH.RENTAL_ORDER_DETAIL,
      name: 'RentalOrderDetail',
      component: () => import('@/views/rental-management/rental-order-management/order-detail'),
      hidden: true,
      meta: {
        title: '租赁订单详情',
        leaveOff: true,
        activeMenu: ROUTER_PATH.RENTAL_ORDERS,
        noCache: true,
        permissionFrom: ROUTER_PATH.RENTAL_ORDERS,
        useTab: false
      }
    }
  ]
}
