/* eslint-disable */
/* prettier-ignore */
// biome-ignore format: off
// biome-ignore lint: off
// @ts-nocheck
// Generated by unplugin-svg-component
import 'vue'
declare module 'vue' {
  export interface GlobalComponents {
    SvgIcon: import("vue").DefineComponent<{
        name: {
            type: import("vue").PropType<"svg-alarm-manage" | "svg-analysis-manage" | "svg-audit-pass" | "svg-base-manage" | "svg-close" | "svg-crop" | "svg-delete" | "svg-device-active" | "svg-device-danger" | "svg-device-occupy" | "svg-device-primary" | "svg-device-success" | "svg-device-warning" | "svg-el-icon-s-tools" | "svg-enterprise-management" | "svg-examination-management" | "svg-exit-fullscreen" | "svg-folder-add" | "svg-folder-delete" | "svg-fullscreen" | "svg-heat-map" | "svg-home" | "svg-hospital-manage" | "svg-ic_finger" | "svg-ic_printer" | "svg-ic_selected" | "svg-ic_warning" | "svg-lock" | "svg-log-manage" | "svg-login-password" | "svg-login-user" | "svg-login-verify-code" | "svg-logout" | "svg-magic-stick" | "svg-menu-bell" | "svg-menu-expand" | "svg-menu-fold" | "svg-minus" | "svg-more-f" | "svg-organization_icon" | "svg-plus" | "svg-price-manage" | "svg-refresh" | "svg-search" | "svg-system_icon" | "svg-transaction" | "svg-unlock" | "svg-user-manage" | "svg-user" | "svg-workforce-management">;
            default: string;
            required: true;
        };
    }, {}, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<import("vue").ExtractPropTypes<{
        name: {
            type: import("vue").PropType<"svg-alarm-manage" | "svg-analysis-manage" | "svg-audit-pass" | "svg-base-manage" | "svg-close" | "svg-crop" | "svg-delete" | "svg-device-active" | "svg-device-danger" | "svg-device-occupy" | "svg-device-primary" | "svg-device-success" | "svg-device-warning" | "svg-el-icon-s-tools" | "svg-enterprise-management" | "svg-examination-management" | "svg-exit-fullscreen" | "svg-folder-add" | "svg-folder-delete" | "svg-fullscreen" | "svg-heat-map" | "svg-home" | "svg-hospital-manage" | "svg-ic_finger" | "svg-ic_printer" | "svg-ic_selected" | "svg-ic_warning" | "svg-lock" | "svg-log-manage" | "svg-login-password" | "svg-login-user" | "svg-login-verify-code" | "svg-logout" | "svg-magic-stick" | "svg-menu-bell" | "svg-menu-expand" | "svg-menu-fold" | "svg-minus" | "svg-more-f" | "svg-organization_icon" | "svg-plus" | "svg-price-manage" | "svg-refresh" | "svg-search" | "svg-system_icon" | "svg-transaction" | "svg-unlock" | "svg-user-manage" | "svg-user" | "svg-workforce-management">;
            default: string;
            required: true;
        };
    }>>, {
        name: "svg-alarm-manage" | "svg-analysis-manage" | "svg-audit-pass" | "svg-base-manage" | "svg-close" | "svg-crop" | "svg-delete" | "svg-device-active" | "svg-device-danger" | "svg-device-occupy" | "svg-device-primary" | "svg-device-success" | "svg-device-warning" | "svg-el-icon-s-tools" | "svg-enterprise-management" | "svg-examination-management" | "svg-exit-fullscreen" | "svg-folder-add" | "svg-folder-delete" | "svg-fullscreen" | "svg-heat-map" | "svg-home" | "svg-hospital-manage" | "svg-ic_finger" | "svg-ic_printer" | "svg-ic_selected" | "svg-ic_warning" | "svg-lock" | "svg-log-manage" | "svg-login-password" | "svg-login-user" | "svg-login-verify-code" | "svg-logout" | "svg-magic-stick" | "svg-menu-bell" | "svg-menu-expand" | "svg-menu-fold" | "svg-minus" | "svg-more-f" | "svg-organization_icon" | "svg-plus" | "svg-price-manage" | "svg-refresh" | "svg-search" | "svg-system_icon" | "svg-transaction" | "svg-unlock" | "svg-user-manage" | "svg-user" | "svg-workforce-management";
    }>;
  }
}
