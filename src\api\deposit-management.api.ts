import {
  ReqHospitalizationDepositOrderByPage,
  ReqHospitalizationDepositOrderForDetail,
  ResHospitalizationDepositOrderByPage,
  ResHospitalizationDepositOrderForDetail
} from '@/api/dto/deposit-management.dto.ts'
import { request } from '@/utils/axios-utils'
import type { ApiFunc } from 'axios'

/**
 * 住院预交金管理 模块
 */

/** 分页获取列表 */
export const requestHospitalizationDepositOrderByPage: ApiFunc<
  ReqHospitalizationDepositOrderByPage,
  ResHospitalizationDepositOrderByPage
> = (data) => {
  return request({
    url: '/foying/web/HospitalizationDepositOrder/getHospitalizationDepositOrderByPage',
    method: 'post',
    data,
    retonly: false
  })
}

/** 获取详情 */
export const requestHospitalizationDepositOrderForDetail: ApiFunc<
  ReqHospitalizationDepositOrderForDetail,
  ResHospitalizationDepositOrderForDetail
> = (data) => {
  return request({
    url: '/foying/web/HospitalizationDepositOrder/getHospitalizationDepositOrderForDetail',
    method: 'post',
    data
  })
}
