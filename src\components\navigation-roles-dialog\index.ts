import NavigationRolesDialog, { type OpenParams } from './navigation-roles-dialog.vue'

export type NewDictionaryDialogType = InstanceType<typeof NavigationRolesDialog>
export type NewDictionaryDialogProps = NewDictionaryDialogType['$props']

let overlayComponent: NewDictionaryDialogType | null = null

function createdOverlay(options?: NewDictionaryDialogProps) {
  const overlayElement = document.createElement('div')
  document.body.appendChild(overlayElement)

  const OverlayInstance = createApp(NavigationRolesDialog, options)
  overlayComponent = OverlayInstance.mount(overlayElement) as NewDictionaryDialogType
}

/**打开新增、修改字典弹窗 */
export const useNavigationRolesDialogHook = () => {
  const open = async (openOpts: OpenParams, dialogOpts?: NewDictionaryDialogProps) => {
    if (!overlayComponent) {
      createdOverlay(dialogOpts)
    }
    overlayComponent!.__open.call(overlayComponent, openOpts)
  }

  const close = () => {
    overlayComponent?.__close.call(overlayComponent)
  }

  return { open, close }
}

export default NavigationRolesDialog
