<script lang="ts" setup>
import SearchBar from './search-bar.vue'
import BasePagination from '@/components/base-pagination'
import { usePaginationHook } from '@/hooks/usePagination'
import { getRelationForPageByIdApi } from '@/api/system/user-group.api'
import type { GetRelationForPageByIdOutput } from '@/api/dto/system/user-group.dto'
import { useLoadingHook } from '@/hooks/useLoading'

const props = defineProps<{
  managerGroupId: string
}>()

const { currentPage, pageSize, total } = usePaginationHook()

const keyword = ref('')

const tableData = ref<GetRelationForPageByIdOutput>([])

const { loading, loadingFunc } = useLoadingHook()
const getRelationForPageById = async () => {
  try {
    const { data, recordCount } = await getRelationForPageByIdApi(
      {
        managerGroupId: props.managerGroupId,
        page: currentPage.value,
        rows: pageSize.value,
        name: keyword.value,
        relationName: 'manager',
      },
      { loading: loadingFunc, retonly: false }
    )
    tableData.value = data
    total.value = recordCount!
  } catch (error) {
    tableData.value = []
  }
}

const hadnleSearch = () => {
  currentPage.value = 1
  getRelationForPageById()
}

watch([currentPage, pageSize], () => getRelationForPageById())

defineExpose({ getRelationForPageById })
</script>

<template>
  <div class="manager_table-container">
    <SearchBar v-model="keyword" @search="hadnleSearch" />

    <BaseTable v-loading="loading" :data="tableData" border height="324">
      <el-table-column prop="name" label="账户名称" />
      <el-table-column prop="encode" label="账户类型" />
    </BaseTable>

    <BasePagination v-model:current-page="currentPage" v-model:page-size="pageSize" :total="total" />
  </div>
</template>
