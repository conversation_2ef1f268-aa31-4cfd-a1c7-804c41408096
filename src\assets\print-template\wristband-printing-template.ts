/**
 * 手腕带打印模板
 *
 * 模板内容填充对象示例：
 * {
 *   // 患者名
 *   "patientName": "张小谦",
 *   // 年龄
 *   "age": "Y73",
 *   // 性别
 *   "gender": "男",
 *   // 科别
 *   "department": "内分泌科住院",
 *   // 住院号
 *   "hospitalizationNumber": "********",
 *   // 记帐号
 *   "billingAccount": "81258751238125875123",
 *   // 二维码值 - 对应记帐号
 *   "billingAccountBar": "81258751238125875123",
 * }
 *
 */

// https://ccsimple.github.io/vue-plugin-hiprint/
// 从网站设计，之后导出 json 格式
// 中标腕带机所适配的模板
// const WRISTBAND_PRINTING_TEMPLATE_JSON = `
// {"panels":[{"index":0,"name":1,"height":25,"width":260,"paperHeader":0,"paperFooter":72,"printElements":[{"options":{"left":132.5,"top":10,"height":13.5,"width":27,"title":"年龄:","fontSize":9.75,"coordinateSync":false,"widthHeightSync":false,"qrCodeLevel":0,"right":151.9921875,"bottom":16.********,"vCenter":138.4921875,"hCenter":9.********},"printElementType":{"type":"text"}},{"options":{"left":158.5,"top":8.5,"height":12,"width":67.5,"field":"age","testData":"Y73","fontSize":13.5,"fontWeight":"bold","right":189.**************,"bottom":14.25,"vCenter":170.**************,"hCenter":9.375,"coordinateSync":false,"widthHeightSync":false,"qrCodeLevel":0},"printElementType":{"type":"text"}},{"options":{"left":45,"top":10,"height":13.5,"width":82.5,"field":"patientName","testData":"张小谦","fontSize":13.5,"fontWeight":"bold","right":122.49373626708984,"bottom":14.25,"vCenter":80.49373626708984,"hCenter":9.375},"printElementType":{"type":"text"}},{"options":{"left":228,"top":9,"height":13.5,"width":28.5,"title":"性别:","fontSize":9.75,"coordinateSync":false,"widthHeightSync":false,"qrCodeLevel":0},"printElementType":{"type":"text"}},{"options":{"left":255,"top":9,"height":13.5,"width":22.5,"field":"gender","testData":"男","fontSize":12,"fontWeight":"bold","right":270.24609375,"bottom":21.24609375,"vCenter":258.********,"hCenter":14.49609375},"printElementType":{"type":"text"}},{"options":{"left":17.5,"top":10,"height":12,"width":28.5,"title":"姓名:","fontSize":9.75,"coordinateSync":false,"widthHeightSync":false,"qrCodeLevel":0,"right":40.99999237060547,"bottom":14.**************1,"vCenter":26.74999237060547,"hCenter":8.**************1},"printElementType":{"type":"text"}},{"options":{"left":45,"top":22.5,"height":21,"width":82.5,"field":"department","testData":"内分泌科住院内分泌科住院","fontWeight":"bold","right":124.**************,"bottom":41.499984********,"vCenter":82.**************,"hCenter":30.***************,"coordinateSync":false,"widthHeightSync":false,"textContentVerticalAlign":"middle","qrCodeLevel":0},"printElementType":{"type":"text"}},{"options":{"left":132.5,"top":22.5,"height":43.5,"width":43.5,"qrcodeType":"qrcode","testData":"qrcode","right":170.*************,"bottom":62.**************,"vCenter":149.*************,"hCenter":41.**************,"qrCodeLevel":0,"coordinateSync":false,"widthHeightSync":false,"hideTitle":true,"field":"billingAccountBar","title":"二维码"},"printElementType":{"title":"二维码","type":"qrcode"}},{"options":{"left":17.5,"top":27.5,"height":9.75,"width":28.5,"title":"科别:","fontSize":9.75,"coordinateSync":false,"widthHeightSync":false,"qrCodeLevel":0,"right":46.**************,"bottom":39.75,"vCenter":31.**************,"hCenter":34.875},"printElementType":{"type":"text"}},{"options":{"left":181,"top":27.5,"height":9.75,"width":70.5,"title":"佛山市中医院","fontSize":9.75,"right":255.*************,"bottom":37.**************,"vCenter":220.*************,"hCenter":32.**************,"coordinateSync":false,"widthHeightSync":false,"qrCodeLevel":0},"printElementType":{"type":"text"}},{"options":{"left":182.5,"top":42.5,"height":9.75,"width":33,"title":"记帐号:","coordinateSync":false,"widthHeightSync":false,"qrCodeLevel":0,"right":210.**************,"bottom":48.75,"vCenter":193.**************,"hCenter":43.875},"printElementType":{"type":"text"}},{"options":{"left":52.5,"top":42.5,"height":22.5,"width":75,"field":"hospitalizationNumber","testData":"********1234565","fontSize":13.5,"fontWeight":"bold","right":123.**************,"bottom":59.25,"vCenter":85.**************,"hCenter":52.5,"coordinateSync":false,"widthHeightSync":false,"textContentVerticalAlign":"middle","lineHeight":11.25,"qrCodeLevel":0},"printElementType":{"type":"text"}},{"options":{"left":215,"top":42.5,"height":22.5,"width":64.5,"field":"billingAccount","testData":"81258751238125875123","fontWeight":"bold","right":279.75,"bottom":57.75,"vCenter":243.75,"hCenter":48.75},"printElementType":{"type":"text"}},{"options":{"left":17.5,"top":47.5,"height":12,"width":36,"title":"住院号:","fontSize":9.75,"coordinateSync":false,"widthHeightSync":false,"qrCodeLevel":0},"printElementType":{"type":"text"}}],"paperNumberLeft":707,"paperNumberTop":48,"paperNumberDisabled":true,"paperNumberContinue":true,"fontFamily":"STHeitiSC-Light","leftOffset":46,"watermarkOptions":{"content":"","fillStyle":"rgba(184, 184, 184, 0.3)","fontSize":"14px","rotate":25,"width":200,"height":200,"timestamp":false,"format":"YYYY-MM-DD HH:mm"},"panelLayoutOptions":{"layoutType":"column","layoutRowGap":0,"layoutColumnGap":0}}]}
// `

// 调整适配医院旧的腕带打印机及模板
const WRISTBAND_PRINTING_TEMPLATE_JSON = `
{"panels":[{"index":0,"name":1,"height":25,"width":260,"paperHeader":0,"paperFooter":72,"printElements":[{"options":{"left":111,"top":3,"height":13.5,"width":27,"title":"年龄:","fontSize":9.75,"coordinateSync":false,"widthHeightSync":false,"qrCodeLevel":0,"right":136.5,"bottom":21,"vCenter":123,"hCenter":14.25},"printElementType":{"type":"text"}},{"options":{"left":138,"top":4.5,"height":12,"width":67.5,"field":"age","testData":"Y73","fontSize":13.5,"fontWeight":"bold","right":205.2421875,"bottom":16.********,"vCenter":171.4921875,"hCenter":10.********,"coordinateSync":false,"widthHeightSync":false,"qrCodeLevel":0},"printElementType":{"type":"text"}},{"options":{"left":23.5,"top":9.5,"height":13.5,"width":82.5,"field":"patientName","testData":"张小谦","fontSize":13.5,"fontWeight":"bold","right":122.49373626708984,"bottom":14.25,"vCenter":80.49373626708984,"hCenter":9.375},"printElementType":{"type":"text"}},{"options":{"left":201,"top":16.5,"height":13.5,"width":28.5,"title":"性别:","fontSize":9.75,"coordinateSync":false,"widthHeightSync":false,"qrCodeLevel":0,"right":231,"bottom":21.********,"vCenter":216.75,"hCenter":15.24609375},"printElementType":{"type":"text"}},{"options":{"left":228,"top":17.5,"height":13.5,"width":22.5,"field":"gender","testData":"男","fontSize":12,"fontWeight":"bold","right":246.********,"bottom":21,"vCenter":235.********,"hCenter":14.25},"printElementType":{"type":"text"}},{"options":{"left":-4.5,"top":11.5,"height":12,"width":28.5,"title":"姓名:","fontSize":9.75,"coordinateSync":false,"widthHeightSync":false,"qrCodeLevel":0,"right":40.99999237060547,"bottom":14.**************1,"vCenter":26.74999237060547,"hCenter":8.**************1},"printElementType":{"type":"text"}},{"options":{"left":23.5,"top":22,"height":21,"width":82.5,"field":"department","testData":"内分泌科住院内分泌科住院","fontWeight":"bold","right":124.**************,"bottom":41.499984********,"vCenter":82.**************,"hCenter":30.***************,"coordinateSync":false,"widthHeightSync":false,"textContentVerticalAlign":"middle","qrCodeLevel":0},"printElementType":{"type":"text"}},{"options":{"left":111,"top":18,"height":43.5,"width":43.5,"qrcodeType":"qrcode","testData":"qrcode","right":154.5,"bottom":61.5,"vCenter":132.75,"hCenter":39.75,"qrCodeLevel":0,"coordinateSync":false,"widthHeightSync":false,"hideTitle":true,"field":"billingAccountBar","title":"二维码"},"printElementType":{"title":"二维码","type":"qrcode"}},{"options":{"left":-4,"top":27,"height":9.75,"width":28.5,"title":"科别:","fontSize":9.75,"coordinateSync":false,"widthHeightSync":false,"qrCodeLevel":0,"right":46.**************,"bottom":39.75,"vCenter":31.**************,"hCenter":34.875},"printElementType":{"type":"text"}},{"options":{"left":159.5,"top":29,"height":9.75,"width":70.5,"title":"佛山市中医院","fontSize":9.75,"right":255.*************,"bottom":37.**************,"vCenter":220.*************,"hCenter":32.**************,"coordinateSync":false,"widthHeightSync":false,"qrCodeLevel":0},"printElementType":{"type":"text"}},{"options":{"left":159.5,"top":42,"height":9.75,"width":33,"title":"记帐号:","coordinateSync":false,"widthHeightSync":false,"qrCodeLevel":0,"right":210.**************,"bottom":48.75,"vCenter":193.**************,"hCenter":43.875},"printElementType":{"type":"text"}},{"options":{"left":30,"top":42,"height":22.5,"width":75,"field":"hospitalizationNumber","testData":"********1234565","fontSize":13.5,"fontWeight":"bold","right":123.**************,"bottom":59.25,"vCenter":85.**************,"hCenter":52.5,"coordinateSync":false,"widthHeightSync":false,"textContentVerticalAlign":"middle","lineHeight":11.25,"qrCodeLevel":0},"printElementType":{"type":"text"}},{"options":{"left":191.5,"top":42,"height":22.5,"width":64.5,"field":"billingAccount","testData":"81258751238125875123","fontWeight":"bold","right":279.75,"bottom":57.75,"vCenter":243.75,"hCenter":48.75},"printElementType":{"type":"text"}},{"options":{"left":-3.5,"top":42.5,"height":12,"width":36,"title":"住院号:","fontSize":9.75,"coordinateSync":false,"widthHeightSync":false,"qrCodeLevel":0},"printElementType":{"type":"text"}}],"paperNumberLeft":707,"paperNumberTop":48,"paperNumberDisabled":true,"paperNumberContinue":true,"fontFamily":"STHeitiSC-Light","leftOffset":46,"watermarkOptions":{"content":"","fillStyle":"rgba(184, 184, 184, 0.3)","fontSize":"14px","rotate":25,"width":200,"height":200,"timestamp":false,"format":"YYYY-MM-DD HH:mm"},"panelLayoutOptions":{"layoutType":"column","layoutRowGap":0,"layoutColumnGap":0}}]}
`

function getWristbandPrintingTemplate() {
  return JSON.parse(WRISTBAND_PRINTING_TEMPLATE_JSON)
}

export { getWristbandPrintingTemplate }
