<script setup lang="ts">
import { DepositStatusConfig, ResHospitalizationDepositOrderForDetail } from '@/api/dto/deposit-management.dto.ts'

defineProps<{
  detailInfo: ResHospitalizationDepositOrderForDetail
}>()
</script>

<template>
  <div class="content-wrapper">
    <div class="content">
      <div class="title">
        <span>住院信息</span>
      </div>
      <el-descriptions border :column="2">
        <el-descriptions-item label="住院人姓名">{{ detailInfo.patientName ?? '-' }}</el-descriptions-item>
        <el-descriptions-item label="住院号">{{ detailInfo.admissionNo ?? '-' }}</el-descriptions-item>
        <el-descriptions-item label="住院科室">{{ detailInfo.departmentName ?? '-' }}</el-descriptions-item>
        <el-descriptions-item label="主治医生">{{ detailInfo.doctorName ?? '-' }}</el-descriptions-item>
        <el-descriptions-item label="住院床号">{{ detailInfo.bedNo ?? '-' }}</el-descriptions-item>
        <el-descriptions-item label="入院日期">{{ detailInfo.inDate ?? '-' }}</el-descriptions-item>
      </el-descriptions>
    </div>

    <div class="content">
      <div class="title">
        <span>缴费信息</span>
      </div>
      <el-descriptions border :column="2">
        <el-descriptions-item label="业务类型">{{ detailInfo.orderReason ?? '-' }}</el-descriptions-item>
        <el-descriptions-item label="状态">
          <el-tag :type="DepositStatusConfig[detailInfo.orderStatus]?.tagType || DepositStatusConfig.default.tagType">
            {{ DepositStatusConfig[detailInfo.orderStatus]?.label || DepositStatusConfig.default.label }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="支付金额（元）">{{ detailInfo.payFee?.toFixed(2) ?? '-' }}</el-descriptions-item>
        <el-descriptions-item label="押金余额（元）">{{ detailInfo.balance?.toFixed(2) ?? '-' }}</el-descriptions-item>
        <el-descriptions-item label="医院单号">{{ detailInfo.hisOrderNo ?? '-' }}</el-descriptions-item>
        <el-descriptions-item label="平台单号">{{ detailInfo.platformOrderNo ?? '-' }}</el-descriptions-item>
        <el-descriptions-item label="支付流水号">{{ detailInfo.payTradeNo ?? '-' }}</el-descriptions-item>
        <el-descriptions-item label="下单时间">{{ detailInfo.createTime ?? '-' }}</el-descriptions-item>
        <el-descriptions-item label="支付时间">{{ detailInfo.payTime ?? '-' }}</el-descriptions-item>
      </el-descriptions>
    </div>
  </div>
</template>

<style scoped lang="scss">
.content {
  & .title {
    margin: 30px 0 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 16px;
  }

  &-wrapper {
    padding: 0 20px 30px;
    display: flex;
    flex-direction: column;
    flex: 1;
    overflow: auto;
  }
}
</style>
