<script setup lang="ts">
import {
  ResInpatientCaregiverPersonItem,
  ReqAddInpatientCaregiverPerson,
  ReqUpdateInpatientCaregiverPerson
} from '@/api/dto/rental-management/caregiver-management.dto.ts'
import { useFormHook } from '@/hooks/useForm.ts'
import { FormRules } from 'element-plus'

// 性别选项
const genderOptions = [
  { label: '男', value: '1' },
  { label: '女', value: '2' }
]

const props = defineProps<{
  detailInfo: ResInpatientCaregiverPersonItem
  title: string
  confirmCallback: (data: ResInpatientCaregiverPersonItem) => Promise<void>
}>()

const { formRef } = useFormHook()

// 表单数据
const formData = ref<ResInpatientCaregiverPersonItem>({} as ResInpatientCaregiverPersonItem)

const visible = defineModel({ default: false })

watch(
  visible,
  (v) => {
    if (v) {
      // 重置表单，避免在点击编辑后再点击新增时候，因为弹窗组件没有销毁导致数据不清空
      formRef.value?.resetFields()
      formData.value = JSON.parse(JSON.stringify(props.detailInfo))
    }
  },
  {
    immediate: true
  }
)

const rules = reactive<FormRules>({
  name: [{ required: true, message: '请输入陪护人名称', trigger: 'blur' }],
  gender: [{ required: true, message: '请选择性别', trigger: 'change' }],
  age: [{ required: true, message: '请输入年龄', trigger: 'blur' }],
  idCard: [{ required: true, message: '请输入身份证号', trigger: 'blur' }],
  phone: [{ required: true, message: '请输入联系电话', trigger: 'blur' }],
  status: [{ required: true, message: '请选择状态', trigger: 'change' }]
})

// 构建提交数据
function buildSubmitData() {
  // 根据是否有ID区分新增和编辑
  if (formData.value.inpatientCaregiverPersonId) {
    // 编辑
    const updateData: ReqUpdateInpatientCaregiverPerson = {
      inpatientCaregiverPersonId: formData.value.inpatientCaregiverPersonId,
      name: formData.value.name,
      gender: formData.value.gender,
      age: formData.value.age,
      idCard: formData.value.idCard,
      phone: formData.value.phone,
      status: formData.value.status,
      remark: formData.value.remark
    }
    return updateData
  } else {
    // 新增
    const addData: ReqAddInpatientCaregiverPerson = {
      name: formData.value.name,
      gender: formData.value.gender,
      age: formData.value.age,
      idCard: formData.value.idCard,
      phone: formData.value.phone,
      status: formData.value.status,
      remark: formData.value.remark
    }
    return addData
  }
}

const handleConfirm = async (done: (keepVisible: boolean) => void) => {
  await formRef.value!.validate(async (valid) => {
    if (valid) {
      try {
        const submitData = buildSubmitData()
        await props.confirmCallback(submitData as unknown as ResInpatientCaregiverPersonItem)
        done(false)
        setTimeout(() => {
          formRef.value!.resetFields()
        })
      } catch (error) {
        // 结束按钮 loading，但是不关闭弹窗
        done(true)
      }
    } else {
      done(true)
    }
  })
}
</script>

<template>
  <CommonFormDialog
    v-model="visible"
    :title="title"
    width="732px"
    showLoading
    :showCancelTips="false"
    @confirm="handleConfirm"
  >
    <el-form ref="formRef" :model="formData" :rules="rules" label-width="auto" label-position="right" size="large">
      <el-row>
        <el-col :span="24">
          <el-form-item label="陪护人名称：" prop="name">
            <el-input v-model="formData.name" clearable placeholder="请输入" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row>
        <el-col :span="12">
          <el-form-item label="性别：" prop="gender">
            <el-select v-model="formData.gender" placeholder="请选择" size="large" clearable>
              <el-option v-for="item in genderOptions" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="年龄：" prop="age">
            <el-input v-model.number="formData.age" clearable placeholder="请输入" type="number" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row>
        <el-col :span="24">
          <el-form-item label="身份证号：" prop="idCard">
            <el-input v-model="formData.idCard" clearable placeholder="请输入" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row>
        <el-col :span="24">
          <el-form-item label="联系电话：" prop="phone">
            <el-input v-model="formData.phone" clearable placeholder="请输入" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row>
        <el-col :span="12">
          <el-form-item label="状态：" prop="status">
            <el-radio-group v-model="formData.status">
              <el-radio :value="true">启用</el-radio>
              <el-radio :value="false">禁用</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row>
        <el-col :span="24">
          <el-form-item label="备注：" prop="remark">
            <el-input
              v-model="formData.remark"
              clearable
              type="textarea"
              placeholder="请输入"
              :autosize="{ minRows: 2, maxRows: 6 }"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </CommonFormDialog>
</template>

<style lang="scss" scoped></style>
