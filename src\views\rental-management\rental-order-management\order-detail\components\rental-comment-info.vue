<script setup lang="ts">
import { ResRentalOrderDetail } from '@/api/dto/rental-management/rental-order-management.dto.ts'

defineProps({
  commentInfo: {
    type: Object as PropType<ResRentalOrderDetail>,
    required: true,
    default: () => ({})
  }
})
</script>

<template>
  <div>
    <div class="common-panel-title">
      <span>评价信息</span>
    </div>
    <el-descriptions border :column="2">
      <el-descriptions-item label="服务评价">
        <el-rate :model-value="commentInfo.serverEvaluate" allow-half disabled />
      </el-descriptions-item>
      <el-descriptions-item label="评价时间">{{ commentInfo.evaluateTime }}</el-descriptions-item>
      <el-descriptions-item label="评价内容">{{ commentInfo.evaluateContent }}</el-descriptions-item>
    </el-descriptions>
  </div>
</template>

<style scoped lang="scss"></style>
