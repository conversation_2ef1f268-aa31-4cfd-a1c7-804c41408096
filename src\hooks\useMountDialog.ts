import ElConfig from '@/components/el-config/el-config.vue'
import { render, onUnmounted, watch, nextTick } from 'vue'

/**
 * 自定义钩子用于在页面上渲染和管理对话框组件的显示和隐藏
 * 该钩子接受一个对话框组件和一些选项作为参数，并提供打开和关闭对话框的方法
 *
 * @param DialogComponent 要渲染的对话框组件
 * @param options 传递给对话框组件的初始化props选项
 * @param hookOptions 钩子自身的配置选项，例如 { destroyOnClose: boolean }
 * @returns 返回包含对话框引用、打开和关闭对话框方法的对象
 */
export const useMountDialog = <T extends new (...args: any) => any, P extends InstanceType<T>['$props']>(
  DialogComponent: T,
  options?: P,
  hookOptions?: { destroyOnClose?: boolean }
) => {
  // 是否在关闭对话框时销毁组件，避免下次打开弹窗时数据为上次打开时的数据
  const { destroyOnClose = true } = hookOptions || {}

  // 初始化传入的默认props
  let opts = { ...options }
  // 创建一个ref来存储对话框组件的实例
  const dialogRef = ref<InstanceType<T> | null>(null)

  // 挂载组件的容器
  const box = document.createElement('div')
  // let parentEl = document.body
  let parentEl = document.getElementById('app')
  if (!parentEl) {
    parentEl = document.body
  }
  parentEl.appendChild(box)

  // 组件是否显示
  const visible = ref(false)
  const isMounted = ref(false)

  const unmountDialog = () => {
    render(null, box)
    isMounted.value = false
  }
  /**
   * 渲染对话框组件
   *
   * 此函数负责在指定的容器中渲染对话框组件它使用了ElConfig和DialogComponent组件
   * ElConfig组件用于提供全局配置，如语言、主题等
   * 通过传递props和事件处理器来配置对话框的显示和交互逻辑
   */
  const renderDialog = () => {
    render(
      h(ElConfig, null, {
        default: () =>
          h(DialogComponent, {
            ref: dialogRef,
            modelValue: visible.value,
            'onUpdate:modelValue': (val: boolean) => {
              visible.value = val
            },
            ...opts
          })
      }),
      box
    )
    isMounted.value = true
  }

  if (destroyOnClose) {
    watch(visible, (val) => {
      if (val) {
        renderDialog()
      } else if (isMounted.value) {
        unmountDialog()
      }
    })
  }

  /**
   * 打开对话框的函数
   * 如果提供了新的配置项，会将这些配置项合并到当前配置中
   *
   * @param newOptions 可选参数，新的配置项如果提供，将覆盖当前配置
   */
  const open = (newOptions?: P) => {
    if (newOptions) {
      Object.assign(opts, newOptions)
    }
    visible.value = true

    if (!destroyOnClose && !isMounted.value) {
      nextTick(() => {
        renderDialog()
      })
    }
  }
  /**
   * 关闭对话框的函数
   */
  const close = () => {
    visible.value = false
  }
  /**
   * 页面退出时销毁组件，防止内存泄漏
   */
  onUnmounted(() => {
    if (isMounted.value) {
      unmountDialog()
    }
    parentEl.removeChild(box)
  })

  return { dialogRef, open, close }
}
