import { useGlobalStore } from '@/stores/global.store'
import { createDiscreteApi, type ConfigProviderProps, darkTheme, lightTheme } from 'naive-ui'
import { storeToRefs } from 'pinia'

/**pinia是否已经挂载 */
const piniaIsMounted = ref(false)

const isDark = ref(false)

const getGlobalStoreDarkValue = () => {
  if (piniaIsMounted.value) return

  try {
    isDark.value = useGlobalStore().isDark
    piniaIsMounted.value = true
  } catch (error) {
    setTimeout(() => {
      getGlobalStoreDarkValue()
    }, 120)
  }
}

getGlobalStoreDarkValue()

watch(piniaIsMounted, (mountedFlag) => {
  if (mountedFlag) {
    const { isDark: isDarkFromPinia } = storeToRefs(useGlobalStore())
    watch(isDarkFromPinia, (isDrkFlag) => {
      isDark.value = isDrkFlag
    })
  }
})

const configProviderPropsRef = computed<ConfigProviderProps>(() => {
  return {
    theme: isDark.value ? darkTheme : lightTheme,
  }
})

export const { message: appMessage } = createDiscreteApi(['message'], {
  configProviderProps: configProviderPropsRef,
})
