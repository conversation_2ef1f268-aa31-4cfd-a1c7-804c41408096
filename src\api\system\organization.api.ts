import type { ApiFunc } from 'axios'
import { authRequest } from '../utils.api'
import type {
  AddSysOrganizationInput,
  DeleteSysOrganizationInput,
  GetLeftSysOrganizationForShowOutput,
  GetRightSysOrganizationForShowOutput,
  GetSysOrganizationForPageInput,
  GetSysOrganizationForPageOutput,
  GetSysOrganizationForUpdateInput,
  GetSysOrganizationForUpdateOutput,
  UpdateSysOrganizationInput,
} from '../dto/system/organization.dto'

/**机构tree */
export const getLeftSysOrganizationForShowApi: ApiFunc<undefined, GetLeftSysOrganizationForShowOutput> = (options) => {
  return authRequest({ url: 'getLeftSysOrganizationForShow', ...options })
}

/**机构列表 */
export const getSysOrganizationForPageApi: ApiFunc<GetSysOrganizationForPageInput, GetSysOrganizationForPageOutput> = (
  data,
  options
) => {
  return authRequest({ url: 'getSysOrganizationForPage', data, ...options })
}

/**删除机构 */
export const deleteSysOrganizationApi: ApiFunc<DeleteSysOrganizationInput, undefined> = (data, options) => {
  return authRequest({ url: 'deleteSysOrganization', data, ...options })
}

/**获取机构列表 */
export const getRightSysOrganizationForShowApi: ApiFunc<undefined, GetRightSysOrganizationForShowOutput> = (
  options
) => {
  return authRequest({ url: 'getRightSysOrganizationForShow', ...options })
}

/**添加机构列表 */
export const addSysOrganizationApi: ApiFunc<AddSysOrganizationInput, undefined> = (data, options) => {
  return authRequest({ url: 'addSysOrganization', data, ...options })
}

/**获取机构详情 */
export const getSysOrganizationForUpdateApi: ApiFunc<
  GetSysOrganizationForUpdateInput,
  GetSysOrganizationForUpdateOutput
> = (data, options) => {
  return authRequest({ url: 'getSysOrganizationForUpdate', data, ...options })
}

/**添加机构列表 */
export const updateSysOrganizationApi: ApiFunc<UpdateSysOrganizationInput, undefined> = (data, options) => {
  return authRequest({ url: 'updateSysOrganization', data, ...options })
}
