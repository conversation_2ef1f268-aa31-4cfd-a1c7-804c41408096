import {
  SelectableWard,
  SelectableBed,
  ReqBaseInfoDTO,
  PatientAccountsBaseInfo,
  ReqExpenseInfoDTO,
  ResExpenseInfoDTO,
  ReqExpenseInfoOfClassificationDTO,
  ResExpenseInfoOfAllOrClassificationDTO,
  ReqDepositPaymentInfoDTO,
  ResDepositPaymentInfoDTO
} from '@/api/dto/discharge-settlement/patient-accounts.dto.ts'
import { request } from '@/utils/axios-utils'
import type { ApiFunc } from 'axios'
import type {
  PatientBillList,
  ReqSettleAnAccountByPhaseOfCostStructureDTO,
  ReqSettleAnAccountByAllOfCostStructureDTO,
  ReqSettleAnAccountByDetailsOfCostStructureDTO,
  ResSettleAnAccountOfCostStructureDTO,
  ReqCheckTheDepositAmountDTO,
  ResCheckTheDepositAmountDTO,
  ReqClearingAmountOfInvoiceNumberDTO,
  ResClearingAmountOfInvoiceNumberDTO
} from '@/api/dto/discharge-settlement/bill-clearing.dto'

/**
 * 出院结算 模块
 */

/** 获取可选病区列表 */
export const requestSettlementDepartmentList: ApiFunc<void, SelectableWard[]> = () => {
  return request({
    url: '/foying/web/DischargeSettlement/getDepartmentList',
    method: 'post'
  })
}

/** 根据病区获取床位信息 */
export const requestSettlementSelectBedNo: ApiFunc<
  { deptcode: string },
  SelectableBed[]
> = (data) => {
  return request({
    url: '/foying/web/DischargeSettlement/selectBedNo',
    method: 'post',
    data
  })
}

/** 获取病人基本信息 */
export const requestPatientBaseInfo: ApiFunc<
  ReqBaseInfoDTO,
  PatientAccountsBaseInfo
> = (data) => {
  return request({
    url: '/foying/web/DischargeSettlement/getBaseInfo',
    method: 'post',
    data
  })
}

/** ------ 出院病人账务 子模块 ------ */
/** 获取病人费用信息 */
export const requestPatientExpenseInfo: ApiFunc<
  ReqExpenseInfoDTO,
  ResExpenseInfoDTO
> = (data) => {
  return request({
    url: '/foying/web/DischargeSettlement/getExpenseInfo',
    method: 'post',
    data
  })
}

/** 获取病人费用信息-全部明细 */
export const requestPatientExpenseInfoOfAll: ApiFunc<
  ReqExpenseInfoDTO,
  { resExpenseInfoList: ResExpenseInfoOfAllOrClassificationDTO[], totalAmount: number }
> = (data) => {
  return request({
    url: '/foying/web/DischargeSettlement/getExpenseInfoOfAll',
    method: 'post',
    data
  })
}

/** 获取病人费用信息-分类明细 */
export const requestPatientExpenseInfoOfClassification: ApiFunc<
  ReqExpenseInfoOfClassificationDTO,
  ResExpenseInfoOfAllOrClassificationDTO[]
> = (data) => {
  return request({
    url: '/foying/web/DischargeSettlement/getExpenseInfoOfClassification',
    method: 'post',
    data
  })
}

/** 获取押金缴纳信息 */
export const requestDepositPaymentInfo: ApiFunc<
  ReqDepositPaymentInfoDTO,
  ResDepositPaymentInfoDTO[]
> = (data) => {
  return request({
    url: '/foying/web/DischargeSettlement/getDepositPaymentInfo',
    method: 'post',
    data
  })
}


/** ------ 出院清账结算 子模块 ------ */
/** 获取病人期账信息 */
export const requestPatientBillList: ApiFunc<
  { inpno: string },
  PatientBillList
> = (data) => {
  return request({
    url: '/foying/web/DischargeSettlement/getAccountsInfo',
    method: 'post',
    data
  })
}

/** 清账结算-按期清账-费用结构 */
export const requestSettleAnAccountByPhaseOfCostStructure: ApiFunc<
  ReqSettleAnAccountByPhaseOfCostStructureDTO,
  ResSettleAnAccountOfCostStructureDTO
> = (data) => {
  return request({
    url: '/foying/web/DischargeSettlement/settleAnAccountByPhaseOfCostStructure',
    method: 'post',
    data
  })
}

/** 清账结算-全部清账-费用结构 */
export const requestSettleAnAccountByAllOfCostStructure: ApiFunc<
  ReqSettleAnAccountByAllOfCostStructureDTO,
  ResSettleAnAccountOfCostStructureDTO
> = (data) => {
  return request({
    url: '/foying/web/DischargeSettlement/settleAnAccountByAllOfCostStructure',
    method: 'post',
    data
  })
}

/** 清账结算-明细清账-费用结构 */
export const requestSettleAnAccountByDetailsOfCostStructure: ApiFunc<
  ReqSettleAnAccountByDetailsOfCostStructureDTO,
  ResSettleAnAccountOfCostStructureDTO
> = (data) => {
  return request({
    url: '/foying/web/DischargeSettlement/settleAnAccountByDetailsOfCostStructure',
    method: 'post',
    data
  })
}

/** 清账结算-确认选中的押金预收单号的金额 */
export const requestCheckTheDepositAmount: ApiFunc<
  ReqCheckTheDepositAmountDTO,
  ResCheckTheDepositAmountDTO
> = (data) => {
  return request({
    url: '/foying/web/DischargeSettlement/checkTheDepositAmount',
    method: 'post',
    data
  })
}

/** 清账结算-清账金额-发票号 */
export const requestClearingAmountOfInvoiceNumber: ApiFunc<
  ReqClearingAmountOfInvoiceNumberDTO,
  ResClearingAmountOfInvoiceNumberDTO
> = (data) => {
  return request({
    url: '/foying/web/DischargeSettlement/clearingAmountOfInvoiceNumber',
    method: 'post',
    data
  })
}


/** ------ 结算设置 子模块 ------ */
/** 结算设置-获取发票自动填充启用情况 */
export const requestSettlementInvoiceAutoFillInfo: ApiFunc<
  void,
  { settingId: string; enabledMark: boolean }
> = () => {
  return request({
    url: '/foying/web/SettlementSettingsController/getEnabledInfo',
    method: 'post'
  })
}

/** 结算设置-修改发票自动填充启用情况 */
export const requestSettlementInvoiceAutoFillUpdate: ApiFunc<
  { settingId: string; enabledMark: boolean },
  { msg: string }
> = (data) => {
  return request({
    url: '/foying/web/SettlementSettingsController/updateSetting',
    method: 'post',
    data
  })
}

