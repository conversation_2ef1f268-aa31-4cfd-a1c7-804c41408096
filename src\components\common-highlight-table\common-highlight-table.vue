<script setup lang="ts">
import { VNode } from 'vue'
import type { SummaryMethodProps } from './types'

interface CommonHighlightTableProps {
  data: any[]
  // 高亮当前行时，是否将第一列改为手指图标
  showFingerIcon?: boolean
  // 是否禁用高亮
  disabledHighlight?: boolean
  // 是否显示合计
  showSummary?: boolean
  // 合计方法
  getSummaries?: (param: SummaryMethodProps) => (string | VNode)[]
}

defineProps<CommonHighlightTableProps>()

const emit = defineEmits<{
  (e: 'current-change', row: any): void
  (e: 'row-dblclick', row: any): void
  (e: 'selection-change', rows: any[]): void
}>()

const tableRef = ref<InstanceType<typeof ElTable>>()

const handleCurrentChange = (row: any) => {
  emit('current-change', row)
}

const handleRowDblclick = (row: any) => {
  emit('row-dblclick', row)
}

const handleSelectionChange = (rows: any[]) => {
  emit('selection-change', rows)
}

// 清除表格选中
function clearTableSelection() {
  tableRef.value?.setCurrentRow(null)
  tableRef.value?.clearSelection()
}

// 设置表格选中
function setTableSelection(row: any) {
  tableRef.value?.setCurrentRow(row)
  tableRef.value?.toggleRowSelection(row)
  tableRef.value?.scrollTo(row)
}

defineExpose({
  clearTableSelection,
  setTableSelection
})
</script>

<template>
  <div class="common-highlight-table" :class="{ 'show-finger-icon': showFingerIcon }">
    <el-table
      ref="tableRef"
      v-bind="$attrs"
      :data="data"
      border
      :highlight-current-row="!disabledHighlight"
      :show-summary="showSummary"
      :summary-method="getSummaries"
      @current-change="handleCurrentChange"
      @row-dblclick="handleRowDblclick"
      @selection-change="handleSelectionChange"
    >
      <slot />
    </el-table>
  </div>
</template>

<style scoped lang="scss">
.common-highlight-table {
  :deep(.el-table__header th) {
    background-color: #f5f7fa !important;
  }

  :deep(.el-table__header .el-table__cell) {
    text-align: center !important;
  }

  :deep(.el-table tr.current-row > td) {
    background-color: #1890ff !important;
    color: #fff !important;
  }

  :deep(.el-table__body-wrapper td) {
    height: 40px;
  }

  :deep(.el-table__footer tfoot td) {
    background-color: #fee9a6 !important;
    color: #0007db !important;
    font-weight: 700 !important;
    font-size: 16px !important;
    border-right: 0;
  }

  :deep(.el-table__body-wrapper .el-scrollbar__bar.is-horizontal) {
    height: 8px !important;
  }

  :deep(.el-checkbox .el-checkbox__input.is-checked .el-checkbox__inner) {
    background-color: var(--el-color-primary) !important;
    border-color: var(--el-color-primary) !important;
  }
}

.show-finger-icon {
  :deep(.el-table tr.current-row > td:first-child) {
    background-color: white !important;
    font-size: 0 !important;
    background-image: url('@/assets/icons/ic_finger.svg') !important;
    background-size: 20px 18px !important;
    background-repeat: no-repeat !important;
    background-position: center !important;
  }
}
</style>
