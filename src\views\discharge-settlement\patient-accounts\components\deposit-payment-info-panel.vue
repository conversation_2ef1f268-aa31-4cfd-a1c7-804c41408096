<script setup lang="ts">
import type { ResDepositPaymentInfoDTO } from '@/api/dto/discharge-settlement/patient-accounts.dto'

defineProps<{
  depositPaymentInfoList: ResDepositPaymentInfoDTO[]
}>()
</script>

<template>
  <CommonHighlightTable :data="depositPaymentInfoList" :max-height="400" show-finger-icon>
    <el-table-column type="index" label="No." width="70" align="center" />
    <el-table-column prop="prepaydate" label="收款日期" width="200" />
    <el-table-column prop="operater" label="收款员" width="110" />
    <el-table-column prop="amount" label="金额" width="120" align="right" />
    <el-table-column prop="paytype" label="币种" width="110" />
    <el-table-column prop="prepayid" label="预交单号" width="180" />
    <el-table-column prop="printno" label="预收印刷号" width="180" />
    <el-table-column prop="dayShiftOrNightShift" label="班别" width="90" />
    <el-table-column prop="ward" label="病区" width="180" />
    <el-table-column prop="clientname" label="交款人" width="120" />
    <el-table-column prop="notes" label="备注" min-width="300" />
  </CommonHighlightTable>
</template>

<style scoped lang="scss"></style>
