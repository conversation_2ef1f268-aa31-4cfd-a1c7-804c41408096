<script setup lang="ts">
import {
  RentalPaymentInfo,
  RentalPayStatusConfig,
  RentalRefundStatusConfig
} from '@/api/dto/rental-management/rental-order-management.dto.ts'

defineProps({
  paymentInfo: {
    type: Object as PropType<RentalPaymentInfo>,
    required: true,
    default: () => ({})
  }
})

const dialogVisible = defineModel({ default: false })
</script>
<template>
  <CommonFormDialog v-model="dialogVisible" title="补缴支付信息" width="1485px" @confirm="dialogVisible = false">
    <div class="payment-title">
      <span>补缴记录</span>
    </div>
    <BaseTable :data="paymentInfo.supplementaryPaymentRecordList" border width="100%" max-height="300">
      <el-table-column prop="feeType" label="费用类型" width="242" />
      <el-table-column prop="payFee" label="补缴金额（元）" width="242" />
      <el-table-column prop="payStatus" label="支付状态" width="242">
        <template #default="scope">
          {{ RentalPayStatusConfig[scope.row.payStatus]?.label || RentalPayStatusConfig.default.label }}
        </template>
      </el-table-column>
      <el-table-column prop="createTime" label="创建时间" width="242" />
      <el-table-column prop="payTime" label="支付时间" width="242">
        <template #default="scope">
          {{ scope.row.paymentTime || '-' }}
        </template>
      </el-table-column>
      <el-table-column prop="payTradeNo" label="支付流水号">
        <template #default="scope">
          {{ scope.row.payTradeNo || '-' }}
        </template>
      </el-table-column>
    </BaseTable>
    <div class="payment-title" style="margin-top: 30px">
      <span>退款记录</span>
    </div>
    <BaseTable :data="paymentInfo.refundRecordList" border width="100%" max-height="300">
      <el-table-column prop="feeType" label="费用类型" width="242" />
      <el-table-column prop="refund" label="退款金额（元）" width="242" />
      <el-table-column prop="refundStatus" label="退款状态" width="242">
        <template #default="scope">
          {{ RentalRefundStatusConfig[scope.row.refundStatus]?.label || RentalRefundStatusConfig.default.label }}
        </template>
      </el-table-column>
      <el-table-column prop="createTime" label="创建时间" width="242" />
      <el-table-column prop="successTime" label="退款时间" width="242">
        <template #default="scope">
          {{ scope.row.successTime || '-' }}
        </template>
      </el-table-column>
      <el-table-column prop="outRefundNo" label="平台退款订单号">
        <template #default="scope">
          {{ scope.row.outRefundNo || '-' }}
        </template>
      </el-table-column>
    </BaseTable>
  </CommonFormDialog>
</template>

<style scoped lang="scss">
.payment-title {
  font-weight: 700;
  font-size: 14px;
  color: #303133;
  margin-bottom: 15px;
}
</style>
