<script lang="ts" setup generic="T">
import { type TableInstance } from 'element-plus'

type PropsType = {
  tableHeaderHeight?: number | string
  columnHeight?: number | string
  emptyText?: string
  tooltipOptions?: object
  // 是否显示空数据图标
  showEmptyIcon?: boolean
}

const props = withDefaults(defineProps<PropsType>(), {
  showEmptyIcon: true
})
const tableRef = ref<TableInstance>()

/**表头行高 */
const tableHeaderHeightValue = computed(() => `${props.tableHeaderHeight || 48}px`)
/**行高 */
const columnHeightValue = computed(() => `${props.columnHeight || 58}px`)

defineExpose({
  tableRef
})
</script>

<template>
  <el-table ref="tableRef" class="base_table" :tooltip-options="tooltipOptions">
    <slot></slot>

    <template #empty>
      <div class="table_empty-container">
        <img v-if="showEmptyIcon" src="@/assets/image/global/table-empty.png" class="table_empty-icon" />
        <p class="table_empty-text">{{ emptyText || $attrs.emptyText || '暂无数据' }}</p>
      </div>
    </template>
  </el-table>
</template>

<style lang="scss" scoped>
.base_table {
  :deep(.el-table__header) {
    th {
      height: v-bind(tableHeaderHeightValue);
    }
  }

  :deep(.el-table__body-wrapper) {
    td {
      height: v-bind(columnHeightValue);
    }
  }

  :deep(.el-table__empty-text) {
    width: 100%;
  }

  .table_empty-container {
    line-height: 1em;

    .table_empty-icon {
      width: 280px;
      height: 160px;
      margin-top: 30px;
    }

    .table_empty-text {
      margin-bottom: 30px;
    }
  }
}
</style>
