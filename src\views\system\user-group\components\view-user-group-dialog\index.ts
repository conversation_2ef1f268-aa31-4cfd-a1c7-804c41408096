import ViewUserGroupDialog, { type DialogData } from './view-user-group-dialog.vue'

export type ViewUserGroupDialogType = InstanceType<typeof ViewUserGroupDialog>
export type ViewUserGroupDialogProps = ViewUserGroupDialogType['$props']

let overlayComponent: ViewUserGroupDialogType | null = null

function createdOverlay(options?: ViewUserGroupDialogProps) {
  const overlayElement = document.createElement('div')
  document.body.appendChild(overlayElement)

  const OverlayInstance = createApp(ViewUserGroupDialog, options)
  overlayComponent = OverlayInstance.mount(overlayElement) as ViewUserGroupDialogType
}

/**打开用户创建弹窗 */
export const useViewUserGroupDialogHook = () => {
  const open = async (openParams: DialogData, dialogOpts?: ViewUserGroupDialogProps) => {
    if (!overlayComponent) {
      createdOverlay(dialogOpts)
    }
    return overlayComponent!.__open.call(overlayComponent, openParams)
  }

  const close = () => {
    overlayComponent?.__close.call(overlayComponent)
  }

  return { open, close }
}

export default ViewUserGroupDialog
