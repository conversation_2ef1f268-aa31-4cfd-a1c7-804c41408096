<script setup lang="ts">
import type { SettlementItem } from '@/api/dto/discharge-settlement/settlement-table.dto'
import { formatPrice } from '@/utils'

interface Props {
  settlementData: SettlementItem[]
}

defineProps<Props>()

const customFormatAmount = (row: SettlementItem) => {
  return formatPrice(row.totalCost)
}
</script>

<template>
  <CommonHighlightTable :data="settlementData" :max-height="420" show-finger-icon>
    <el-table-column prop="period" label="期数" width="120" align="center" />
    <el-table-column prop="settlementDate" label="结算日期" width="180" />
    <el-table-column prop="totalCost" label="累计费用" width="120" align="right" :formatter="customFormatAmount" />
    <el-table-column
      prop="totalSelfPayAmount"
      label="累计自费"
      width="120"
      align="right"
      :formatter="customFormatAmount"
    />
    <el-table-column
      prop="currentPeriodCost"
      label="本期费用"
      width="120"
      align="right"
      :formatter="customFormatAmount"
    />
    <el-table-column
      prop="currentSelfPayAmount"
      label="本期自费"
      width="120"
      align="right"
      :formatter="customFormatAmount"
    />
    <el-table-column prop="selfPayClearingDate" label="自费清账日" width="180" />
    <el-table-column
      prop="totalCoordinatedFundAmount"
      label="累计公费"
      width="120"
      align="right"
      :formatter="customFormatAmount"
    />
    <el-table-column
      prop="currentCoordinatedFundAmount"
      label="本期公费"
      width="120"
      align="right"
      :formatter="customFormatAmount"
    />
    <el-table-column prop="coordinatedFundClearingDate" label="公费清账日" width="180" />
    <!-- 最后一列只是使表格填满页面宽度 -->
    <el-table-column prop="" label="" />
  </CommonHighlightTable>
</template>
