// * {
//   user-select: none;
// }

HTML,
body,
div,
h1,
h2,
h3,
h4,
h5,
h6,
ul,
ol,
dl,
li,
dt,
dd,
p,
blockquote,
pre,
form,
fieldset,
table,
th,
td {
  box-sizing: border-box;
  padding: 0px;
  margin: 0px;
  border: none;
  letter-spacing: 0.1em;
}
html,
body {
  position: relative;
  width: 100vw;
  height: 100vh;
  overflow: hidden;
}
a {
  text-decoration: none;
}
a:link {
  color: var(el-color-wirte);
}
a:visited {
  color: var(el-color-wirte);
}
a:hover {
  color: var(el-color-wirte);
}
a:active {
  color: var(el-color-wirte);
}
input::-ms-clear {
  display: none;
}
input::-ms-reveal {
  display: none;
}
input {
  margin: 0;
  outline: none;
  padding: 0;
}
input::-webkit-input-placeholder {
  color: #ccc;
}
input::-ms-input-placeholder {
  color: #ccc;
}
input::-moz-placeholder {
  color: #ccc;
}
input[type='submit'],
input[type='button'] {
  cursor: pointer;
}
button[disabled],
input[disabled] {
  cursor: default;
}
img {
  border: none;
}
ul,
ol,
li {
  list-style-type: none;
}
/*公共方法*/
.clear {
  clear: both;
}
.clearleft {
  clear: left;
}
.clearright {
  clear: right;
}
.floatleft {
  float: left;
}
.floatright {
  float: right;
}
.cursor {
  cursor: pointer;
}
