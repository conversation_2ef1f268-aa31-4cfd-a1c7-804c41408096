/**
 * 陪护人租赁管理 - 陪护类型管理
 */

/**
 * 陪护类型管理 - 分页获取列表 - 请求参数
 */
export interface ReqGetInpatientCaregiverTypeByPage {
  /** 类型名称 */
  serverName?: string
  /** 状态 true启用 false禁用 */
  status?: boolean
  /** 当前页 */
  page: number
  /** 每页的记录数 */
  rows: number
}

/**
 * 陪护类型管理 - 分页获取列表 - 响应项
 */
export interface ResInpatientCaregiverTypeItem {
  /** ID */
  inpatientCaregiverTypeId: string
  /** 类型名称 */
  serverName: string
  /** 单价 */
  price: number
  /** 状态 true启用 false禁用 */
  status: boolean
  /** 备注 */
  remark?: string
  /** 创建时间 */
  createTime: string
  /** 更新时间 */
  modifyTime: string
}

/**
 * 陪护类型管理 - 分页获取列表 - 响应
 */
export interface ResGetInpatientCaregiverTypeByPage {
  /** 实体列表 */
  data: ResInpatientCaregiverTypeItem[]
  /** 当前页 */
  pageIndex: number
  /** 每页记录数 */
  pageSize: number
  /** 总记录数 */
  recordCount: number
  /** 总页数 */
  pageCount: number
}

// === 陪护类型管理 - 新增 ===
/**
 * 陪护类型管理 - 新增 - 请求参数
 */
export interface ReqAddInpatientCaregiverType {
  /** 类型名称 */
  serverName: string
  /** 单价 */
  price: number
  /** 状态 true启用 false禁用 */
  status: boolean
  /** 备注 */
  remark?: string
}

// === 陪护类型管理 - 修改 ===
/**
 * 陪护类型管理 - 修改 - 请求参数
 */
export interface ReqUpdateInpatientCaregiverType {
  /** ID */
  inpatientCaregiverTypeId: string
  /** 类型名称 */
  serverName: string
  /** 单价 */
  price: number
  /** 状态 true启用 false禁用 */
  status: boolean
  /** 备注 */
  remark?: string
}

// === 陪护类型管理 - 操作ID请求 ===
/**
 * 陪护类型管理 - 启用/禁用/删除 - 请求参数
 */
export interface ReqInpatientCaregiverTypeId {
  /** ID */
  inpatientCaregiverTypeId: string
}

// === 陪护类型管理 - 公共响应 ===
/**
 * 陪护类型管理 - 公共响应信息
 */
export interface ResInpatientCaregiverTypeMsg {
  /** 消息 */
  msg: string
}
