<script setup lang="ts">
import { FormInstance, FormRules } from 'element-plus'
import type {
  ResCheckTheDepositAmountDTO,
  ResClearingAmountOfInvoiceNumberDTO
} from '@/api/dto/discharge-settlement/bill-clearing.dto'

/**
 * 清账金额 弹窗
 */

// 清账类型，是否补收 true补收false退款
const clearTypeOptions = [
  {
    label: '补收',
    value: 'true'
  },
  {
    label: '退款',
    value: 'false'
  }
]

// 币种
const currencyOptions = [
  {
    label: '人民币',
    value: '人民币'
  },
  {
    label: '美元',
    value: '美元'
  }
]

interface RuleForm {
  // 金额
  amount: number | undefined
  // 币种
  currency: string | undefined
  // 病区
  ward: string | undefined
  // 发票号
  invoiceno: string | undefined
  // 清账类型，是否补收 true补收false退款
  clearType: string | undefined
}

// 是否允许选择病区
const isAllowSelectWard = ref(false)

const props = defineProps<{
  // 预收单数据
  preReceiptData: ResCheckTheDepositAmountDTO
  // 发票号数据，有数据时候表示自动填充，没有数据时候表示手动输入
  invoiceNumberData: ResClearingAmountOfInvoiceNumberDTO
  // 确认回调
  confirmCallback: (period: number) => Promise<void>
}>()

const inputFormRef = ref<FormInstance>()

const inputForm = reactive<RuleForm>({
  amount: undefined,
  currency: undefined,
  ward: undefined,
  invoiceno: undefined,
  clearType: undefined
})

const dialogVisible = defineModel({ default: false })

const inputFormRules = reactive<FormRules<RuleForm>>({
  amount: [{ required: true, message: '请输入清账金额', trigger: 'blur' }],
  currency: [{ required: true, message: '请选择币种', trigger: 'blur' }],
  ward: [{ required: true, message: '请选择病区', trigger: 'blur' }],
  invoiceno: [{ required: true, message: '请输入发票号', trigger: 'blur' }],
  clearType: [{ required: true, message: '请选择清账类型', trigger: 'blur' }]
})

// 弹窗关闭时，重置表单
watch(
  dialogVisible,
  (val) => {
    if (val) {
      // 打开弹窗时，同步到 inputForm
      inputForm.invoiceno = props.invoiceNumberData.invoiceno
      inputForm.currency = currencyOptions[0].value
      inputForm.ward = props.preReceiptData.deptList[0]
      inputForm.clearType = props.preReceiptData.isSupplement ? clearTypeOptions[0].value : clearTypeOptions[1].value
    } else {
      inputFormRef.value?.resetFields()
    }
  },
  {
    immediate: true
  }
)

// 弹窗 - 点击确认
async function handleConfirm(done: (flag?: boolean) => void, formEl: FormInstance | undefined) {
  if (!formEl) return
  await formEl.validate(async (valid, fields) => {
    if (valid) {
      try {
        // 提交
        await props.confirmCallback(JSON.parse(JSON.stringify(inputForm)))
        done(false)
        setTimeout(() => {
          formEl.resetFields()
        })
      } catch (e) {
        done(true)
      }
    } else {
      console.log('提交 弹窗 表单 失败 表单校验不通过：', fields)
      done(true)
    }
  })
}
</script>

<template>
  <CommonFormDialog
    v-model="dialogVisible"
    class="clear-amount-dialog"
    :close-on-click-modal="false"
    title="清账金额"
    width="750px"
    show-loading
    @confirm="handleConfirm($event, inputFormRef)"
  >
    <el-form ref="inputFormRef" size="large" :model="inputForm" :rules="inputFormRules" hide-required-asterisk>
      <div class="form-item">
        <div class="form-item-content">
          <div class="form-item-label--max">发票印刷号：</div>
          <el-form-item prop="invoiceno">
            <el-input
              v-model="inputForm.invoiceno"
              class="form-item-value--large"
              clearable
              placeholder="请输入"
              :disabled="!!invoiceNumberData.invoiceno"
            />
          </el-form-item>
          <div class="had-invoice-tips" v-if="!!invoiceNumberData.invoiceno">已使用电子发票</div>
        </div>
      </div>

      <el-divider />

      <div class="form-item form-item--bold">
        <div class="form-item-content">
          <div class="form-item-label">费用总金额：</div>
          <el-input v-model="preReceiptData.totalCostAmount" class="form-item-value" clearable disabled />
        </div>
        <div class="form-item-content">
          <div class="form-item-label">冲销预交人民币：</div>
          <el-input v-model="preReceiptData.writeOffPrepaidRMB" class="form-item-value" clearable disabled />

          <div class="form-item-label">补收人民币：</div>
          <el-input
            v-model="preReceiptData.supplementRMB"
            class="form-item-value form-item-value--red"
            clearable
            disabled
          />
        </div>

        <div class="form-item-content">
          <div class="form-item-label">精度费支付：</div>
          <!-- TODO 待补充 -->
          <el-input class="form-item-value" clearable disabled />

          <div class="form-item-label">医保结算日期：</div>
          <el-input class="form-item-value form-item-value--left" clearable disabled />
        </div>

        <div class="form-item-content">
          <div class="form-item-label">医保基金支付：</div>
          <el-input class="form-item-value" clearable disabled />
        </div>
      </div>

      <el-divider />

      <div class="form-item-invoice">
        <div>发票日期：</div>
        <el-input
          v-model="preReceiptData.invoiceStartDate"
          class="form-item-value--small form-item-value--left"
          clearable
          disabled
        />
        <div style="margin: 0 10px">至</div>
        <el-input
          v-model="preReceiptData.invoiceEndDate"
          class="form-item-value--small form-item-value--left"
          clearable
          disabled
        />

        <el-checkbox style="margin-left: 20px; margin-right: 12px" v-model="isAllowSelectWard">选择病区</el-checkbox>
        <el-form-item prop="ward">
          <el-select v-model="inputForm.ward" class="form-item-value--ward" clearable :disabled="!isAllowSelectWard">
            <el-option v-for="item in preReceiptData.deptList" :key="item" :label="item" :value="item" />
          </el-select>
        </el-form-item>
      </div>

      <div class="form-item form-item--bold form-item--horizontal">
        <div class="clear-type">
          <el-form-item prop="clearType">
            <el-select v-model="inputForm.clearType" class="form-item-value--clear-type" clearable>
              <el-option v-for="item in clearTypeOptions" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </div>

        <div class="clear-panel">
          <div class="clear-panel-item">
            <div class="clear-panel-item-label">币种：</div>
            <el-form-item prop="currency">
              <el-select v-model="inputForm.currency" style="width: 200px" clearable>
                <el-option v-for="item in currencyOptions" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
            </el-form-item>

            <div class="clear-panel-item-label">金额：</div>
            <el-form-item prop="amount">
              <el-input v-model="inputForm.amount" style="width: 260px" placeholder="请输入" />
            </el-form-item>
          </div>
        </div>
      </div>
    </el-form>
  </CommonFormDialog>
</template>

<style scoped lang="scss">
.clear-amount-dialog {
  :deep(.el-dialog .el-dialog__body) {
    padding: 0 !important;
  }

  :deep(.el-checkbox .el-checkbox__input.is-checked .el-checkbox__inner::after) {
    transform: rotate(45deg) scale(1) translate(0, 0);
  }

  :deep(.el-divider) {
    margin: 0 !important;
  }

  :deep(.el-form-item--large) {
    margin-bottom: 0 !important;
  }

  :deep(.el-form-item__content) {
    margin-bottom: 0 !important;
    min-width: 0 !important;
  }

  .form-item {
    padding: 20px 16px;
    display: flex;
    flex-direction: column;
    gap: 10px;
  }

  .form-item--horizontal {
    flex-direction: row;
    align-items: center;
  }

  .form-item-invoice {
    display: flex;
    align-items: center;
    padding: 20px 20px 0 42px;
  }

  :deep(.el-input.is-disabled .el-input__wrapper) {
    background-color: #fafafa !important;
  }

  :deep(.el-input__inner) {
    color: #303133 !important;
    text-align: right !important;
    height: 32px !important;
    font-size: 16px !important;
    -webkit-text-fill-color: #303133 !important;
  }

  .form-item-value--large :deep(.el-input__inner) {
    font-size: 30px !important;
    text-align: left !important;
  }

  .form-item--bold :deep(.el-input__inner) {
    font-weight: 700 !important;
  }

  .form-item-value--red :deep(.el-input__inner) {
    color: #eb0000 !important;
    -webkit-text-fill-color: #eb0000 !important;
  }

  .form-item-value--left :deep(.el-input__inner) {
    text-align: left !important;
  }

  .form-item-value--bg-warning :deep(.el-input__wrapper) {
    background-color: #fee9a6 !important;
  }

  :deep(.el-select__wrapper) {
    height: 34px !important;
    min-height: 34px !important;
  }
}

.form-item-content {
  display: flex;
  align-items: center;
  color: #303133 !important;

  & .form-item-label {
    width: 144px;
    text-align: right;

    &--max {
      font-size: 24px;
    }
  }
}

.form-item-value {
  width: 140px;

  &--large {
    font-size: 30px;
    width: 412px;
    height: 60px;
  }

  &--medium {
    width: 130px;
  }

  &--small {
    width: 120px;
  }

  &--ward {
    width: 220px;
  }

  &--clear-type {
    width: 100px;
  }
}

.clear-panel {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 10px;
  padding: 16px;
  background-color: #f5f7fa;

  &-item {
    display: flex;
    align-items: center;

    .clear-panel-item-label {
      margin-left: 20px;

      &:first-child {
        margin-left: 0;
      }
    }
  }
}

.had-invoice-tips {
  font-size: 14px;
  color: #909399;
  margin-left: 38px;
}
</style>
