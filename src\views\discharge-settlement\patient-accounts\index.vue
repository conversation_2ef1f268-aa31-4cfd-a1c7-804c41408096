<script setup lang="ts">
import { ref } from 'vue'
import type { PatientAccountsBaseInfo } from '@/api/dto/discharge-settlement/patient-accounts.dto'
import PatientAccountsBaseInfoPanel from '../components/patient-accounts-base-info-panel.vue'
import PatientAccountsDetailPanel from './components/patient-accounts-detail.vue'
import InpatientInputDialog from '../components/inpatient-input-dialog.vue'
import { useMountDialog } from '@/hooks/useMountDialog.ts'
import PatientDataDialog from '../components/patient-data-dialog.vue'
import { requestPatientBaseInfo } from '@/api/discharge-settlement.api'

const { open: openInpatientInputDialog } = useMountDialog(InpatientInputDialog)
const { open: openPatientDataDialog } = useMountDialog(PatientDataDialog)

/**
 * 出院结算办理 - 出院病人账单
 */

const patientAccountsBaseInfo = ref<PatientAccountsBaseInfo>({} as PatientAccountsBaseInfo)

onMounted(() => {
  handleInputHospitalizationNo()
})

// 输入住院号
function handleInputHospitalizationNo() {
  openInpatientInputDialog({
    confirmCallback: async (data: any) => {
      console.log('输入住院号信息：', data)
      patientAccountsBaseInfo.value = {} as PatientAccountsBaseInfo
      await handleGetPatientBaseInfo(data.hospitalizationNo)
    }
  })
}

// 获取病人基本信息
async function handleGetPatientBaseInfo(patno: string) {
  const res = await requestPatientBaseInfo({
    patno
  })
  patientAccountsBaseInfo.value = res
  console.log('病人基本信息', patientAccountsBaseInfo.value)
}

// 查看病人资料
function handleViewPatientInfo() {
  console.log('查看病人资料')
  openPatientDataDialog({
    patientInfo: patientAccountsBaseInfo.value
  })
}

// 切换病人
function handleSwitchPatient() {
  console.log('切换病人')
  handleInputHospitalizationNo()
}
</script>

<template>
  <div class="page-container">
    <PatientAccountsBaseInfoPanel
      :base-info="patientAccountsBaseInfo"
      @view-patient-info="handleViewPatientInfo"
      @switch-patient="handleSwitchPatient"
    />
    <PatientAccountsDetailPanel :inpno="patientAccountsBaseInfo.inpno" />
  </div>
</template>

<style scoped lang="scss">
.page-container {
  padding: 20px;
}
</style>
