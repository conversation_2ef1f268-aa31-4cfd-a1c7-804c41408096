<script setup lang="ts">
import type { ResDepositPaymentInfoDTO } from '@/api/dto/discharge-settlement/patient-accounts.dto'
import { formatPrice } from '@/utils'
import CommonHighlightTable from '@/components/common-highlight-table/common-highlight-table.vue'

/**
 * 欲冲销的预收单列表弹窗
 */

const props = defineProps<{
  // 预收单列表
  depositPaymentInfoList: ResDepositPaymentInfoDTO[]
  /* 点击确认回调 */
  confirmCallback: (data: ResDepositPaymentInfoDTO[]) => Promise<void>
}>()

const dialogVisible = defineModel({ default: false })

watch(dialogVisible, (val) => {
  if (val) {
    reset()
  }
})

// 选择的预收单
const selectedPreReceiptBill = ref<ResDepositPaymentInfoDTO[]>([])

const tableRef = ref<InstanceType<typeof CommonHighlightTable>>()

// 弹窗 - 点击确认
async function handleConfirm(done: (flag?: boolean) => void) {
  if (selectedPreReceiptBill.value.length === 0) return
  try {
    // 提交
    await props.confirmCallback(JSON.parse(JSON.stringify(selectedPreReceiptBill.value)))
    console.warn('预收单选择，点击确认，提交数据', selectedPreReceiptBill.value)
    done(false)
  } catch (e) {
    console.warn('预收单选择，点击确认，提交数据失败', e)
    done(true)
  }
}

// 合计，使用已选择预收单来计算
const getSummaries = () => {
  if (selectedPreReceiptBill.value.length === 0) return ['', '请勾选以上预收单']

  const sums: (string | VNode)[] = []

  // 项目合计多少项
  const count = selectedPreReceiptBill.value.length

  // 项目合计金额
  const totalAmount = formatPrice(selectedPreReceiptBill.value.reduce((acc, item) => acc + item.amount, 0))

  sums[0] = ''
  sums[1] = `${count}张`
  sums[2] = '合计：'
  sums[3] = `${totalAmount}`
  return sums
}

// 多选变化
function handleSelectionChange(rows: ResDepositPaymentInfoDTO[]) {
  selectedPreReceiptBill.value = rows
}

// 重置
function reset() {
  console.log('重置预收单选择弹窗')
  selectedPreReceiptBill.value = []
  tableRef.value?.clearTableSelection()
}

defineExpose({
  reset
})
</script>

<template>
  <CommonFormDialog
    v-model="dialogVisible"
    :close-on-click-modal="false"
    title="欲冲销的预收单（可多选）"
    width="1200px"
    show-loading
    :disabled-confirm="selectedPreReceiptBill.length === 0"
    @confirm="handleConfirm"
  >
    <CommonHighlightTable
      ref="tableRef"
      :data="depositPaymentInfoList"
      :max-height="518"
      :show-finger-icon="false"
      width="1168px"
      show-summary
      disabled-highlight
      row-key="prepayid"
      :get-summaries="getSummaries"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="48" />
      <el-table-column prop="prepaydate" label="收费日期" width="200" />
      <el-table-column prop="operater" label="收费员号" width="140" />
      <el-table-column prop="amount" label="金额" min-width="140" align="right" />
      <el-table-column prop="paytype" label="预收币种" min-width="140" />
      <el-table-column prop="prepayid" label="预收单号" min-width="200" />
      <el-table-column prop="printno" label="印刷号" min-width="200" />
      <el-table-column prop="clientname" label="患者姓名" min-width="140" />
      <el-table-column prop="dayShiftOrNightShift" label="班别" min-width="90" />
      <el-table-column prop="ward" label="病区" min-width="180" />
      <el-table-column prop="notes" label="备注" min-width="300" />
    </CommonHighlightTable>
  </CommonFormDialog>
</template>

<style scoped lang="scss">
:deep(.el-table__footer) {
  .el-table__cell {
    text-align: end;
  }
}
</style>
