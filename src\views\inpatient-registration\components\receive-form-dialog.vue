<script setup lang="ts">
import { useFormHook } from '@/hooks/useForm.ts'
import { parseTime } from '@/utils/index.ts'
import { FormRules } from 'element-plus'

const props = defineProps<{
  patientName: string
  hosNumber: string
  date?: string
  finished?: (selectedDate: string) => void
}>()

const visible = defineModel({ default: false })

const formData = reactive({
  receiveTime: ''
})
const { formRef, resetForm } = useFormHook()

const rules = reactive<FormRules<any>>({
  receiveTime: [{ required: true, message: '请选择入院时间', trigger: 'change' }]
})

async function handleConfirm(done: (flag?: boolean) => void) {
  console.log(`选择的日期：${formData.receiveTime}`)
  await formRef.value!.validate(async (valid) => {
    if (valid) {
      try {
        await props.finished?.(formData.receiveTime)
        resetForm()
        done(false)
      } catch (error) {
        // 结束按钮 loading，但是不关闭弹窗
        done(true)
      }
    } else {
      done(true)
    }
  })
}

watch(
  visible,
  (v) => {
    if (v) {
      // 医院要求默认当前时间
      formData.receiveTime = props.date || parseTime()
    }
  },
  { immediate: true }
)
</script>

<template>
  <CommonFormDialog v-model="visible" title="接受处理" width="600px" show-loading @confirm="handleConfirm">
    <div class="patient-info">住院人姓名：{{ patientName }} 丨 预入院流水号：{{ hosNumber ?? '-' }}</div>
    <el-form ref="formRef" class="mt-20" :rules="rules" :model="formData" size="large">
      <el-form-item label="入院时间：" prop="receiveTime">
        <el-date-picker
          v-model="formData.receiveTime"
          type="datetime"
          format="YYYY-MM-DD HH:mm:ss"
          value-format="YYYY-MM-DD HH:mm:ss"
          placeholder="请选择"
          style="width: 100%"
        />
      </el-form-item>
    </el-form>
  </CommonFormDialog>
</template>

<style scoped lang="scss">
.patient-info {
  height: 40px;
  border-radius: 4px;
  background: #ecf5ff;
  border: 1px solid #d9ecff;
  color: #1890ef;
  line-height: 40px;
  padding: 0 16px;
}

.mt-20 {
  margin-top: 20px;
}
</style>
