<script setup lang="ts">
/**
 * 陪护人租赁管理 - 陪护人管理
 */
import {
  ReqGetInpatientCaregiverPersonByPage,
  ResInpatientCaregiverPersonItem
} from '@/api/dto/rental-management/caregiver-management.dto.ts'
import { useMountDialog } from '@/hooks/useMountDialog.ts'
import CaregiverEditDialog from '@/views/rental-management/caregiver-management/components/caregiver-edit-dialog.vue'
import { toRaw } from 'vue'
import { maskIdCard } from '@/utils/index.ts'

enum ItemActionType {
  /* 编辑 */
  EDIT = 'edit',
  /* 启用 */
  ENABLE = 'enable',
  /* 禁用 */
  DISABLE = 'disable',
  /* 删除 */
  DELETE = 'delete'
}

const emits = defineEmits<{
  (e: 'search', data: { currentPage: number; pageSize: number; searchData: ReqGetInpatientCaregiverPersonByPage }): void
  (e: 'add', data: ResInpatientCaregiverPersonItem, resolve: () => void, reject: () => void): void
  (e: 'edit', data: ResInpatientCaregiverPersonItem, resolve: () => void, reject: () => void): void
  (e: 'changeStatus', data: { inpatientCaregiverPersonId: string; status: boolean }): void
  (e: 'delete', data: ResInpatientCaregiverPersonItem): void
}>()

defineProps<{
  listData: ResInpatientCaregiverPersonItem[]
  total: number
}>()

const { open: openEditDialog } = useMountDialog(CaregiverEditDialog)

// 搜索表单
const searchFormData = reactive<ReqGetInpatientCaregiverPersonByPage>({} as ReqGetInpatientCaregiverPersonByPage)

// 分页
const paginationData = reactive({
  currentPage: 1,
  pageSize: 10
})

onMounted(() => {
  // 默认加载页面时搜索
  emitSearch()
})

// 搜索
function handleSearch() {
  paginationData.currentPage = 1
  emitSearch()
}

// 重置
function handleReset() {
  searchFormData.name = ''
  searchFormData.isCaregiver = undefined
  searchFormData.status = undefined
  emitSearch()
}

// 新增
function handleAdd() {
  openEditDialog({
    detailInfo: {} as ResInpatientCaregiverPersonItem,
    title: '新增',
    confirmCallback: async (data: ResInpatientCaregiverPersonItem) => {
      return new Promise<void>((resolve, reject) => {
        // 父组件可能是异步操作，把 resolve 和 reject 传给子组件，子组件可以调用
        emits('add', data, resolve, reject)
      })
    }
  })
}

function emitSearch() {
  emits('search', {
    currentPage: paginationData.currentPage,
    pageSize: paginationData.pageSize,
    searchData: toRaw(searchFormData)
  })
}

function handleItemAction(itemAction: ItemActionType, itemData: ResInpatientCaregiverPersonItem) {
  switch (itemAction) {
    case ItemActionType.EDIT:
      openEditDialog({
        detailInfo: itemData,
        title: '编辑',
        confirmCallback: async (data: ResInpatientCaregiverPersonItem) => {
          return new Promise<void>((resolve, reject) => {
            // 父组件可能是异步操作，把 resolve 和 reject 传给子组件，子组件可以调用
            emits('edit', data, resolve, reject)
          })
        }
      })
      break
    case ItemActionType.ENABLE:
      emits('changeStatus', {
        inpatientCaregiverPersonId: itemData.inpatientCaregiverPersonId,
        status: true
      })
      break
    case ItemActionType.DISABLE:
      emits('changeStatus', {
        inpatientCaregiverPersonId: itemData.inpatientCaregiverPersonId,
        status: false
      })
      break
    case ItemActionType.DELETE:
      ElMessageBox.confirm('确定要删除此陪护人信息吗？', '删除确认', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          emits('delete', itemData)
        })
        .catch(() => {
          // 取消删除
        })
      break
    default:
      break
  }
}

function handleSizeChange() {
  // 切换每页条数时把当前页重置回 1
  paginationData.currentPage = 1
  emitSearch()
}

function handleCurrentChange() {
  emitSearch()
}

defineExpose({
  handleSearch
})
</script>

<template>
  <div class="page-container">
    <el-form inline :model="searchFormData">
      <el-form-item label="陪护人名称：">
        <el-input v-model="searchFormData.name" class="input-container" placeholder="请输入" size="large" clearable />
      </el-form-item>
      <el-form-item label="是否陪护中：">
        <el-select
          v-model="searchFormData.isCaregiver"
          class="input-container"
          placeholder="请选择"
          size="large"
          clearable
        >
          <el-option label="是" :value="true" />
          <el-option label="否" :value="false" />
        </el-select>
      </el-form-item>
      <el-form-item label="状态：">
        <el-select v-model="searchFormData.status" class="input-container" placeholder="请选择" size="large" clearable>
          <el-option label="启用" :value="true" />
          <el-option label="禁用" :value="false" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button v-auth.disabled="'InpatientCaregiverPersonByPage'" size="large" type="primary" @click="handleSearch"
          >搜索
        </el-button>
        <el-button v-auth.disabled="'InpatientCaregiverPersonByPage'" size="large" @click="handleReset"
          >重置
        </el-button>
        <el-button v-auth.disabled="'InpatientCaregiverPersonAdd'" size="large" type="success" @click="handleAdd"
          >新增
        </el-button>
      </el-form-item>
    </el-form>

    <BaseTable class="table-container" :data="listData" border width="100%" height="608">
      <el-table-column prop="name" label="陪护人名称" width="160" />
      <el-table-column label="性别" width="120">
        <template #default="scope">
          {{ scope.row.gender === '1' ? '男' : scope.row.gender === '2' ? '女' : '-' }}
        </template>
      </el-table-column>
      <el-table-column prop="age" label="年龄" width="120" />
      <el-table-column prop="idCard" label="身份证号" width="220">
        <template #default="scope">
          {{ maskIdCard(scope.row.idCard) }}
        </template>
      </el-table-column>
      <el-table-column prop="phone" label="联系电话" width="180" />
      <el-table-column label="是否陪护中" width="120">
        <template #default="scope">
          {{ scope.row.isCaregiver ? '是' : '否' }}
        </template>
      </el-table-column>
      <el-table-column label="状态" width="120">
        <template #default="scope">
          <el-tag :type="scope.row.status ? 'success' : 'warning'">
            {{ scope.row.status ? '启用' : '禁用' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="remark" label="备注" width="180">
        <template #default="scope"> {{ scope.row.remark ?? '-' }}</template>
      </el-table-column>
      <el-table-column prop="modifyTime" label="更新时间" width="180" />

      <el-table-column label="操作" fixed="right" min-width="220">
        <template #default="scope">
          <el-button
            v-auth.disabled="'InpatientCaregiverPersonUpdate'"
            size="small"
            @click="handleItemAction(ItemActionType.EDIT, scope.row)"
            >编辑</el-button
          >
          <el-button
            v-if="!scope.row.status"
            v-auth.disabled="'InpatientCaregiverPersonEnabled'"
            size="small"
            type="success"
            @click="handleItemAction(ItemActionType.ENABLE, scope.row)"
            >启用
          </el-button>
          <el-button
            v-if="scope.row.status"
            v-auth.disabled="'InpatientCaregiverPersonDisabled'"
            size="small"
            type="warning"
            @click="handleItemAction(ItemActionType.DISABLE, scope.row)"
            >禁用
          </el-button>
          <el-button
            v-auth.disabled="'InpatientCaregiverPersonDelete'"
            size="small"
            type="danger"
            @click="handleItemAction(ItemActionType.DELETE, scope.row)"
            >删除
          </el-button>
        </template>
      </el-table-column>
    </BaseTable>

    <base-pagination
      v-model:current-page="paginationData.currentPage"
      v-model:page-size="paginationData.pageSize"
      :total="total"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />
  </div>
</template>

<style scoped lang="scss">
.page-container {
  padding: 20px 0;
}

.input-container {
  width: 200px;
}

.table-container {
  margin: 12px 0 30px;
}
</style>
