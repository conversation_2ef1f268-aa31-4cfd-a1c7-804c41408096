// 和 editorconfig 对应
// js 结尾的配置文件在 webstorm 中有代码提示
module.exports = {
  $schema: 'https://json.schemastore.org/prettierrc',
  // 结尾使用分号
  semi: false,
  // 缩进长度
  tabWidth: 2,
  // 使用单引号
  singleQuote: true,
  // 超过最大值换行
  printWidth: 120,
  // 在对象或数组最后一个元素后面是否加逗号
  trailingComma: 'none',
  //结束行形式
  endOfLine: 'lf',
  // 使用 tab 缩进
  useTabs: false
}

/*  prettier的配置 */
// "prettier.printWidth": 100, // 超过最大值换行
// "prettier.tabWidth": 4, // 缩进字节数
// "prettier.useTabs": false, // 缩进不使用tab，使用空格
// "prettier.semi": true, // 句尾添加分号
// "prettier.singleQuote": true, // 使用单引号代替双引号
// "prettier.proseWrap": "preserve", // 默认值。因为使用了一些折行敏感型的渲染器（如GitHub comment）而按照markdown文本样式进行折行
// "prettier.arrowParens": "avoid", //  (x) => {} 箭头函数参数只有一个时是否要有小括号。avoid：省略括号
// "prettier.bracketSpacing": true, // 在对象，数组括号与文字之间加空格 "{ foo: bar }"
// "prettier.disableLanguages": ["vue"], // 不格式化vue文件，vue文件的格式化单独设置
// "prettier.endOfLine": "auto", // 结尾是 \n \r \n\r auto
// "prettier.eslintIntegration": false, //不让prettier使用eslint的代码格式进行校验
// "prettier.htmlWhitespaceSensitivity": "ignore",
// "prettier.ignorePath": ".prettierignore", // 不使用prettier格式化的文件填写在项目的.prettierignore文件中
// "prettier.jsxBracketSameLine": false, // 在jsx中把'>' 单独放一行
// "prettier.jsxSingleQuote": false, // 在jsx中使用单引号代替双引号
// "prettier.parser": "babylon", // 格式化的解析器，默认是babylon
// "prettier.requireConfig": false, // Require a 'prettierconfig' to format prettier
// "prettier.stylelintIntegration": false, //不让prettier使用stylelint的代码格式进行校验
// "prettier.trailingComma": "es5", // 在对象或数组最后一个元素后面是否加逗号（在ES5中加尾逗号）
// "prettier.tslintIntegration": false // 不让prettier使用tslint的代码格式进行校验
