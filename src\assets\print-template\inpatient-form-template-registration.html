<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <title>佛山市中医院入院登记卡</title>
  <style>
    @page {
      size: A4;
      margin: 4mm;
      border: 1px solid black;
    }

    body {
      font-family: "宋体", SimSun, serif;
    }

    .checkbox-with-border {
      width: 18px;
      height: 18px;
      border: 1px solid black;
    }

    .registration-print-container .title {
      text-align: center;
      font-size: 1.4em;
      font-weight: bold;
      margin: 14px 0;
    }

    .registration-print-container {
      width: 100vw;
      height: 100vh;
      margin: 0;
      padding: 0 40px 0 10px;
      box-sizing: border-box;
      line-height: 1.8em;
    }

    .registration-print-container .flex-justify-between {
      display: flex;
      justify-content: space-between;
    }

    .registration-print-container .flex-1 {
      flex: 1;
    }

    .registration-print-container .flex-none {
      flex: none;
    }

    .registration-print-container .border-bottom {
      border-bottom: 1px solid black;
      vertical-align: baseline;
      padding: 0 0.3em;
      box-sizing: border-box;
      display: inline-block;
      line-height: 18px;
      height: 18px;
      overflow: hidden;
      white-space: nowrap;
      word-break: break-all;
      text-overflow: ellipsis;
    }

    .registration-print-container .inline-block {
      display: inline-block;
    }

    .registration-print-container .inline-block.check-box {
      text-align: right;
      width: 2em;
    }

    .registration-print-container .inline-block.check-box-min {
      text-align: right;
      width: 1.4em;
    }

    .registration-print-container .text-large {
      font-size: 1.2em;
    }

    .registration-print-container .table-box {
      padding: 4px 6px;
      border-bottom: 1px solid black;
    }

    .registration-print-container .table-box:last-of-type {
      border-bottom: 0;
    }

    .registration-print-container .footer {
      margin-top: 6px;
      display: flex;
      flex-direction: column;
      gap: 6px;
    }
  </style>
</head>

<body>
<div class="registration-print-container">
  <div class="title">佛山市中医院入院登记卡</div>
  <div style="margin-bottom: 6px;">
    <div class="flex-justify-between">
      <div>记帐号：
        <span class="border-bottom" style="width: 10em;">{{ inpno || "" }}</span></div>
      <div>第
        <span class="inline-block" style="width: 2em;"></span>次住院</div>
      <div>病案号：
        <span class="border-bottom" style="width: 9em;">{{ admissionNo || "" }}</span></div>
    </div>
    <div>以下内容请患者或家属如实填写并请在适当的空格内打“√”</div>
    <div class="flex-justify-between">
      <div>姓名
        <span class="border-bottom" style="width: 8em;">{{ patientName || "" }}</span></div>
      <div>性别
        <span class="inline-block check-box">{{ gender_1 || "" }}</span>
        <span>男</span>
        <span class="inline-block check-box">{{ gender_2 || "" }}</span>
        <span>女</span></div>
      <div>出生日期
        <span class="border-bottom" style="width: 4em;">{{ yearOfBirth || "" }}</span>年
        <span class="border-bottom" style="width: 2em;">{{ monthOfBirth || "" }}</span>月
        <span class="border-bottom" style="width: 2em;">{{ dayOfBirth || "" }}</span>日</div>
      <div>年龄
        <span class="border-bottom" style="width: 3em;">{{ age || "" }}</span></div>
    </div>
    <div class="flex-justify-between">
      <div>出生地
        <span class="border-bottom" style="width: 14em;">{{ birthPlace || "" }}</span></div>
      <div>国籍
        <span class="border-bottom" style="width: 4em;">{{ nationality || "" }}</span></div>
      <div>籍贯
        <span class="border-bottom" style="width: 4em;"></span>省(区、市)
        <span class="border-bottom" style="width: 4em;"></span>市</div>
      <div>民族
        <span class="border-bottom" style="width: 3em;">{{ nation || "" }}</span></div>
    </div>
    <div style="display: flex;">
      <div>身份证
        <span class="border-bottom" style="width: 14em;">{{ patientIdNo || "" }}</span></div>
      <div style="margin-left: 10px;">婚姻：
        <span class="inline-block check-box">{{ marital_10 || "" }}</span>未婚
        <span class="inline-block check-box">{{ marital_20 || "" }}</span>已婚
        <span class="inline-block check-box">{{ marital_30 || "" }}</span>丧偶
        <span class="inline-block check-box">{{ marital_40 || "" }}</span>离婚
        <span class="inline-block check-box">{{ marital_other || "" }}</span>其他</div></div>
    <div class="">职业：
      <span class="inline-block check-box">{{ career_11 || "" }}</span>国家公务员
      <span class="inline-block check-box">{{ career_13 || "" }}</span>专业技术人员
      <span class="inline-block check-box">{{ career_17 || "" }}</span>职员
      <span class="inline-block check-box">{{ career_21 || "" }}</span>企业管理人员
      <span class="inline-block check-box">{{ career_24 || "" }}</span>工人
      <span class="inline-block check-box">{{ career_27 || "" }}</span>农民
      <span class="inline-block check-box">{{ career_31 || "" }}</span>学生
      <div>
        <span class="inline-block check-box" style="width: 4em;">{{ career_37 || "" }}</span>现役军人
        <span class="inline-block check-box">{{ career_51 || "" }}</span>自由职业者
        <span class="inline-block check-box">{{ career_54 || "" }}</span>个体经营者
        <span class="inline-block check-box">{{ career_70 || "" }}</span>无业人员
        <span class="inline-block check-box">{{ career_80 || "" }}</span>退(离)休人员
        <span class="inline-block check-box">{{ career_90 || "" }}</span>其他</div></div>
    <div>联系地址
      <span class="border-bottom" style="width: 30em;">{{ currentResidentialAddress || "" }}</span> 电话
      <span class="border-bottom" style="width: 7em;">{{ mobilephone || "" }}</span></div>
    <div>户口地址
      <span class="border-bottom" style="width: 40em;">{{ domicileAddress || "" }}</span></div>
    <div>工作单位及地址
      <span class="border-bottom" style="width: 25em;">{{ employerName || "" }}</span> 单位电话
      <span class="border-bottom" style="width: 7em;"></span></div>
    <div>联系人姓名
      <span class="border-bottom" style="width: 10em;">{{ contactName || "" }}</span>
      <span class="inline-block" style="width: 1em;"></span>
        关系：
      <span class="inline-block check-box">{{ contactRelationship_1 || "" }}</span>配偶
      <span class="inline-block check-box">{{ contactRelationship_2 || "" }}</span>子
      <span class="inline-block check-box">{{ contactRelationship_3 || "" }}</span>女
      <span class="inline-block check-box">{{ contactRelationship_4 || "" }}</span>(外)孙子女
      <span class="inline-block check-box">{{ contactRelationship_5 || "" }}</span>(岳)父母(外)</div>
    <div>
      <span class="inline-block check-box-min">{{ contactRelationship_6 || "" }}</span>(外)祖父母
      <span class="inline-block check-box">{{ contactRelationship_7 || "" }}</span>兄弟姐妹
      <span class="inline-block check-box">{{ contactRelationship_0 || "" }}</span>家庭内其他关系
      <span class="inline-block check-box">{{ contactRelationship_8 || "" }}</span>非家庭关系成员(请勾选：同事、雇主、领导等)</div>
    <div>联系人地址
      <span class="border-bottom" style="width: 26em;">{{ contactAddress || "" }}</span> 联系人电话
      <span class="border-bottom" style="width: 7em;">{{ contactMobilephone || "" }}</span></div>
  </div>
  <div style="border: 1px solid black">
    <div class="flex-justify-between table-box">
      <div class="flex-none" style="margin-right: 20px;">外伤必填原因：</div>
      <div class="flex-1">
        <div>有责任人的交通事故
          <span class="inline-block" style="width: 2em;"></span>自负责任的交通事故
          <span class="inline-block" style="width: 2em;"></span>自己摔倒
          <span class="inline-block" style="width: 2em;"></span>他人致伤</div>
        <div>工伤事故
          <span class="inline-block" style="width: 2em;"></span>打架、酗酒、自杀等
          <span class="inline-block" style="width: 2em;"></span>
          <span class="inline-block checkbox-with-border" style="margin-right: 4px;vertical-align: middle;"></span>佛山
          <span class="inline-block" style="width: 2em;"></span>
          <span class="inline-block checkbox-with-border" style="margin-right: 4px;vertical-align: middle;"></span>省内异地
          <span class="inline-block" style="width: 2em;"></span>
          <span class="inline-block checkbox-with-border" style="margin-right: 4px;vertical-align: middle;"></span>跨省异地
        </div></div>
    </div>
    <div class="table-box">
      <div>
        <span>职工基本医疗保险：</span>
        <span class="inline-block check-box">{{ insurance_职工基本医疗保险_佛山医保 || "" }}</span>佛山医保
        <span class="inline-block check-box">{{ insurance_职工基本医疗保险_省内异地医保 || "" }}</span>省内异地医保
        <span class="inline-block check-box">{{ insurance_职工基本医疗保险_跨省异地医保 || "" }}</span>跨省异地医保
      </div>

      <div>
        <span>城乡居民基本医疗保险：</span>
        <span class="inline-block check-box">{{ insurance_城乡居民基本医疗保险_佛山医保 || "" }}</span>佛山医保
        <span class="inline-block check-box">{{ insurance_城乡居民基本医疗保险_省内异地医保 || "" }}</span>省内异地医保
        <span class="inline-block check-box">{{ insurance_城乡居民基本医疗保险_跨省异地医保 || "" }}</span>跨省异地医保
      </div>

      <div>
        <span>工伤：</span>
        <span class="inline-block check-box">{{ insurance_工伤_佛山工伤 || "" }}</span>佛山工伤
        <span class="inline-block check-box">{{ insurance_工伤_省内异地工伤 || "" }}</span>省内异地工伤
        <span class="inline-block check-box">{{ insurance_工伤_跨省异地工伤 || "" }}</span>跨省异地工伤
        <span class="inline-block check-box">{{ insurance_工伤_工伤康复 || "" }}</span>工伤康复
      </div>

      <div>
        <span>佛山离休：</span>
        <span class="inline-block check-box">{{ insurance_佛山离休_市直 || "" }}</span>市直
        <span class="inline-block check-box">{{ insurance_佛山离休_禅城 || "" }}</span>禅城
        <span class="inline-block check-box">{{ insurance_佛山离休_南海 || "" }}</span>南海
        <span class="inline-block check-box">{{ insurance_佛山离休_高明 || "" }}</span>高明
        <span class="inline-block check-box">{{ insurance_佛山离休_三水 || "" }}</span>三水
        <span class="inline-block check-box">{{ insurance_佛山离休_顺德 || "" }}</span>顺德
      </div>

      <div>
        <span>自费：</span>
        <span class="inline-block check-box">{{ insurance_自费_全自费 || "" }}</span>全自费
        <span class="inline-block check-box">{{ insurance_自费_由于交通事故、工伤、其他意外不报基本医疗保险 || "" }}</span>由于交通事故、工伤、其他意外不报基本医疗保险
      </div>

      <div>
        <span>其他：</span>
        <span class="inline-block check-box">{{ insurance_其他_省公医 || "" }}</span>省公医
        <span class="inline-block check-box">{{ insurance_其他_贫困救助 || "" }}</span>贫困救助
        <span class="inline-block check-box">{{ insurance_其他_商业医保 || "" }}</span>商业医保
        <span class="inline-block check-box">{{ insurance_其他_全公费 || "" }}</span>全公费
        <span class="inline-block check-box">{{ insurance_其他_其他社保 || "" }}</span>其他社保
      </div>
    </div>
    <div class="table-box">
          <span>注意事项：职工或居民医保参保人员在就医时①未按规定办理医保登记，并经本人或亲属签名确认为“自费患者”的，按自动放弃基本医疗保险待遇处理。②入院超过3日未办理医保登记的，所发生的医疗费用全部由参保人自行承担。③省内异地及跨省异地医保患者须备案成功后三天内到6号楼1楼一站式服务中心办理医保登记手续。④外伤无涉及第三方责任医保患者住院24小时后交承诺书到6号楼1楼一站式服务中心。
            <br />患者或家属声明：
            <br />保证提供资料和勾选事项资料属实，确认已仔细阅读并同意以上注意事项及背面所载的住院病人须知。</span>
      <br />
      <span>患者或家属签名：
            <span class="border-bottom" style="width: 9em;"></span>
            <span class="inline-block" style="width: 4em;"></span>日期：
            <span class="border-bottom" style="width: 4em;"></span>年
            <span class="border-bottom" style="width: 2em;"></span>月
            <span class="border-bottom" style="width: 2em;"></span>日</span></div>
  </div>
  <div class="footer">
    <div>接诊医生填写
      <span class="inline-block" style="width: 2em;"></span>入院途径：
      <span class="inline-block check-box">{{ admway_1 || "" }}</span>急诊
      <span class="inline-block check-box">{{ admway_2 || "" }}</span>门诊
      <span class="inline-block check-box">{{ admway_3 || "" }}</span>其他医疗机构转入
      <span class="inline-block check-box">{{ admway_9 || "" }}</span>其他</div>
    <div>门(急)西医诊断：
      <span class="border-bottom" style="width: 14em;">{{ diag || "" }}</span> 门(急)中医诊断：
      <span class="border-bottom" style="width: 14em;"></span></div>
    <div>入院科别：
      <span class="border-bottom" style="width: 17em;">{{ indptnm || "" }}</span> 医师姓名：
      <span class="border-bottom" style="width: 7em;">{{ doctorName || "" }}</span> 工号：
      <span class="border-bottom" style="width: 6em;">{{ doctorNo || "" }}</span></div>
  </div>
</div>
</body>
</html>
