import type { AccountContextInformationInput } from '@/api/dto/system/admin-manage.dto'
import { ElButton, ElInput, ElLoading, ElTabPane, ElTable, ElTableColumn, ElTabs } from 'element-plus'
import BasePagination from '@/components/base-pagination'
import { usePaginationHook } from '@/hooks/usePagination'
import {
  getSysOrganizationsByManagerIdByPageApi,
  getRolesByManagerIdByPageApi,
  getSysManagerGroupsByManagerIdByPageApi,
} from '@/api/system/admin-manage.api'
import { useLoadingHook } from '@/hooks/useLoading'

export interface AccountContextInformationDialogProps {
  /**账号名字 */
  accountName: string
  /**账号id */
  managerId: string
}

export enum CONTEXT_INFORMATION_ENUM {
  ORGANIZATION = 'organization',
  ROLES = 'roles',
  USER_GROUP = 'userGroup',
}

/**dialog数据 */
const dialogData = reactive({
  managerId: '',
})

/**dialog内容 */
const BodyComponent = defineComponent({
  directives: { loading: ElLoading.directive },

  setup() {
    const { currentPage, pageSize, total } = usePaginationHook(5)

    const { loading, loadingFunc } = useLoadingHook()

    const bodyComData = reactive({
      targetPane: CONTEXT_INFORMATION_ENUM.ORGANIZATION,
      keyword: '',
      tableData: [] as { name: string }[],
    })

    /**表头名字 */
    const tableColumnLabel = computed(() => {
      currentPage.value = 1

      if (bodyComData.targetPane === CONTEXT_INFORMATION_ENUM.ORGANIZATION) {
        return '部门'
      } else if (bodyComData.targetPane === CONTEXT_INFORMATION_ENUM.ROLES) {
        return '角色名称'
      } else if (bodyComData.targetPane === CONTEXT_INFORMATION_ENUM.USER_GROUP) {
        return '用户组名称'
      }
    })

    /**获取关联的机构 */
    const getSysOrganizationsByManagerIdByPage = async (param: AccountContextInformationInput) => {
      const { data, recordCount } = await getSysOrganizationsByManagerIdByPageApi(param, {
        loading: loadingFunc,
        retonly: false,
        showNoData: false,
      })
      bodyComData.tableData = data.map((item) => ({ name: item.organizationName }))
      total.value = recordCount!
    }

    /**获取关联的角色 */
    const getRolesByManagerIdByPage = async (param: AccountContextInformationInput) => {
      const { data, recordCount } = await getRolesByManagerIdByPageApi(param, {
        loading: loadingFunc,
        retonly: false,
        showNoData: false,
      })
      bodyComData.tableData = data.map((item) => ({ name: item.roleName }))
      total.value = recordCount!
    }

    /**获取关联的用户组 */
    const getSysManagerGroupsByManagerIdByPage = async (param: AccountContextInformationInput) => {
      const { data, recordCount } = await getSysManagerGroupsByManagerIdByPageApi(param, {
        loading: loadingFunc,
        retonly: false,
        showNoData: false,
      })
      bodyComData.tableData = data.map((item) => ({ name: item.managerGroupName }))
      total.value = recordCount!
    }

    const handleGetTableData = async () => {
      const param: AccountContextInformationInput = {
        managerId: dialogData.managerId,
        keyword: bodyComData.keyword,
        page: currentPage.value,
        rows: pageSize.value,
      }

      try {
        if (bodyComData.targetPane === CONTEXT_INFORMATION_ENUM.ORGANIZATION) {
          await getSysOrganizationsByManagerIdByPage(param)
        } else if (bodyComData.targetPane === CONTEXT_INFORMATION_ENUM.ROLES) {
          await getRolesByManagerIdByPage(param)
        } else if (bodyComData.targetPane === CONTEXT_INFORMATION_ENUM.USER_GROUP) {
          await getSysManagerGroupsByManagerIdByPage(param)
        }
      } catch (error) {
        bodyComData.tableData = []
        total.value = 1
      }
    }

    /**搜索 */
    const handleSearch = async () => {
      currentPage.value = 1
      handleGetTableData()
    }

    watch([currentPage, pageSize], () => {
      handleGetTableData()
    })

    watch(
      () => bodyComData.targetPane,
      () => {
        currentPage.value = 1
        bodyComData.keyword = ''
        handleGetTableData()
      },
      { immediate: true }
    )

    return () => (
      <>
        <ElTabs v-model={bodyComData.targetPane}>
          <ElTabPane label="所属部门" name="organization" />
          <ElTabPane label="关联的角色" name="roles" />
          <ElTabPane label="所属用户组" name="userGroup" />
        </ElTabs>

        <div class="search-bar flex mt-10">
          <ElInput v-model={bodyComData.keyword} placeholder="关键字搜索" style={{ width: '240px' }} />
          <ElButton type="primary" class="ml-6" onClick={handleSearch}>
            搜索
          </ElButton>
        </div>

        <ElTable v-loading={loading.value} data={bodyComData.tableData} height="328">
          <ElTableColumn label={tableColumnLabel.value} prop="name" />
        </ElTable>

        <BasePagination
          v-model:current-page={currentPage.value}
          v-model:page-size={pageSize.value}
          total={total.value}
        />
      </>
    )
  },
})

/**查看账号关联信息 */
export const useAccountContextInformation = async (props: AccountContextInformationDialogProps) => {
  dialogData.managerId = props.managerId

  await ElMessageBox({
    title: `${props.accountName} - 关联信息`,
    message: () => <BodyComponent></BodyComponent>,
    customStyle: { width: '600px', maxWidth: 'none', maxHeight: '568px', overflow: 'auto' },
    closeOnHashChange: true,
    showCancelButton: false,
    showConfirmButton: false,
  })
}
