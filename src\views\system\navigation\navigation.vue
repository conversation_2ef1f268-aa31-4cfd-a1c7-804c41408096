<script lang="ts" setup>
import TreeCom from './components/tree.vue'
import TableCom from './components/table.vue'
import {
  deleteSysNavigationApi,
  getSysNavigationForPageApi,
  getRightSysNavigationForShowApi,
} from '@/api/system/navigation.api'
import { formatTreeData } from '@/utils'
import type {
  GetSysNavigationForPageInput,
  GetSysNavigationForPageOutput,
  SysNavigationModel,
  SysNavigationMenuItem,
} from '@/api/dto/system/navigation.dto'
import type { Tree } from '@/types/tree'
import { useLoadingHook } from '@/hooks/useLoading'
import { useNavigationDialogHook } from '@/components/navigation-dialog'
import { appMessage } from '@/hooks/useNaiveApi'
import { useUserStore } from '@/stores/user.store'
import { useNavigationRolesDialogHook } from '@/components/navigation-roles-dialog'
import type { OperaRow } from '@/api/dto/index.dto'

const userStore = useUserStore()

const treeData = ref<Tree<SysNavigationModel>[]>([])
const { loading: treeLoading, loadingFunc: treeLoadingFunc } = useLoadingHook()

/**获取字典tree */
const getRightSysNavigationForShow = async () => {
  try {
    const data = await getRightSysNavigationForShowApi({ loading: treeLoadingFunc })
    treeData.value = formatTreeData<SysNavigationModel>(data, 'navigationName', 'navigationId')
  } catch (error) {
    treeData.value = []
  }
}

onMounted(getRightSysNavigationForShow)

const parentId = ref(0)

const handleSelectAll = () => {
  parentId.value = 0
}

const handleSelect = (target: Tree<SysNavigationModel>) => {
  parentId.value = target.extData.mainId
}

const tableComRef = ref<null | InstanceType<typeof TableCom>>(null)
const tableData = ref<GetSysNavigationForPageOutput>([])
const total = ref(1)
const { loading: tableLoading, loadingFunc: tableLoadingFunc } = useLoadingHook()

/**获取字典信息列表 */
const getSysNavigationForPage = async (param: GetSysNavigationForPageInput) => {
  try {
    const { data, recordCount } = await getSysNavigationForPageApi(param, {
      loading: tableLoadingFunc,
      retonly: false,
      showNoData: false,
    })
    tableData.value = data
    total.value = recordCount!
  } catch (error) {
    tableData.value = []
    total.value = 1
  }
}

const { open, close } = useNavigationDialogHook()
onBeforeUnmount(close)
/**打开新增、修改权限dialog */
const handleOpenNavigationDialog = async (target?: SysNavigationMenuItem) => {
  try {
    await open({ navigationId: target?.navigationId || '', parentId: parentId.value })
    getRightSysNavigationForShow()
    tableComRef.value!.getData()
    userStore.getAsyncRouter()
  } catch (error) {}
}
/**新增字典 */
const handleNewNavigation = () => {
  handleOpenNavigationDialog()
}
/**修改字典 */
const handleEditInfo = async (target: OperaRow<SysNavigationMenuItem>) => {
  handleOpenNavigationDialog(target)
}

const { open: openNavigationRolesDialog } = useNavigationRolesDialogHook()
/**编辑值内容 */
const handleEditRoles = (target: OperaRow<SysNavigationMenuItem>) => {
  const { navigationName, navigationId } = target
  openNavigationRolesDialog({ title: navigationName, navigationId })
}

/**删除字典 */
const handleDeleteRow = async (target: OperaRow<SysNavigationMenuItem>) => {
  const { navigationName, navigationId } = target
  try {
    await ElMessageBox.confirm(`确定要删除字典【${navigationName}】吗?`, '提示', { type: 'warning' })
    await deleteSysNavigationApi({ navigationId }, { loading: (flag) => (target.loading = flag) })
    appMessage.success('删除成功')
    getRightSysNavigationForShow()
    tableComRef.value!.getData()
    userStore.getAsyncRouter()
  } catch (error) {}
}
</script>

<template>
  <div class="navigation-container flex">
    <TreeCom v-loading="treeLoading" :tree-data="treeData" @select-all="handleSelectAll" @select="handleSelect" />

    <TableCom
      ref="tableComRef"
      v-loading="tableLoading"
      :parent-id="parentId"
      :table-data="tableData"
      :total="total"
      @search="getSysNavigationForPage"
      @new-navigation="handleNewNavigation"
      @edit-info="handleEditInfo"
      @edit-roles="handleEditRoles"
      @delete-row="handleDeleteRow"
    />
  </div>
</template>

<style lang="scss" scoped>
.navigation-container {
  height: calc(100vh - 90px);
  background-color: var(--el-bg-color-page);
}
</style>
