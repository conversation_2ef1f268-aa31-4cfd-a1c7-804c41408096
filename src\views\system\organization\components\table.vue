<script lang="ts" setup>
import type { OperaRow } from '@/api/dto/index.dto'
import type { GetSysOrganizationForPageInput, OrganizationPageItemModel } from '@/api/dto/system/organization.dto'
import BasePagination from '@/components/base-pagination'
import { useFormHook } from '@/hooks/useForm'
import { usePaginationHook } from '@/hooks/usePagination'

export type SearchData = Omit<GetSysOrganizationForPageInput, 'page' | 'rows' | 'parentId'>

const props = defineProps<{
  tableData: any[]
  parentId: number
  total: number
}>()

const { formRef, resetForm } = useFormHook()
const { currentPage, pageSize } = usePaginationHook()
const formData = reactive<SearchData>({
  organizationName: '',
  enabledMark: '',
})

const emit = defineEmits<{
  search: [payload: GetSysOrganizationForPageInput]
  newOrganization: []
  editInfo: [payload: OperaRow<OrganizationPageItemModel>]
  deleteRow: [payload: OperaRow<OrganizationPageItemModel>]
}>()

const searchFunc = () => {
  emit('search', {
    ...formData,
    parentId: props.parentId,
    page: currentPage.value,
    rows: pageSize.value,
  })
}

const handleSearch = () => {
  currentPage.value = 1
  searchFunc()
}

watch([currentPage, pageSize], () => searchFunc())

watch(
  () => props.parentId,
  () => searchFunc()
)

const handleReset = () => {
  resetForm()
  handleSearch()
}

const handleNewOrganization = () => {
  emit('newOrganization')
}

const handleEditInfo = (target: OperaRow<OrganizationPageItemModel>) => {
  emit('editInfo', target)
}

const handleDeleteRow = (target: OperaRow<OrganizationPageItemModel>) => {
  emit('deleteRow', target)
}

defineExpose({ getData: searchFunc })
</script>

<template>
  <div class="table-container ml-10">
    <el-form ref="formRef" :model="formData" inline>
      <el-form-item prop="organizationName" label="组织名称：">
        <el-input v-model="formData.organizationName"></el-input>
      </el-form-item>

      <el-form-item prop="enabledMark" label="是否启用：">
        <el-select v-model="formData.enabledMark">
          <el-option label="全部" value=""></el-option>
          <el-option label="已启用" :value="true"></el-option>
          <el-option label="未启用" :value="false"></el-option>
        </el-select>
      </el-form-item>

      <el-form-item>
        <el-button v-auth="'getSysOrganizationForPage'" type="primary" @click="handleSearch">搜索</el-button>
        <el-button v-auth="'getSysOrganizationForPage'" @click="handleReset">重置</el-button>
        <el-button type="success" @click="handleNewOrganization"> 新增组织 </el-button>
      </el-form-item>
    </el-form>

    <BaseTable :data="tableData" border height="660">
      <el-table-column prop="organizationName" label="组织名称"></el-table-column>
      <el-table-column prop="managerName" label="负责人"></el-table-column>
      <el-table-column prop="outerTelephone" label="联系方式" width="140"></el-table-column>
      <el-table-column label="是否启用" align="center" width="90">
        <template #default="{ row }: { row: OrganizationPageItemModel }">
          <el-tag :type="row.enabledMark ? 'success' : 'danger'">{{ row.enabledMark ? '启用' : '停用' }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="address" label="地址"></el-table-column>
      <el-table-column prop="description" label="备注"></el-table-column>
      <el-table-column label="操作" min-width="120">
        <template #default="{ row }: { row: OperaRow<OrganizationPageItemModel> }">
          <el-button
            v-auth="[
              'buttonClickToJump_updateSysOrganization',
              'getRightSysOrganizationForShow_addOrUpdataSysOrganization',
            ]"
            @click="handleEditInfo(row)"
          >
            编辑
          </el-button>
          <el-button
            v-auth="'deleteSysOrganization'"
            :loading="row.loading"
            type="danger"
            @click="handleDeleteRow(row)"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </BaseTable>

    <BasePagination v-model:current-page="currentPage" v-model:page-size="pageSize" :total="total" />
  </div>
</template>

<style lang="scss" scoped>
.table-container {
  flex-grow: 1;
  padding: 20px;
  background-color: var(--el-bg-color-overlay);
}
</style>
