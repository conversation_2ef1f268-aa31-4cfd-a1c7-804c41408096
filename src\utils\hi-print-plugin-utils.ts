import { PrintMode } from '@/stores/printer.store.ts'
import { App } from 'vue'
import { autoConnect, hiprint, hiPrintPlugin } from 'vue-plugin-hiprint'

/**
 * HiPrint 插件相关工具方法
 *
 * https://gitee.com/CcSimple/vue-plugin-hiprint
 * https://github.com/CcSimple/vue-plugin-hiprint
 *
 * 打印客户端：https://gitee.com/CcSimple/electron-hiprint
 */

/**
 * 注册 hiPrint 插件到 Vue 应用
 * @param app Vue 应用实例
 * @param pluginName 插件全局名称
 */
export function registerHiPrintPlugin(app: App, pluginName: string = '$hiPrintPlugin') {
  app.use(hiPrintPlugin, pluginName)
  // 可以对插件做一些设置，例如 禁用自动连接
  hiPrintPlugin.disAutoConnect()
  console.log('hiPrintPlugin 注册成功')
}

/**
 * 设置打印客户端连接回调
 */
export function setHiPrintClientConnectCallback(callback: (status: boolean) => void) {
  autoConnect((status: boolean) => {
    callback(!!status)
  })
}

/**
 * 检查打印机客户端是否已经连接
 */
export function getHiPrinterClientStatus() {
  return !!hiprint.hiwebSocket.opened
}
/**
 * 关闭打印机客户端连接
 */
export function closeHiPrinterClient() {
  return hiprint.hiwebSocket.stop()
}

/**
 * 手动连接打印机客户端，一般用不到，插件会默认开启自动轮询连接
 */
export function connectHiPrinterClient() {
  return new Promise<void>((resolve, reject) => {
    if (getHiPrinterClientStatus()) {
      resolve()
      return
    }

    // 打印机客户端连接状态
    let isConnected = false
    // 检查打印机连接状态轮询次数
    let checkStatusIntervalSum = 0

    // 连接时候回调是异步的，按日志提示，就算连接成功了，会回调一次 status = false，之后才回调 true，
    // 所以下方轮询检查打印机客户端是否连接成功，有连接就马上 resolve，否则会一直轮询 5s 超时，避免第一次返回 false 回调
    // 存在两种情况，1. 打印机客户端连接成功 false -> true，2. 打印机客户端连接失败 false 不会变成 true
    autoConnect((status, msg) => {
      console.log('打印机客户端连接回调：', status, msg)
      isConnected = !!status
    })

    // 轮询检查打印机客户端是否连接成功
    const checkStatusInterval = setInterval(() => {
      console.log('打印机客户端连接状态轮询次数：', checkStatusIntervalSum)
      if (isConnected) {
        clearInterval(checkStatusInterval)
        resolve()
      }
      if (checkStatusIntervalSum > 10) {
        clearInterval(checkStatusInterval)
        reject('打印机连接超时，请检查打印机状态是否正常')
      }
      checkStatusIntervalSum++
    }, 500)
  })
}

/**
 * 获取打印机列表
 * 这样获取的是连接成功时「本机的打印机列表」
 */
export function getHiPrinterList() {
  return new Promise((resolve, reject) => {
    if (!getHiPrinterClientStatus()) {
      reject('打印机客户端未连接')
    }

    const printerList = hiprint.hiwebSocket.getPrinterList()
    if (printerList) {
      resolve(printerList)
    } else {
      reject('打印机列表为空，请检查打印机是否连接成功或者刷新页面重试')
    }
  })
}

/**
 * 刷新获取打印机列表(异步)
 */
export function refreshHiPrinterList() {
  return new Promise((resolve, reject) => {
    if (!getHiPrinterClientStatus()) {
      reject(new Error('打印机客户端未连接'))
    }

    hiprint.hiwebSocket.refreshPrinterList((printerList: any) => {
      if (printerList) {
        resolve(printerList)
      } else {
        reject(new Error('打印机列表为空，请检查打印机是否连接成功'))
      }
    })
  })
}

/**
 * 打印 JSON 模板
 * @param template    打印模板 JSON
 * @param data        填充到模板的数据
 * @param printerName 打印机名称
 * @param printMode   打印模式，静默打印 / 打印预览
 * @returns Promise<any> 打印结果的 Promise
 */
export function handleHiPrintTemplate(
  template: any,
  data: Record<string, any>,
  printerName: string,
  printMode: PrintMode
): Promise<any> {
  return new Promise((resolve, reject) => {
    // 检查打印机客户端是否已经连接
    if (printMode === PrintMode.SILENT && !getHiPrinterClientStatus()) {
      reject('打印机客户端未连接')
    }

    if (!template) {
      reject('打印模板为空')
    }

    const hiPrintTemplate = new hiprint.PrintTemplate({
      template
    })

    if (printMode === PrintMode.PREVIEW) {
      // 打印预览模式，直接打印，不需要返回结果
      hiPrintTemplate.print(data, {
        printer: printerName,
        paperNumberDisabled: true
      })
      resolve('')
    } else {
      // 开始打印，print2 表示通过客户端实现静默打印
      hiPrintTemplate.print2(data, {
        printer: printerName,
        paperNumberDisabled: true
      })

      // 绑定成功事件
      hiPrintTemplate.on('printSuccess', (result: any) => {
        resolve(result)
      })

      // 绑定失败事件
      hiPrintTemplate.on('printError', (error: any) => {
        console.error('打印失败：', error)
        reject(error?.msg || error)
      })
    }
  })
}

/**
 * 打印 HTML 模板
 * @param templates   模板数组，每个模板包含 `html` 和 `data`
 * @param printerName 打印机名称
 * @param printMode   打印模式，静默打印 / 打印预览
 * @returns Promise<any> 打印结果的 Promise
 */
export function handleHiPrintHtml(
  templates: { html: string; data?: Record<string, any> }[],
  printerName: string,
  printMode: PrintMode
): Promise<any> {
  return new Promise((resolve, reject) => {
    // 检查打印机客户端是否已经连接
    if (printMode === PrintMode.SILENT && !getHiPrinterClientStatus()) {
      reject('打印机客户端未连接')
      return
    }

    if (!templates || templates.length === 0) {
      reject('打印模板数组为空')
      return
    }

    const hiPrintTemplate = new hiprint.PrintTemplate()

    // 遍历模板数组，逐个添加 PrintPanel 和填充后的 HTML
    templates.forEach(({ html, data }) => {
      if (!html) {
        reject('模板内容为空')
        return
      }

      // 填充模板数据
      const filledHtml = fillHtmlTemplate(html, data)
      const compressedHtml = compressHtml(filledHtml)

      // 添加打印面板并设置 HTML 内容,虽然可以添加多个 html 面板,但是要注意面板间的 style 样式会覆盖!需要特别注意!!!
      const panel = hiPrintTemplate.addPrintPanel({ paperNumberDisabled: true, width: 210, height: 297 })
      panel.addPrintHtml({ options: { top: 0, left: 0, content: compressedHtml } })
    })

    if (printMode === PrintMode.PREVIEW) {
      // 打印预览
      hiPrintTemplate.print(null, {
        printer: printerName,
        // 双面打印
        duplexMode: 'longEdge'
      })
      // 打印预览无法 获取打印结果，所以返回空字符串
      resolve('')
    } else {
      // 开始打印，print2 表示通过客户端实现静默打印
      hiPrintTemplate.print2(null, {
        printer: printerName,
        // 双面打印
        duplexMode: 'longEdge'
      })

      // 绑定成功事件
      hiPrintTemplate.on('printSuccess', (result: any) => {
        resolve(result)
      })

      // 绑定失败事件
      hiPrintTemplate.on('printError', (error: any) => {
        console.error('打印失败：', error)
        reject(error?.msg || error)
      })
    }
  })
}

/**
 * 填充 HTML 模板预留的变量，如果数据中没有对应的 key，则使用默认值
 * 格式：{{ key || "默认值" }}
 * 示例：{{ billingAccount || "        " }}
 * 如果默认值和变量都 为空，则使用空字符串填充
 * @param template HTML 模板字符串
 * @param data 数据对象
 * @returns 填充后的 HTML 字符串
 */
function fillHtmlTemplate(template: string, data?: Record<string, string>): string {
  return template.replace(/{{(.*?)}}/g, (_, expression) => {
    // 拆分表达式，支持默认值逻辑
    const [key, defaultValue] = expression.split('||').map((str) => str.trim())

    // 去掉默认值中的引号，如果有的话
    const cleanedDefaultValue = defaultValue ? defaultValue.replace(/^"|"$/g, '') : ''

    const value = data?.[key]

    // 如果数据中有对应的 key，使用其值，否则使用去除引号后的默认值
    return value || cleanedDefaultValue || ''
  })
}

function compressHtml(html) {
  // 去掉标签前后的换行符，但保留标签内部的空格
  html = html
    .replace(/>\s*\n\s*/g, '>') // 移除标签后多余的换行和空格
    .replace(/\n\s*</g, '<') // 移除标签前多余的换行和空格
    .trim() // 去掉整个字符串首尾的多余空白

  return html
}
