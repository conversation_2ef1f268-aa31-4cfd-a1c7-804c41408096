import { defineStore } from 'pinia'
import { ref } from 'vue'

export const useInpatientNotificationStore = defineStore('inpatientNotification', () => {
  // 用于触发待处理入院列表刷新的标记
  // 每次需要刷新时，只需让这个数字自增即可
  const pendingAdmissionUpdateTicker = ref(0)

  function triggerPendingAdmissionUpdate() {
    pendingAdmissionUpdateTicker.value++
  }

  return {
    pendingAdmissionUpdateTicker,
    triggerPendingAdmissionUpdate
  }
}) 