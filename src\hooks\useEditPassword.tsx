import { ElForm, ElFormItem, ElInput, type FormRules } from 'element-plus'
import type { RuleItem } from 'async-validator'
import { useFormHook } from './useForm'
import { useUserStore } from '@/stores/user.store'
import { validatorPassword } from '@/utils'
import { appMessage } from './useNaiveApi'
import { updatePasswordApi } from '@/api/auth.api'
import { sha256 } from 'js-sha256'
import { resetPasswordApi } from '@/api/system/admin-manage.api'

export interface EditPasswordDialogProps {
  /**是否强制修改密码 */
  force?: boolean
  /**账号id */
  managerId?: string
  /**账号名 */
  managerName?: string
}

export type DialogData = {
  pervPassword: string
  password: string
  checkPassword: string
} & EditPasswordDialogProps

const { formRef, validate, resetForm } = useFormHook()

const dialogData = reactive<DialogData>({
  pervPassword: '',
  password: '',
  checkPassword: '',
})

const validatorCheckPassword: RuleItem['validator'] = (_, value, callback) => {
  if (value === '') {
    callback(new Error('请再次输入密码'))
  } else if (value !== dialogData.password) {
    callback(new Error('两次输入密码不一致!'))
  } else {
    callback()
  }
}
const rules = computed<FormRules<DialogData>>(() => {
  return {
    pervPassword: [{ required: dialogData.force, message: '请输入旧密码', trigger: 'blur' }],
    password: [{ required: true, validator: validatorPassword, trigger: 'blur' }],
    checkPassword: [{ required: true, validator: validatorCheckPassword, trigger: 'blur' }],
  }
})

const dialogBody = () => {
  return (
    <>
      <ElForm ref={(el: any) => (formRef.value = el)} model={dialogData} rules={rules.value} labelWidth="140">
        {dialogData.managerName ? (
          <ElFormItem label="用户名：">
            <span>{dialogData.managerName}</span>
          </ElFormItem>
        ) : undefined}

        {dialogData.force ? (
          <ElFormItem label="旧密码：" prop="pervPassword" class="mt-15">
            <ElInput v-model={dialogData.pervPassword} type="password" showPassword></ElInput>
          </ElFormItem>
        ) : undefined}

        <ElFormItem label="请输入新密码：" prop="password" class="mt-15">
          <ElInput v-model={dialogData.password} type="password" showPassword />
        </ElFormItem>

        <ElFormItem label="请确认新密码：" prop="checkPassword" class="mt-15">
          <ElInput v-model={dialogData.checkPassword} type="password" showPassword />
        </ElFormItem>
      </ElForm>
    </>
  )
}

/**修改密码弹窗 */
export const useEditPasswordHook = async (props?: EditPasswordDialogProps) => {
  dialogData.force = props?.force
  dialogData.managerId = props?.managerId
  if (props?.managerId) {
    dialogData.managerName = props?.managerName
  } else {
    dialogData.managerName = useUserStore().managerInfo?.account
  }

  await ElMessageBox({
    title: '密码修改',
    message: dialogBody,
    customStyle: { width: '550px', maxWidth: 'none' },
    showCancelButton: !props?.force,
    closeOnClickModal: !props?.force,
    closeOnPressEscape: !props?.force,
    closeOnHashChange: !props?.force,
    showClose: !props?.force,
    beforeClose: async (action, instance, done) => {
      if (action === 'cancel') {
        resetForm()
        done()
        return
      }

      try {
        await validate()

        instance.confirmButtonLoading = true
        instance.showCancelButton = false

        if (props?.managerId) {
          // 修改密码
          await resetPasswordApi(
            {
              managerId: props.managerId,
              password: sha256(dialogData.password),
            },
            { loading: false }
          )
        } else {
          // 强制刷新密码
          await updatePasswordApi(
            {
              oldPassword: sha256(dialogData.pervPassword),
              newPassword: sha256(dialogData.password),
            },
            { loading: false }
          )
        }

        appMessage.success('重置密码成功')

        resetForm()
        done()
      } finally {
        instance.confirmButtonLoading = false
        instance.showCancelButton = !props?.force
      }
    },
  })
}
