<script setup lang="ts">
import {
  ResHospitalSitterInfo,
  EscortOrderStatus,
  EscortOrderStatusConfig,
  HospitalSitterInfoPayStatusConfig
} from '@/api/dto/escort-management.dto.ts'
import { formatPrice, maskIdCard } from '@/utils/index.ts'

defineProps<{
  detailInfo: ResHospitalSitterInfo
}>()

const emits = defineEmits<{
  (e: 'confirmService'): void
  (e: 'confirmReturn'): void
}>()

// 确定服务
function handleConfirmService() {
  emits('confirmService')
}

// 确定归还
function handleConfirmReturn() {
  emits('confirmReturn')
}
</script>

<template>
  <div class="content-wrapper">
    <div class="content">
      <div class="title">
        <span>住院信息</span>
        <el-button
          v-if="detailInfo.orderStatus === EscortOrderStatus.RESERVE"
          v-auth.disabled="'HospitalSitterConfirmService'"
          type="success"
          size="large"
          @click="handleConfirmService"
          >确定租赁
        </el-button>
        <el-button
          v-else-if="detailInfo.orderStatus === EscortOrderStatus.RETURN"
          v-auth.disabled="'HospitalSitterConfirmReturn'"
          type="primary"
          size="large"
          @click="handleConfirmReturn"
          >确定归还
        </el-button>
      </div>
      <el-descriptions border :column="2">
        <el-descriptions-item label="住院人姓名">{{ detailInfo.patientName }}</el-descriptions-item>
        <el-descriptions-item label="证件号">{{ maskIdCard(detailInfo.patientIdNo) }}</el-descriptions-item>
        <el-descriptions-item label="住院科室">{{ detailInfo.department }}</el-descriptions-item>
        <el-descriptions-item v-if="detailInfo.doctor" label="主治医生">{{ detailInfo.doctor }}</el-descriptions-item>
        <el-descriptions-item v-if="detailInfo.bedno" label="住院床号">{{ detailInfo.bedno }}</el-descriptions-item>
        <el-descriptions-item label="入院时间">{{ detailInfo.indate }}</el-descriptions-item>
      </el-descriptions>
    </div>

    <div class="content">
      <div class="title">
        <span>申请信息</span>
      </div>
      <el-descriptions border :column="2">
        <el-descriptions-item label="服务名称">{{ detailInfo.serverName }}</el-descriptions-item>
        <el-descriptions-item label="价格">{{ detailInfo.price }}元/天/人</el-descriptions-item>
        <el-descriptions-item label="服务时间">{{ detailInfo.serverTime }}</el-descriptions-item>
        <el-descriptions-item label="状态">
          <el-tag
            :type="EscortOrderStatusConfig[detailInfo.orderStatus]?.tagType || EscortOrderStatusConfig.default.tagType"
          >
            {{ EscortOrderStatusConfig[detailInfo.orderStatus]?.label || EscortOrderStatusConfig.default.label }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="联系人姓名">{{ detailInfo.contactName }}</el-descriptions-item>
        <el-descriptions-item label="联系人电话">{{ detailInfo.contactPhone }}</el-descriptions-item>
        <el-descriptions-item label="订单金额（元）">{{ formatPrice(detailInfo.payFee) }}</el-descriptions-item>
        <el-descriptions-item label="支付状态">{{
          HospitalSitterInfoPayStatusConfig[detailInfo.payStatus]?.label ||
          HospitalSitterInfoPayStatusConfig.default.label
        }}</el-descriptions-item>
        <el-descriptions-item label="业务类型">{{ detailInfo.businessType }}</el-descriptions-item>
        <el-descriptions-item label="平台单号">{{ detailInfo.platformOrderNo }}</el-descriptions-item>
        <el-descriptions-item v-if="detailInfo.payTradeNo" label="支付流水号">{{
          detailInfo.payTradeNo
        }}</el-descriptions-item>
        <el-descriptions-item v-if="detailInfo.payTime" label="支付时间">{{ detailInfo.payTime }}</el-descriptions-item>
        <el-descriptions-item v-if="detailInfo.selfPickupCode" label="自提码">{{
          detailInfo.selfPickupCode
        }}</el-descriptions-item>
        <el-descriptions-item v-if="detailInfo.confirmTime" label="确定时间">{{
          detailInfo.confirmTime
        }}</el-descriptions-item>
        <el-descriptions-item v-if="detailInfo.completeTime" label="完成时间">{{
          detailInfo.completeTime
        }}</el-descriptions-item>
        <el-descriptions-item v-if="detailInfo.cancelTime" label="取消时间">{{
          detailInfo.cancelTime
        }}</el-descriptions-item>
      </el-descriptions>
    </div>
  </div>
</template>

<style scoped lang="scss">
.content {
  & .title {
    margin: 30px 0 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 16px;
  }

  &-wrapper {
    padding: 0 20px 30px;
    display: flex;
    flex-direction: column;
    flex: 1;
    overflow: auto;
  }

  .id-card {
    display: flex;
    align-items: center;
    justify-content: space-between;

    &-image {
      width: 57px;
      height: 36px;
      border-radius: 2px;
      vertical-align: middle;
    }

    &-tips {
      color: #c0c4cc;
    }
  }

  .signature-box {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
}
</style>
