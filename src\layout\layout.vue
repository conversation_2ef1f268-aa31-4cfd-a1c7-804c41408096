<script lang="ts" setup>
import LayoyutMenu from './layout-menu'
import LayoutHeader from './layout-header'
import LayoutTabs from './layout-tabs'
import LayoutPage from './layout-page'
import { useLayoutStore } from '@/stores/layout.store'
import { resizeH<PERSON>lerHook } from './resize-handler.hook'
import { baseCssVariablesSymbol, verticalWidthSymbol } from '@/utils/injection-symbols'

const baseCssVariables = useCssModule('baseCssVariables')
provide(baseCssVariablesSymbol, baseCssVariables)

const layoutStore = useLayoutStore()

resizeHandlerHook()

const layoutClassList = computed(() => {
  return {
    'layout-container': true,
    hideSideBar: layoutStore.menuIsCollapse,
    showSideBar: !layoutStore.menuIsCollapse,
    mobile: layoutStore.isMobile
  }
})

const verticalMarginLeft = computed(() => {
  if (layoutStore.isMobile) {
    return 0
  } else {
    return layoutStore.menuIsCollapse ? baseCssVariables.sideBarCollapseWidth : baseCssVariables.sideBarWidth
  }
})

const verticalWidth = computed(() => {
  if (layoutStore.isMobile) {
    return '100vw'
  } else {
    return `calc(100vw - ${verticalMarginLeft.value})`
  }
})
provide(verticalWidthSymbol, verticalWidth)

const handleClickOutside = () => {
  layoutStore.menuIsCollapse = true
}
</script>

<template>
  <div :class="layoutClassList">
    <XyzTransition xyz="fade">
      <div
        v-if="layoutStore.isMobile && !layoutStore.menuIsCollapse"
        class="drawer-bg"
        @click="handleClickOutside"
      ></div>
    </XyzTransition>

    <LayoyutMenu />

    <div class="vertical">
      <LayoutHeader />

      <LayoutTabs />

      <LayoutPage />
    </div>
  </div>
</template>

<style lang="scss" scoped module="baseCssVariables">
.layout-container {
  display: flex;
  width: 100vw;
  height: 100vh;
  overflow: hidden;
}
</style>

<style lang="scss" scoped>
.drawer-bg {
  background: #000;
  opacity: 0.3;
  width: 100%;
  top: 0;
  height: 100%;
  position: absolute;
  z-index: 999;
}

.vertical {
  position: fixed;
  width: v-bind(verticalWidth);
  margin-left: v-bind(verticalMarginLeft);
  transition: all 0.28s;
  // 解决富文本编辑器全屏时，被菜单栏（LayoyutMenu z-index: 1001）遮挡的问题
  z-index: 1002;
}
</style>
