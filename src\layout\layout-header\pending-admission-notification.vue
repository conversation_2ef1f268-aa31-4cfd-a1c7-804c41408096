<script setup lang="ts">
import { requestInpatientRegistrationByPage } from '@/api/inpatient-registration.api'
import { ref, watch } from 'vue'
import { OperaRow } from '@/api/dto/index.dto'
import { useIntervalFn } from '@vueuse/core'
import type { AdmissionRegistrationExtra } from '@/api/dto/inpatient-registration.dto'
import { AdmissionRegistrationStatus } from '@/api/dto/emun.dto'
import { ROUTER_PATH } from '@/router/router-path'
import { useRouter } from 'vue-router'
import { useInpatientNotificationStore } from '@/stores/inpatient-notification.store'
import { maskIdCard } from '@/utils/index.ts'
import { Refresh } from '@element-plus/icons-vue'

const router = useRouter()
const inpatientNotificationStore = useInpatientNotificationStore()

const dialogVisible = ref(false)
const loading = ref(false)
const tableData = ref<AdmissionRegistrationExtra[]>([])
const pendingCount = computed(() => tableData.value.length)

const lastPatientIds = ref(new Set<string>())

async function fetchPendingList() {
  try {
    loading.value = true
    const { data } = await requestInpatientRegistrationByPage(
      {
        status: AdmissionRegistrationStatus.COMPLETED,
        page: 1,
        rows: 999 // 获取所有待处理的
      },
      { loading: false, showNoData: false }
    )

    tableData.value = data || []

    const newPatientIds = new Set(data.map((p) => p.admissionRegistrationId))
    const newPatients = data.filter((p) => !lastPatientIds.value.has(p.admissionRegistrationId))

    // console.log('获取待处理入院列表：', data, newPatients.length, lastPatientIds.value.size)
    if (newPatients.length > 0) {
      const notificationInstance = ElNotification({
        title: '新入院提醒',
        message: `您有 ${newPatients.length} 位新患者待收治：${newPatients.map((p) => p.patientName).join('、')}`,
        type: 'warning',
        offset: 40,
        onClick: () => {
          notificationInstance.close()
          showDialog()
        }
      })
    }

    lastPatientIds.value = newPatientIds
  } catch (e) {
    console.error('获取待处理入院列表失败：', e)
    tableData.value = []
  } finally {
    loading.value = false
  }
}

watch(
  () => inpatientNotificationStore.pendingAdmissionUpdateTicker,
  () => {
    console.log('全局 - 触发待处理入院列表刷新')
    fetchPendingList()
  }
)

function showDialog() {
  dialogVisible.value = true
  fetchPendingList()
}

function handleDetail(row: AdmissionRegistrationExtra) {
  router.push({
    path: ROUTER_PATH.INPATIENT_REGISTRATION_DETAIL,
    query: {
      admissionRegistrationId: row.admissionRegistrationId
    }
  })
  dialogVisible.value = false
}

const { pause, resume } = useIntervalFn(fetchPendingList, 10000, { immediate: true })

onMounted(() => {
  fetchPendingList()
  resume()
})

onUnmounted(() => {
  pause()
})
</script>

<template>
  <div class="pending-admission-notification">
    <el-badge :value="pendingCount" :show-zero="false" :max="99">
      <el-tooltip content="点击查看待收治患者列表" placement="bottom">
        <div class="bell-button" @click="showDialog">
          <svg-icon name="svg-menu-bell" class="bell-button-icon" />
        </div>
      </el-tooltip>
    </el-badge>

    <el-dialog v-model="dialogVisible" width="60%">
      <template #header>
        <div class="dialog-header">
          <span> 待收治患者列表 </span>
          <el-button :icon="Refresh" type="primary" plain @click="fetchPendingList"> 手动刷新 </el-button>
        </div>
      </template>
      <BaseTable v-loading="loading" border :data="tableData" height="600px">
        <el-table-column prop="patientName" label="住院人姓名" min-width="120" fixed="left" />
        <el-table-column prop="patientIdNo" label="身份证号" min-width="200">
          <template #default="{ row }: { row: OperaRow<AdmissionRegistrationExtra> }">
            {{ maskIdCard(row.patientIdNo) }}
          </template>
        </el-table-column>
        <el-table-column prop="hospitalizationDepartmentName" label="住院科室" min-width="170" />
        <el-table-column prop="applyDepartmentName" label="开卡科室" min-width="150" />
        <el-table-column prop="applyDoctorName" label="开卡医生" min-width="120" />
        <el-table-column prop="applyTime" label="开卡时间" width="180" />
        <el-table-column prop="registrationTime" label="登记时间" width="200">
          <template #default="{ row }: { row: OperaRow<AdmissionRegistrationExtra> }">
            {{ row.registrationTime || '-' }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="100" fixed="right">
          <template #default="{ row }">
            <el-button plain @click="handleDetail(row)"> 详情 </el-button>
          </template>
        </el-table-column>
      </BaseTable>
    </el-dialog>
  </div>
</template>

<style lang="scss" scoped>
.pending-admission-notification {
  display: flex;
  align-items: center;
  height: 100%;
  padding-right: 10px;
}

.dialog-header {
  display: flex;
  align-items: center;
  line-height: var(--el-dialog-font-line-height);
  font-size: var(--el-dialog-title-font-size);
  color: var(--el-text-color-primary);
  gap: 20px;
}

.bell-button {
  width: 22px;
  height: 22px;
  cursor: pointer;

  &-icon {
    width: 100% !important;
    height: 100% !important;
  }
}
</style>
