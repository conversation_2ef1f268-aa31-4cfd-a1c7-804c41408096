<script setup lang="ts">
import { FormInstance, FormRules } from 'element-plus'
import IconSelected from '@/assets/icons/ic_selected.svg'
import { useMountDialog } from '@/hooks/useMountDialog.ts'
import WardSelectionDialog from './ward-selection-dialog.vue'
import BedSelectionDialog from './bed-selection-dialog.vue'

/**
 * 输入住院号弹窗
 */

interface RuleForm {
  hospitalizationNo: string
  wardCode: string
  bedNo: string
}

const props = defineProps<{
  confirmCallback: (data: any) => Promise<void>
}>()

const { open: openWardSelectionDialog } = useMountDialog(WardSelectionDialog)
const { open: openBedSelectionDialog } = useMountDialog(BedSelectionDialog)

const inputFormRef = ref<FormInstance>()

// 确定服务
const inputForm = reactive({
  hospitalizationNo: '',
  wardCode: '',
  bedNo: ''
})

const dialogVisible = defineModel({ default: false })

const inputFormRules = reactive<FormRules<RuleForm>>({
  hospitalizationNo: [{ required: true, message: '请输入住院号', trigger: 'blur' }],
  wardCode: [{ required: false, message: '请选择病区', trigger: 'change' }],
  bedNo: [{ required: false, message: '请选择床位号', trigger: 'change' }]
})

// 弹窗 - 点击确认
async function handleConfirm(done: (flag?: boolean) => void, formEl: FormInstance | undefined) {
  if (!formEl) return
  await formEl.validate(async (valid, fields) => {
    if (valid) {
      try {
        // 提交
        await props.confirmCallback(JSON.parse(JSON.stringify(inputForm)))
        done(false)
        setTimeout(() => {
          formEl.resetFields()
        })
      } catch (e) {
        done(true)
      }
    } else {
      console.log('提交 弹窗 表单 失败 表单校验不通过：', fields)
      done(true)
    }
  })
}

// 选择病区
function handleSelectWard() {
  openWardSelectionDialog({
    defaultWardCode: inputForm.wardCode,
    confirmCallback: async (data: any) => {
      console.log('选择病区', data)
      inputForm.wardCode = data.deptcode
      // 重置床位号和住院号
      inputForm.bedNo = ''
      inputForm.hospitalizationNo = ''
    }
  })
}

// 选择床位
function handleSelectBed() {
  // 需要先选择病区
  if (!inputForm.wardCode) {
    ElMessage.warning('请先选择病区')
    return
  }
  openBedSelectionDialog({
    defaultBedCode: inputForm.bedNo,
    deptcode: inputForm.wardCode,
    confirmCallback: async (data: any) => {
      console.log('选择床位', data)
      inputForm.bedNo = data.bedno
      inputForm.hospitalizationNo = data.patno
    }
  })
}

// 选择住院号
function handleSelectHospitalizationNo() {
  console.log('选择住院号: ', inputForm.hospitalizationNo)
}
</script>

<template>
  <CommonFormDialog
    v-model="dialogVisible"
    title="输入住院号"
    width="524px"
    show-loading
    :disabled-confirm="!inputForm.hospitalizationNo"
    @confirm="handleConfirm($event, inputFormRef)"
  >
    <el-form
      ref="inputFormRef"
      size="large"
      :model="inputForm"
      :rules="inputFormRules"
      label-width="128"
      hide-required-asterisk
    >
      <el-form-item label="住院号或记账号：" prop="hospitalizationNo">
        <el-input v-model="inputForm.hospitalizationNo" class="input-box" clearable placeholder="请输入">
          <template #append>
            <el-button @click="handleSelectHospitalizationNo">
              <el-image class="icon-selected" :src="IconSelected" />
            </el-button>
          </template>
        </el-input>
      </el-form-item>

      <el-form-item label="病区：" prop="wardCode">
        <el-input v-model="inputForm.wardCode" class="input-box" clearable placeholder="请选择" disabled />
        <div class="more-select" @click="handleSelectWard">···</div>
      </el-form-item>

      <el-form-item label="床位号：" prop="bedNo">
        <el-input v-model="inputForm.bedNo" class="input-box" clearable placeholder="请选择" disabled />
        <div class="more-select" @click="handleSelectBed">···</div>
      </el-form-item>
    </el-form>
  </CommonFormDialog>
</template>

<style scoped lang="scss">
.more-select {
  width: 40px;
  height: 40px;
  border-radius: 4px;
  background: #fff;
  border: 1px solid #1890ff;
  font-weight: 500;
  font-size: 16px;
  line-height: 40px;
  text-align: center;
  color: #1890ff;
  margin-left: 16px;
}

.input-box {
  width: 300px;
}

.icon-selected {
  width: 16px;
  height: 18px;
}

:deep(.el-input-group__append, .el-input-group__prepend) {
  padding: 0 13px;
}
</style>
