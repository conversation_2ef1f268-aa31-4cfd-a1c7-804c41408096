import router from '@/router'
import { usePermissionStore } from '@/stores/permission.store'
import type { AxiosRequestConfig } from 'axios'
import { request } from '@/utils/axios-utils'
import { appMessage } from '@/hooks/useNaiveApi'

/**权限接口 */
export const authRequest = (options: AxiosRequestConfig) => {
  try {
    const key = router.currentRoute.value.meta.navigationId
    // 主意：👇这里key使用非空断言是为了触发catch提示,当key为undefined的时候，permissionMap[key]将会报错
    const permission = usePermissionStore().permissionMap[key!]

    return request({
      ...options,
      url: permission[options.url!].url,
    })
  } catch (error) {
    const message = `暂无权限操作`
    appMessage.error(message)
    return Promise.reject(message)
  }
}
