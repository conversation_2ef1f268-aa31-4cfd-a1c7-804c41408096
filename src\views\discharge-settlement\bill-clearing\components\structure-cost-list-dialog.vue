<script setup lang="ts">
import type { ResSettleAnAccountOfCostStructureDTO } from '@/api/dto/discharge-settlement/bill-clearing.dto'
import type { SummaryMethodProps } from '@/components/common-highlight-table/types'
import { formatPrice } from '@/utils'
import CommonHighlightTable from '@/components/common-highlight-table/common-highlight-table.vue'

/**
 * 结构费用列表弹窗
 */

const props = defineProps<{
  /* 数据 */
  costStructure: ResSettleAnAccountOfCostStructureDTO
  /* 点击确认回调 */
  confirmCallback: () => void
}>()

const dialogVisible = defineModel({ default: false })

watch(dialogVisible, (val) => {
  if (val) {
    reset()
  }
})

const tableRef = ref<InstanceType<typeof CommonHighlightTable>>()

// 弹窗 - 点击确认
async function handleConfirm(done: (flag?: boolean) => void) {
  // 提交
  props.confirmCallback()
  done()
}

const getSummaries = (param: SummaryMethodProps) => {
  if (param.columns.length === 0) {
    return []
  }
  const { data } = param
  const sums: (string | VNode)[] = []

  // 项目合计多少项
  const count = props.costStructure.amountToTerm

  // 项目合计金额
  const totalAmount = formatPrice(props.costStructure.amountToYuan)

  // 项目合计打折优惠前金额
  const totalDiscountAmount = formatPrice(data.reduce((acc, item) => acc + item.amountBeforeDiscountOffer, 0))

  sums[0] = ''
  sums[1] = `合计：${count}项 ${totalAmount}元`
  sums[2] = ''
  sums[3] = `${totalDiscountAmount}`
  return sums
}

// 重置
function reset() {
  console.log('重置结构费用选择弹窗')
  tableRef.value?.clearTableSelection()
}

defineExpose({
  reset
})
</script>

<template>
  <CommonFormDialog
    v-model="dialogVisible"
    :close-on-click-modal="false"
    title="结构费用"
    width="766px"
    show-loading
    @confirm="handleConfirm"
  >
    <CommonHighlightTable
      ref="tableRef"
      :data="costStructure.expenseInfoList"
      :max-height="548"
      width="548px"
      show-summary
      :get-summaries="getSummaries"
    >
      <el-table-column type="index" label="No." width="100" align="center" />
      <el-table-column prop="costtype" label="结构名称" width="280" />
      <el-table-column prop="totalAmount" label="金额" width="160" align="right" />
      <el-table-column prop="amountBeforeDiscountOffer" label="打折优惠前金额" min-width="160" align="center" />
    </CommonHighlightTable>
  </CommonFormDialog>
</template>

<style scoped lang="scss">
:deep(.el-table__footer) {
  // 第四列的效果
  .el-table__cell:nth-child(4) {
    text-align: end;
  }
}
</style>
