<script setup lang="ts">
import type { ResExpenseInfoDTO, ExpenseInfo } from '@/api/dto/discharge-settlement/patient-accounts.dto'
import type { SummaryMethodProps } from '@/components/common-highlight-table/types'
import { formatPrice } from '@/utils'
import PatientAllDetailDrawer from './patient-billing-detail-drawer.vue'
import PatientCategoryDetailDrawer from './patient-category-detail-drawer.vue'
import { onKeyStroke } from '@vueuse/core'

const props = defineProps<{
  /** 记账号 */
  inpno: string | undefined
  /** 病人费用信息 */
  patientFee: ResExpenseInfoDTO
  /** 阻止快捷键 */
  preventShortcut?: boolean
}>()

// 全部明细抽屉
const allDetailedBillingVisible = ref(false)

// 分类明细抽屉
const categoryDetailedBillingVisible = ref(false)

// 选择某一行
const selectedRow = ref<ExpenseInfo>()

const getSummaries = (param: SummaryMethodProps) => {
  if (param.columns.length === 0) {
    return []
  }
  const { columns, data } = param
  const sums: (string | VNode)[] = []

  // 项目合计
  const count = data.length
  // 金额合计
  const amount = data.reduce((acc, item) => {
    return acc + Number(item[columns[2].property])
  }, 0)
  // 打折优惠前金额合计
  const amountBeforeDiscount = data.reduce((acc, item) => {
    return acc + Number(item[columns[3].property])
  }, 0)

  sums[0] = ``
  sums[1] = `合计：${count}项  ${formatPrice(amount)}元`
  sums[2] = ''
  sums[3] = `${formatPrice(amountBeforeDiscount)}元`
  return sums
}

// 显示全部明细
function handleShowAllDetailedBilling() {
  allDetailedBillingVisible.value = true
}

// 显示分类明细
function handleShowCategoryDetailedBilling() {
  categoryDetailedBillingVisible.value = true
}

// 全部明细（F1）
onKeyStroke('F1', (e) => {
  e.preventDefault()
  if (props.preventShortcut) {
    return
  }
  handleShowAllDetailedBilling()
})

// 分类明细（F2）
onKeyStroke('F2', (e) => {
  e.preventDefault()
  // 设置了阻止快捷键或没有选中行时候，不执行
  if (props.preventShortcut || !selectedRow.value) {
    return
  }
  handleShowCategoryDetailedBilling()
})

// 选择行
function handleSelectionChange(row: ExpenseInfo) {
  selectedRow.value = row
}
</script>

<template>
  <div class="patient-fee-panel">
    <CommonHighlightTable
      class="patient-fee-table"
      :data="patientFee.expenseInfoList || []"
      :max-height="400"
      show-summary
      show-finger-icon
      :get-summaries="getSummaries"
      @current-change="handleSelectionChange"
    >
      <el-table-column type="index" label="No." width="70" align="center" />
      <el-table-column prop="costtype" label="结构名称" />
      <el-table-column prop="totalAmount" label="金额" width="160" align="right" />
      <el-table-column prop="amountBeforeDiscountOffer" label="打折优惠前金额" width="160" align="right" />
    </CommonHighlightTable>

    <div class="fee-operation-container">
      <div class="common-panel-title">费用操作</div>
      <div class="fee-operation-button" @click="handleShowAllDetailedBilling">全部明细（F1）</div>
      <div v-if="selectedRow" class="fee-operation-button" @click="handleShowCategoryDetailedBilling">
        分类明细（F2）
      </div>
    </div>

    <PatientAllDetailDrawer v-model="allDetailedBillingVisible" :inpno="props.inpno" />
    <PatientCategoryDetailDrawer
      v-model="categoryDetailedBillingVisible"
      :costtype="selectedRow?.costtype"
      :inpno="props.inpno"
    />
  </div>
</template>

<style scoped lang="scss">
.patient-fee-panel {
  display: flex;
  gap: 20px;
}

.patient-fee-table {
  flex: 1;
}

.fee-operation-container {
  display: flex;
  gap: 20px;
  flex-direction: column;

  .fee-operation-button {
    height: 40px;
    width: 320px;
    line-height: 40px;
    text-align: center;
    border-radius: 4px;
    background: #fff;
    border: 1px solid var(--el-border-color-primary);
    color: var(--el-color-primary);
    cursor: pointer;
  }
}
</style>
