import DictionaryDialog, { type OpenParams } from './dictionary-dialog.vue'

export type DictionaryDialogType = InstanceType<typeof DictionaryDialog>
export type DictionaryDialogProps = DictionaryDialogType['$props']

let overlayComponent: DictionaryDialogType | null = null

function createdOverlay(options?: DictionaryDialogProps) {
  const overlayElement = document.createElement('div')
  document.body.appendChild(overlayElement)

  const OverlayInstance = createApp(DictionaryDialog, options)
  overlayComponent = OverlayInstance.mount(overlayElement) as DictionaryDialogType
}

/**打开新增、修改字典弹窗 */
export const useDictionaryDialogHook = () => {
  const open = async (openOpts: OpenParams, dialogOpts?: DictionaryDialogProps) => {
    if (!overlayComponent) {
      createdOverlay(dialogOpts)
    }
    return await overlayComponent!.__open.call(overlayComponent, openOpts)
  }

  const close = () => {
    overlayComponent?.__close.call(overlayComponent)
  }

  return { open, close }
}

export default DictionaryDialog
