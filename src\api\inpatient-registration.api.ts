import {
  ReqAcceptAdmissionRegistration,
  ReqGetAdmissionRegistrationByPage,
  ReqGetAdmissionRegistrationForDetail,
  ResAdmissionEnumsDTO,
  ResDictionaryDTO,
  ReqPrintingOfApplicationForm,
  ReqPrintingOfWristStrap,
  ReqUpdateAdmissionRegistration,
  ResAcceptAdmissionRegistration,
  ResGetAdmissionRegistrationByPage,
  ResGetAdmissionRegistrationForDetail,
  ResPrintingOfApplicationForm,
  ResPrintingOfWristStrap,
  ResUpdateAdmissionRegistration
} from '@/api/dto/inpatient-registration.dto.ts'
import { request } from '@/utils/axios-utils'
import type { ApiFunc, ApiFuncOptions } from 'axios'

/**
 * 住院登记模块
 */

/** 分页获取列表 */
export const requestInpatientRegistrationByPage: ApiFunc<
  ReqGetAdmissionRegistrationByPage,
  ResGetAdmissionRegistrationByPage
> = (data, config: ApiFuncOptions = {}) => {
  return request({
    url: '/foying/web/AdmissionRegistration/getAdmissionRegistrationByPage',
    method: 'post',
    data,
    retonly: false,
    ...config
  })
}

/** 获取详情 */
export const requestInpatientRegistrationForDetail: ApiFunc<
  ReqGetAdmissionRegistrationForDetail,
  ResGetAdmissionRegistrationForDetail
> = (data) => {
  return request({
    url: '/foying/web/AdmissionRegistration/getAdmissionRegistrationForDetail',
    method: 'post',
    data
  })
}

/** 接受处理 */
export const requestAcceptInpatientRegistration: ApiFunc<
  ReqAcceptAdmissionRegistration,
  ResAcceptAdmissionRegistration
> = (data) => {
  return request({
    url: '/foying/web/AdmissionRegistration/accept',
    method: 'post',
    data
  })
}

/** 更新 */
export const requestUpdateInpatientRegistration: ApiFunc<
  ReqUpdateAdmissionRegistration,
  ResUpdateAdmissionRegistration
> = (data) => {
  return request({
    url: '/foying/web/AdmissionRegistration/update',
    method: 'post',
    data
  })
}

/** 入院卡打印 */
export const requestPrintingOfApplicationForm: ApiFunc<ReqPrintingOfApplicationForm, ResPrintingOfApplicationForm> = (
  data
) => {
  return request({
    url: '/foying/web/AdmissionRegistration/printingOfApplicationForm',
    method: 'post',
    data
  })
}

/** 手腕带打印 */
export const requestPrintingOfWristStrap: ApiFunc<ReqPrintingOfWristStrap, ResPrintingOfWristStrap> = (data) => {
  return request({
    url: '/foying/web/AdmissionRegistration/printingOfWristStrap',
    method: 'post',
    data
  })
}


/** 获取国籍字典 */
export const requestNationalityDictionary: ApiFunc<void, ResDictionaryDTO[]> = () => {
  return request({
    url: '/foying/web/AdmissionRegistration/getNationalityDictionary'
  })
}

/** 获取民族字典 */
export const requestNationDictionary: ApiFunc<void, ResDictionaryDTO[]> = () => {
  return request({
    url: '/foying/web/AdmissionRegistration/getNationDictionary'
  })
}

/** 获取地区字典 */
export const requestAreaDictionary: ApiFunc<void, ResDictionaryDTO[]> = () => {
  return request({
    url: '/foying/web/AdmissionRegistration/getAreaDictionary'
  })
}

/** 获取住院信息编辑所需其他字典（婚姻、职业、医保付费类型、性别） */
export const requestAdmissionEnums: ApiFunc<void, ResAdmissionEnumsDTO> = () => {
  return request({
    url: '/foying/web/AdmissionRegistration/getAdmissionEnums'
  })
}
