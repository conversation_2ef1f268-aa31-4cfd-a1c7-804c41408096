const routerMap: Record<string, (() => any) | any> = {
  // 系统管理
  userGroup: () => import('@/views/system/user-group'),
  navigation: () => import('@/views/system/navigation'),
  organization: () => import('@/views/system/organization'),
  role: () => import('@/views/system/roles-manage'),
  manager: () => import('@/views/system/admin-manage'),
  dictionary: () => import('@/views/system/dictionary'),
  auditLog: () => import('@/views/system/audit-log'),
  operationLog: () => import('@/views/system/operation-log'),
}

export default routerMap
