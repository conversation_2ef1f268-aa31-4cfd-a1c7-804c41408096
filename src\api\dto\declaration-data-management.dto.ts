/**
 * 申报资料管理 模块
 */

/**
 * 搜索 申报资料信息
 */
export interface ReqDeclarationApplyFileByPage {
  /* 申报资料名称 */
  fileName?: string
  /** 住院人姓名 */
  admissionName?: string
  /** 住院号 */
  admissionNo?: string
  /* 当前页 */
  page: number
  /* 每页的记录数 */
  rows: number
}

// 分页获取 申报资料信息 列表响应
export interface ResDeclarationApplyFileByPage {
  data: DeclarationApplyFileItem[] // 实体列表
  pageIndex: number // 当前页
  pageSize: number // 每页记录数
  recordCount: number // 总记录数
  pageCount: number // 总页数
}

/**
 * 申报资料信息列表 item 数据
 */
export interface DeclarationApplyFileItem {
  /* 申报资料id */
  applefileId: string
  /* 申报资料名称 */
  fileName: string
  /** 住院人姓名 */
  admissionName: string
  /** 住院号 */
  admissionNo: string
  /** 住院科室 */
  hospitalizationDepartmentName: string
  /** 提交时间 */
  submissionTime: string
}

/* 获取申报材料详情 */
export interface ReqDeclarationApplyFileDetail {
  /* 申报资料id */
  applefileId: string
  /* 申报资料名称 */
  fileName: string
}
