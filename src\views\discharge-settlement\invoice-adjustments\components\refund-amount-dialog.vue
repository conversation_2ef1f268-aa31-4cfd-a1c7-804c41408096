<script setup lang="ts">
import { FormInstance, FormRules } from 'element-plus'
import { formatPrice } from '@/utils'
import { onKeyStroke } from '@vueuse/core'

/**
 * 应退金额 弹窗
 */

// 币种
const currencyOptions = [
  {
    label: '人民币',
    value: '人民币'
  },
  {
    label: '美元',
    value: '美元'
  }
]

interface RefundData {
  currency: string
  amount: number | undefined
  rmbAmount: number | undefined
}

// TODO 测试数据：各个字段是否能修改还没明确，DTO 也还没声明
const testData = reactive({
  // 发票印刷号
  printNo: '1234567890',
  // 应退金额
  refundAmount: 100
})

const refundList1 = reactive<RefundData>({
  // 币种
  currency: '人民币',
  // 金额
  amount: undefined,
  // 折人民币
  rmbAmount: undefined
})

const refundList2 = reactive<RefundData>({
  // 币种
  currency: '人民币',
  // 金额
  amount: undefined,
  // 折人民币
  rmbAmount: undefined
})

interface RuleForm {
  amount1: number | undefined
  amount2: number | undefined
}

const props = defineProps<{
  confirmCallback: (data: any) => Promise<void>
}>()

const inputFormRef = ref<FormInstance>()

const inputForm = reactive<RuleForm>({
  amount1: undefined,
  amount2: undefined
})

// 监听 inputForm.amount1 的变化，更新到 testData
watch(
  () => inputForm.amount1,
  (newValue) => {
    if (newValue !== undefined && !isNaN(Number(newValue))) {
      const numValue = Number(newValue)
      refundList1.amount = numValue

      // 自动计算折人民币金额
      if (refundList1.currency === '人民币') {
        refundList1.rmbAmount = numValue
      } else {
        refundList1.rmbAmount = numValue * 7.2 // 假设美元汇率为1:7.2
      }
    }
  }
)

// 监听 inputForm.amount2 的变化，更新到 testData
watch(
  () => inputForm.amount2,
  (newValue) => {
    if (newValue !== undefined && !isNaN(Number(newValue))) {
      const numValue = Number(newValue)
      refundList2.amount = numValue

      // 自动计算折人民币金额
      if (refundList2.currency === '人民币') {
        refundList2.rmbAmount = numValue
      } else {
        refundList2.rmbAmount = numValue * 7.2 // 假设美元汇率为1:7.2
      }
    }
  }
)

// 监听币种变化，自动更新折人民币金额
watch(
  () => refundList1.currency,
  (newValue) => {
    if (newValue === '人民币') {
      refundList1.rmbAmount = refundList1.amount
    } else {
      refundList1.rmbAmount = refundList1.amount! * 7.2 // 假设美元汇率为1:7.2
    }
  }
)

watch(
  () => refundList2.currency,
  (newValue) => {
    if (newValue === '人民币') {
      refundList2.rmbAmount = refundList2.amount
    } else {
      refundList2.rmbAmount = refundList2.amount! * 7.2 // 假设美元汇率为1:7.2
    }
  }
)

// 格式化后的金额计算属性
const formattedAmounts = computed(() => {
  return {
    refund1RmbAmount: formatPrice(inputForm.amount1),
    refund2RmbAmount: formatPrice(inputForm.amount2)
  }
})

const dialogVisible = defineModel({ default: false })

const inputFormRules = reactive<FormRules<RuleForm>>({
  amount1: [{ required: true, message: '请输入应退金额', trigger: 'blur' }],
  amount2: [{ required: true, message: '请输入应退金额', trigger: 'blur' }]
})

// 弹窗关闭时，重置表单
watch(dialogVisible, (val) => {
  if (val) {
    // 打开弹窗时，同步 testData 到 inputForm
    inputForm.amount1 = undefined
    inputForm.amount2 = undefined
  } else {
    inputFormRef.value?.resetFields()
  }
})

// 弹窗 - 点击确认
async function handleConfirm(done: (flag?: boolean) => void, formEl: FormInstance | undefined) {
  if (!formEl) return
  await formEl.validate(async (valid, fields) => {
    if (valid) {
      try {
        // 提交
        await props.confirmCallback(JSON.parse(JSON.stringify(inputForm)))
        done(false)
        setTimeout(() => {
          formEl.resetFields()
        })
      } catch (e) {
        done(true)
      }
    } else {
      console.log('提交 弹窗 表单 失败 表单校验不通过：', fields)
      done(true)
    }
  })
}

// 找零
function handleFindChange() {
  console.log('找零')
}

// 使用 watchEffect 来监听 F2 键，只在对话框打开时生效
watchEffect((onCleanup) => {
  // 只有当对话框可见时才注册键盘事件监听
  if (dialogVisible.value) {
    // 返回的函数是一个清理函数，当重新运行 watchEffect 或组件卸载时会自动调用
    const stopListener = onKeyStroke('F2', (e) => {
      e.preventDefault()
      handleFindChange()
    })

    // 当 dialogVisible 变为 false 或组件卸载时，自动清除监听
    onCleanup(() => {
      stopListener()
    })
  }
})
</script>

<template>
  <CommonFormDialog
    v-model="dialogVisible"
    class="refund-amount-dialog"
    title="应退金额"
    width="648px"
    show-loading
    @confirm="handleConfirm($event, inputFormRef)"
  >
    <el-form ref="inputFormRef" size="large" :model="inputForm" :rules="inputFormRules" hide-required-asterisk>
      <div class="form-item">
        <div class="form-item-content">
          <div class="form-item-label form-item-label--max">发票印刷号：</div>
          <el-input
            v-model="testData.printNo"
            class="form-item-value--large form-item-value--red form-item-value--left"
            placeholder="请输入"
          />
        </div>
      </div>

      <el-divider />

      <div class="form-item">
        <div class="form-item-content">
          <div class="form-item-label">应退金额：</div>
          <el-input v-model="testData.refundAmount" class="form-item-value--large" placeholder="请输入" />
        </div>
      </div>

      <el-divider />

      <div class="form-item form-item--bold" style="gap: 20px">
        <div class="fee-panel-title">
          <div class="common-panel-title">应退</div>
          <el-button type="success" size="large" style="width: 84px; height: 36px" @click="handleFindChange"
            >找零(F2)</el-button
          >
        </div>

        <div class="fee-panel">
          <div class="fee-panel-item">
            <div class="fee-panel-item-label">币种：</div>
            <el-select v-model="refundList1.currency" class="form-item-value" clearable>
              <el-option v-for="item in currencyOptions" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>

            <div class="fee-panel-item-label">金额：</div>
            <el-form-item prop="amount1">
              <el-input v-model="inputForm.amount1" class="form-item-value" placeholder="请输入" />
            </el-form-item>

            <div class="fee-panel-item-label">折人民币：</div>
            <el-input
              v-model="formattedAmounts.refund1RmbAmount"
              class="form-item-value form-item-value--bg-warning"
              disabled
            />
          </div>

          <div class="fee-panel-item">
            <div class="fee-panel-item-label">币种：</div>
            <el-select v-model="refundList2.currency" class="form-item-value" clearable>
              <el-option v-for="item in currencyOptions" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>

            <div class="fee-panel-item-label">金额：</div>
            <el-form-item prop="amount2">
              <el-input
                v-model="inputForm.amount2"
                class="form-item-value form-item-value--bg-warning"
                placeholder="请输入"
              />
            </el-form-item>

            <div class="fee-panel-item-label">折人民币：</div>
            <el-input
              v-model="formattedAmounts.refund2RmbAmount"
              class="form-item-value form-item-value--bg-warning"
              disabled
            />
          </div>
        </div>
      </div>
    </el-form>
  </CommonFormDialog>
</template>

<style scoped lang="scss">
.refund-amount-dialog {
  :deep(.el-dialog .el-dialog__body) {
    padding: 0 !important;
  }

  :deep(.el-divider) {
    margin: 0 !important;
  }

  :deep(.el-form-item--large) {
    margin-bottom: 0 !important;
  }

  :deep(.el-form-item__content) {
    margin-bottom: 0 !important;
    min-width: 0 !important;
  }

  .form-item {
    padding: 20px 16px;
    display: flex;
    flex-direction: column;
    gap: 20px;
  }

  :deep(.el-input__inner) {
    color: #303133 !important;
    text-align: right !important;
    height: 32px !important;
    font-size: 16px !important;
    -webkit-text-fill-color: #303133 !important;
  }

  .form-item-value--large :deep(.el-input__inner) {
    font-size: 30px !important;
  }

  .form-item--bold :deep(.el-input__inner) {
    font-weight: 700 !important;
  }

  .form-item-value--red :deep(.el-input__inner) {
    color: #eb0000 !important;
    -webkit-text-fill-color: #eb0000 !important;
  }

  .form-item-value--left :deep(.el-input__inner) {
    text-align: left !important;
  }

  .form-item-value--bg-warning :deep(.el-input__wrapper) {
    background-color: #fee9a6 !important;
  }

  :deep(.el-select__wrapper) {
    height: 34px !important;
    min-height: 34px !important;
  }
}

.form-item-content {
  display: flex;
  align-items: center;
  color: #303133 !important;

  & .form-item-label {
    width: 160px;
    text-align: right;
    font-size: 20px;

    &--max {
      font-size: 24px;
    }
  }
}

.form-item-value {
  width: 130px;

  &--large {
    font-size: 30px;
    width: 412px;
    height: 60px;
  }
}

.fee-panel {
  display: flex;
  flex-direction: column;
  gap: 20px;
  padding: 18px 16px;
  background-color: #f5f7fa;

  & .fee-panel-item {
    display: flex;
    align-items: center;

    .fee-panel-item-label {
      margin-left: 12px;

      &:first-child {
        margin-left: 0;
      }
    }
  }
}

.fee-panel-title {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
</style>
