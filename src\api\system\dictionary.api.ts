import type {
  AddSysDictionaryDetailInput,
  AddSysDictionaryInfoInput,
  DeleteSysDictionaryDetailInput,
  DeleteSysDictionaryInfoInput,
  GetSysDictionaryDetailForPageInput,
  GetSysDictionaryDetailForPageOutput,
  GetSysDictionaryDetailForUpdateInput,
  GetSysDictionaryDetailForUpdateOutput,
  GetSysDictionaryInfoForPageInput,
  GetSysDictionaryInfoForPageOutput,
  GetSysDictionaryInfoForShowOutput,
  GetSysDictionaryInfoForUpdateInput,
  GetSysDictionaryInfoForUpdateOutput,
  UpdateSysDictionaryDetailInput,
  UpdateSysDictionaryInfoInput,
} from '../dto/system/dictionary.dto'
import { authRequest } from '../utils.api'
import type { ApiFunc } from 'axios'

/**获取字典类别 */
export const getSysDictionaryInfoForShowApi: ApiFunc<undefined, GetSysDictionaryInfoForShowOutput> = (options) => {
  return authRequest({ url: 'getSysDictionaryInfoForShow', ...options })
}

/**获取字典详情列表 */
export const getSysDictionaryInfoForPageApi: ApiFunc<
  GetSysDictionaryInfoForPageInput,
  GetSysDictionaryInfoForPageOutput
> = (data, options) => {
  return authRequest({ url: 'getSysDictionaryInfoForPage', data, ...options })
}

/**获取字典详情 */
export const getSysDictionaryInfoForUpdateApi: ApiFunc<
  GetSysDictionaryInfoForUpdateInput,
  GetSysDictionaryInfoForUpdateOutput
> = (data, options) => {
  return authRequest({ url: 'getSysDictionaryInfoForUpdate', data, ...options })
}

/**新增字典 */
export const addSysDictionaryInfoApi: ApiFunc<AddSysDictionaryInfoInput, undefined> = (data, options) => {
  return authRequest({ url: 'addSysDictionaryInfo', data, ...options })
}

/**修改字典 */
export const updateSysDictionaryInfoApi: ApiFunc<UpdateSysDictionaryInfoInput, undefined> = (data, options) => {
  return authRequest({ url: 'updateSysDictionaryInfo', data, ...options })
}

/**删除字典 */
export const deleteSysDictionaryInfoApi: ApiFunc<DeleteSysDictionaryInfoInput, undefined> = (data, options) => {
  return authRequest({ url: 'deleteSysDictionaryInfo', data, ...options })
}

/**获取字典内容详情列表 */
export const getSysDictionaryDetailForPageApi: ApiFunc<
  GetSysDictionaryDetailForPageInput,
  GetSysDictionaryDetailForPageOutput
> = (data, options) => {
  return authRequest({ url: 'getSysDictionaryDetailForPage', data, ...options })
}

/**获取字典内容详情项目详情 */
export const getSysDictionaryDetailForUpdateApi: ApiFunc<
  GetSysDictionaryDetailForUpdateInput,
  GetSysDictionaryDetailForUpdateOutput
> = (data, options) => {
  return authRequest({ url: 'getSysDictionaryDetailForUpdate', data, ...options })
}

/**新增字典内容详情项目详情 */
export const addSysDictionaryDetailApi: ApiFunc<AddSysDictionaryDetailInput, undefined> = (data, options) => {
  return authRequest({ url: 'addSysDictionaryDetail', data, ...options })
}

/**更新字典内容详情项目详情 */
export const updateSysDictionaryDetailApi: ApiFunc<UpdateSysDictionaryDetailInput, undefined> = (data, options) => {
  return authRequest({ url: 'updateSysDictionaryDetail', data, ...options })
}

/**删除字典内容详情项目 */
export const deleteSysDictionaryDetailApi: ApiFunc<DeleteSysDictionaryDetailInput, undefined> = (data, options) => {
  return authRequest({ url: 'deleteSysDictionaryDetail', data, ...options })
}
