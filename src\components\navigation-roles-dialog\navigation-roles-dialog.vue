<script lang="ts" setup>
import ElConfig from '@/components/el-config'
import { useElDialogHook } from '@/hooks/useDialog'
import BasePagination from '../base-pagination'
import { usePaginationHook } from '@/hooks/usePagination'
import type { GetSysFunctionauthForPageOutput, SysFunctionauthModel } from '@/api/dto/system/navigation.dto'
import { getSysFunctionauthForPageApi, deleteSysFunctionauthApi } from '@/api/system/navigation.api'
import { useLoadingHook } from '@/hooks/useLoading'
import { appMessage } from '@/hooks/useNaiveApi'
import { useNavigationAddRolesDialogHook } from '../navigation-add-roles-dialog'

export interface OpenParams {
  title: string
  navigationId: string
}

export type DialogData = {
  functionAuthName: string
} & OpenParams

const dialogData = reactive<DialogData>({
  title: '',
  functionAuthName: '',
  navigationId: '',
})

const resetDialogData = () => {
  dialogData.title = ''
  dialogData.functionAuthName = ''
  dialogData.navigationId = ''
}

const { dialogVisible, __open, __close } = useElDialogHook(false)

const { loading, loadingFunc } = useLoadingHook()
/**按钮权限列表 */
const rolesList = ref<GetSysFunctionauthForPageOutput>([])
const { currentPage, pageSize, total } = usePaginationHook()

/**获取按钮权限列表 */
const getSysFunctionauthForPage = async () => {
  try {
    const { data, recordCount } = await getSysFunctionauthForPageApi(
      {
        navigationId: dialogData.navigationId,
        functionAuthName: dialogData.functionAuthName,
        page: currentPage.value,
        rows: pageSize.value,
      },
      { loading: loadingFunc, retonly: false, showNoData: false }
    )
    rolesList.value = data
    total.value = recordCount!
  } catch (error) {
    rolesList.value = []
    total.value = 1
  }
}

watch([currentPage, pageSize], () => {
  getSysFunctionauthForPage()
})

const { open, close: closeNavigationAddRolesDialog } = useNavigationAddRolesDialogHook()
const handleOpenNavigationAddRolesDialog = async (target?: SysFunctionauthModel) => {
  try {
    await open({
      navigationName: dialogData.title,
      navigationId: dialogData.navigationId,
      functionAuthId: target?.functionAuthId,
    })
    getSysFunctionauthForPage()
  } catch (error) {}
}

/**添加按钮权限 */
const handleAddRoles = () => {
  handleOpenNavigationAddRolesDialog()
}

/**按钮权限编辑 */
const handleEditRole = (target: SysFunctionauthModel) => {
  handleOpenNavigationAddRolesDialog(target)
}

/**按钮权限删除 */
const handleDeleteRole = async (target: SysFunctionauthModel) => {
  const { functionAuthName, functionAuthId } = target
  try {
    await ElMessageBox.confirm(`是否确定删除【${dialogData.title}】的按钮权限：${functionAuthName}？`, '警告', {
      type: 'warning',
    })
    await deleteSysFunctionauthApi({ functionAuthId })
    appMessage.success('删除成功')
    getSysFunctionauthForPage()
  } catch (error) {}
}

const handleOpenDialog = (openParams: OpenParams) => {
  __open()
  dialogData.title = openParams.title
  dialogData.navigationId = openParams.navigationId
  getSysFunctionauthForPage()
}

const handleCloseDialog = () => {
  __close()
}

const handleClosed = () => {
  resetDialogData()
  closeNavigationAddRolesDialog()
}

defineExpose({
  __open: handleOpenDialog,
  __close: handleCloseDialog,
})
</script>

<template>
  <ElConfig>
    <el-dialog v-model="dialogVisible" width="708" class="navigation_roles_dialog-container" @closed="handleClosed">
      <template #header>
        <div class="title">{{ dialogData.title }} - 权限设置</div>
      </template>

      <el-tabs model-value="button-roles">
        <el-tab-pane label="按钮权限" key="button-roles" name="button-roles"> </el-tab-pane>
      </el-tabs>

      <div class="search-bar flex mt-10 mb-10">
        <el-input
          v-model="dialogData.functionAuthName"
          placeholder="输入搜索关键字"
          style="width: 220px"
          @keydown.enter="getSysFunctionauthForPage"
        />
        <el-button type="primary" class="ml-8" @click="getSysFunctionauthForPage">搜索</el-button>
        <el-button type="success" class="ml-8" @click="handleAddRoles">添加</el-button>
      </div>

      <BaseTable v-loading="loading" :data="rolesList" border height="324">
        <el-table-column prop="functionAuthName" label="名称"></el-table-column>
        <el-table-column label="是否启用">
          <template #default="{ row }: { row: SysFunctionauthModel }">
            <el-tag :type="row.enabledMark ? 'success' : 'danger'">{{ row.enabledMark ? '启用' : '停用' }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作">
          <template #default="{ row }: { row: SysFunctionauthModel }">
            <el-button @click="handleEditRole(row)">编辑</el-button>
            <el-button type="danger" @click="handleDeleteRole(row)">删除</el-button>
          </template>
        </el-table-column>
      </BaseTable>

      <BasePagination v-model:current-page="currentPage" v-model:page-size="pageSize" :total="total" />
    </el-dialog>
  </ElConfig>
</template>

<style lang="scss" scoped>
.navigation_roles_dialog-container {
  .title {
    height: 44px;
    padding: 10px 16px;
    font-size: 18px;
    color: var(--el-text-color-regular);
  }
}
</style>
