import { ROUTER_PATH } from '@/router/router-path.ts'
import type { RouteRecordRaw } from 'vue-router'

const Layout = () => import('@/layout')

export const dischargeSettlementRouter: RouteRecordRaw = {
  path: ROUTER_PATH.DISCHARGE_SETTLEMENT,
  name: 'DischargeSettlement',
  redirect: ROUTER_PATH.PATIENT_ACCOUNTS,
  component: Layout,
  meta: { title: '出院结算办理', icon: 'el-icon-document-checked' },
  children: [
    // 根级路由从动态路由接口获取
    // {
    //   path: ROUTER_PATH.PATIENT_ACCOUNTS,
    //   name: 'PatientAccounts',
    //   component: () => import('@/views/discharge-settlement/patient-accounts'),
    //   meta: { title: '出院病人账务' }
    // },
    // {
    //   path: ROUTER_PATH.BILL_CLEARING,
    //   name: 'BillClearing',
    //   component: () => import('@/views/discharge-settlement/bill-clearing'),
    //   meta: { title: '出院清账结算' }
    // },
    // {
    //   path: ROUTER_PATH.SETTLEMENT_INQUIRY,
    //   name: 'SettlementInquiry',
    //   component: () => import('@/views/discharge-settlement/settlement-inquiry'),
    //   meta: { title: '出院结算查询' }
    // },
    // {
    //   path: ROUTER_PATH.INVOICE_ADJUSTMENTS,
    //   name: 'InvoiceAdjustments',
    //   component: () => import('@/views/discharge-settlement/invoice-adjustments'),
    //   meta: { title: '结算发票退费/作废' }
    // },
    // {
    //   path: ROUTER_PATH.SETTLEMENT_SETTINGS,
    //   name: 'SettlementSettings',
    //   component: () => import('@/views/discharge-settlement/settlement-settings'),
    //   meta: { title: '结算设置' }
    // }
  ]
}
