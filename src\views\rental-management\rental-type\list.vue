<script setup lang="ts">
/**
 * 陪护人租赁管理 - 陪护类型管理
 */
import {
  ReqGetInpatientCaregiverTypeByPage,
  ReqAddInpatientCaregiverType,
  ReqUpdateInpatientCaregiverType,
  ReqInpatientCaregiverTypeId,
  ResInpatientCaregiverTypeItem
} from '@/api/dto/rental-management/rental-type.dto.ts'
import {
  requestGetInpatientCaregiverTypeByPage,
  requestAddInpatientCaregiverType,
  requestUpdateInpatientCaregiverType,
  requestEnableInpatientCaregiverType,
  requestDisableInpatientCaregiverType,
  requestDeleteInpatientCaregiverType
} from '@/api/rental-management.api.ts'
import RentalTypeList from '@/views/rental-management/rental-type/components/rental-type-list.vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { reactive } from 'vue'

const rentalTypeListRef = ref<InstanceType<typeof RentalTypeList>>()

// 表格数据
const infoData = reactive({
  listData: [] as ResInpatientCaregiverTypeItem[],
  total: 0
})

// 搜索
async function handleSearchData(e: {
  currentPage: number
  pageSize: number
  searchData: { serverName?: string; status?: boolean }
}) {
  try {
    const { searchData } = e
    const params: ReqGetInpatientCaregiverTypeByPage = {
      serverName: searchData.serverName,
      status: searchData.status,
      page: e.currentPage,
      rows: e.pageSize
    }

    const res = await requestGetInpatientCaregiverTypeByPage(params)
    infoData.listData = res.data || []
    infoData.total = res.recordCount || 0
  } catch (error) {
    console.error('获取陪护类型列表失败：', error)
    ElMessage.error('获取陪护类型列表失败')
  }
}

// 新增
async function handleAddInfo(data: ReqAddInpatientCaregiverType, resolve: () => void, reject: () => void) {
  try {
    const params: ReqAddInpatientCaregiverType = {
      serverName: data.serverName,
      price: data.price,
      status: data.status,
      remark: data.remark
    }

    await requestAddInpatientCaregiverType(params)
    resolve()

    ElMessage.success('新增陪护类型成功')

    rentalTypeListRef.value?.handleSearch()
  } catch (e) {
    console.error('新增陪护类型失败：', e)
    reject()
    ElMessage.error('新增陪护类型失败')
  }
}

// 编辑
async function handleEditInfo(data: ReqUpdateInpatientCaregiverType, resolve: () => void, reject: () => void) {
  try {
    const params: ReqUpdateInpatientCaregiverType = {
      inpatientCaregiverTypeId: data.inpatientCaregiverTypeId,
      serverName: data.serverName,
      price: data.price,
      status: data.status,
      remark: data.remark
    }

    await requestUpdateInpatientCaregiverType(params)
    resolve()

    ElMessage.success('修改陪护类型成功')

    rentalTypeListRef.value?.handleSearch()
  } catch (e) {
    console.error('编辑陪护类型失败：', e)
    reject()
    ElMessage.error('编辑陪护类型失败')
  }
}

// 启用/禁用
async function handleChangeInfoStatus(e: { id: string; status: boolean }) {
  try {
    const params: ReqInpatientCaregiverTypeId = {
      inpatientCaregiverTypeId: e.id
    }

    if (e.status) {
      await requestEnableInpatientCaregiverType(params)
      ElMessage.success('启用陪护类型成功')
    } else {
      await requestDisableInpatientCaregiverType(params)
      ElMessage.success('禁用陪护类型成功')
    }

    rentalTypeListRef.value?.handleSearch()
  } catch (error) {
    console.error('操作陪护类型状态失败：', error)
    ElMessage.error('操作陪护类型状态失败')
  }
}

// 删除
async function handleDeleteInfo(data: ReqInpatientCaregiverTypeId) {
  try {
    await ElMessageBox.confirm('确定要删除该陪护类型吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    const params: ReqInpatientCaregiverTypeId = {
      inpatientCaregiverTypeId: data.inpatientCaregiverTypeId
    }

    await requestDeleteInpatientCaregiverType(params)
    ElMessage.success('删除陪护类型成功')

    rentalTypeListRef.value?.handleSearch()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除陪护类型失败：', error)
      ElMessage.error('删除陪护类型失败')
    }
  }
}
</script>

<template>
  <RentalTypeList
    ref="rentalTypeListRef"
    class="container"
    :list-data="infoData.listData"
    :total="infoData.total"
    @search="handleSearchData"
    @add="handleAddInfo"
    @edit="handleEditInfo"
    @changeStatus="handleChangeInfoStatus"
    @delete="handleDeleteInfo"
  />
</template>

<style scoped lang="scss">
.container {
  padding: 30px 20px;
}
</style>
