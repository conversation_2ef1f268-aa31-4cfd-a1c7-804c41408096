<script setup lang="ts">
/**
 * 留人陪护申请管理 - 留人陪护信息管理
 */
import {
  booleanToEscortInfoStatus,
  EscortInfoStatus,
  EscortInfoStatusConfig,
  HospitalSitterServerItem,
  ReqGetHospitalSitterServerByPage
} from '@/api/dto/escort-management.dto.ts'
import { useMountDialog } from '@/hooks/useMountDialog.ts'
import EscortInformationEditDialog from '@/views/escort-management/info/components/escort-information-edit-dialog.vue'
import { toRaw } from 'vue'

enum ItemActionType {
  /* 编辑 */
  EDIT = 'edit',
  /* 启用 */
  ENABLE = 'enable',
  /* 禁用 */
  DISABLE = 'disable',
  /* 删除 */
  DELETE = 'delete'
}

const emits = defineEmits<{
  (e: 'search', data: { currentPage: number; pageSize: number; searchData: ReqGetHospitalSitterServerByPage }): void
  (e: 'add', data: any, resolve: () => void, reject: () => void): void
  (e: 'edit', data: any, resolve: () => void, reject: () => void): void
  (e: 'changeStatus', data: { id: string; status: EscortInfoStatus }): void
  (e: 'delete', data: HospitalSitterServerItem): void
}>()

const props = defineProps<{
  listData: HospitalSitterServerItem[]
  total: number
  departmentOptions: { label: string; value: string }[]
}>()

const { open: openEditDialog } = useMountDialog(EscortInformationEditDialog)

// 搜索表单
const searchFormData = reactive<ReqGetHospitalSitterServerByPage>({} as ReqGetHospitalSitterServerByPage)

// 分页
const paginationData = reactive({
  currentPage: 1,
  pageSize: 10
})

onMounted(() => {
  // 默认加载页面时搜索
  emitSearch()
})

// 搜索
function handleSearch() {
  paginationData.currentPage = 1
  emitSearch()
}

// 重置
function handleReset() {
  searchFormData.department = ''
  searchFormData.status = undefined
  emitSearch()
}

// 新增
function handleAdd() {
  openEditDialog({
    detailInfo: {} as any,
    title: '新增',
    departmentOptions: toRaw(props.departmentOptions),
    confirmCallback: async (data: any) => {
      return new Promise<void>((resolve, reject) => {
        // 父组件可能是异步操作，把 resolve 和 reject 传给子组件，子组件可以调用
        emits('add', data, resolve, reject)
      })
    }
  })
}

function emitSearch() {
  emits('search', {
    currentPage: paginationData.currentPage,
    pageSize: paginationData.pageSize,
    searchData: toRaw(searchFormData)
  })
}

function handleItemAction(itemAction: ItemActionType, itemData: HospitalSitterServerItem) {
  console.log('itemData', itemData)
  // 转换后端数据格式为UI组件需要的格式
  const mappedItem = {
    id: itemData.medicalHospitalSitterServerId,
    departmentName: itemData.department,
    serviceName: itemData.serverName,
    price: itemData.price.toString(),
    total: itemData.totalNumber,
    left: itemData.surplus,
    status: booleanToEscortInfoStatus(itemData.status),
    remark: itemData.remark,
    createTime: itemData.createTime,
    modifyTime: itemData.modifyTime
  }

  switch (itemAction) {
    case ItemActionType.EDIT:
      openEditDialog({
        detailInfo: mappedItem,
        title: '编辑',
        departmentOptions: toRaw(props.departmentOptions),
        confirmCallback: async (data: any) => {
          return new Promise<void>((resolve, reject) => {
            // 父组件可能是异步操作，把 resolve 和 reject 传给子组件，子组件可以调用
            emits('edit', data, resolve, reject)
          })
        }
      })
      break
    case ItemActionType.ENABLE:
      // 启用
      emits('changeStatus', {
        id: itemData.medicalHospitalSitterServerId,
        status: EscortInfoStatus.ENABLE
      })
      break
    case ItemActionType.DISABLE:
      // 禁用
      emits('changeStatus', {
        id: itemData.medicalHospitalSitterServerId,
        status: EscortInfoStatus.DISABLE
      })
      break
    case ItemActionType.DELETE:
      // 删除
      emits('delete', itemData)
      break
    default:
      break
  }
}

function handleSizeChange() {
  // 切换每页条数时把当前页重置回 1
  paginationData.currentPage = 1
  emitSearch()
}

function handleCurrentChange() {
  emitSearch()
}

defineExpose({
  handleSearch
})
</script>

<template>
  <div class="page-container">
    <el-form inline :model="searchFormData">
      <el-form-item label="住院科室：">
        <el-select
          v-model="searchFormData.department"
          class="input-container"
          placeholder="请选择"
          size="large"
          clearable
        >
          <el-option v-for="item in departmentOptions" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="状态：">
        <el-select v-model="searchFormData.status" class="input-container" placeholder="请选择" size="large" clearable>
          <el-option label="启用" :value="true" />
          <el-option label="禁用" :value="false" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button v-auth.disabled="'HospitalSitterServerByPage'" size="large" type="primary" @click="handleSearch">
          搜索
        </el-button>
        <el-button v-auth.disabled="'HospitalSitterServerByPage'" size="large" @click="handleReset">重置</el-button>
        <el-button v-auth.disabled="'HospitalSitterServerAdd'" size="large" type="success" @click="handleAdd">
          新增
        </el-button>
      </el-form-item>
    </el-form>

    <BaseTable class="table-container" :data="listData" border width="100%" height="608">
      <el-table-column prop="department" label="住院科室" width="200" />
      <el-table-column prop="serverName" label="服务名称" width="200" />
      <el-table-column prop="price" label="单价" width="200">
        <template #default="scope"> {{ scope.row.price }}元/天/人 </template>
      </el-table-column>
      <el-table-column prop="totalNumber" label="总数量" width="200" />
      <el-table-column prop="surplus" label="剩余数量" width="200" />
      <el-table-column prop="status" label="状态" width="200">
        <template #default="scope">
          <el-tag
            :type="
              (EscortInfoStatusConfig[booleanToEscortInfoStatus(scope.row.status)]?.tagType ||
                EscortInfoStatusConfig.default.tagType) as any
            "
          >
            {{
              EscortInfoStatusConfig[booleanToEscortInfoStatus(scope.row.status)]?.label ||
              EscortInfoStatusConfig.default.label
            }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="remark" label="备注" width="200" />
      <el-table-column prop="createTime" label="创建时间" width="200" />
      <el-table-column prop="modifyTime" label="修改时间" width="200" />

      <el-table-column label="操作" fixed="right" min-width="280">
        <template #default="scope">
          <el-button
            v-auth.disabled="'HospitalSitterServerUpdate'"
            size="small"
            @click="handleItemAction(ItemActionType.EDIT, scope.row)"
          >
            编辑
          </el-button>
          <el-button
            v-if="!scope.row.status"
            v-auth.disabled="'HospitalSitterServerEnable'"
            size="small"
            type="success"
            @click="handleItemAction(ItemActionType.ENABLE, scope.row)"
            >启用
          </el-button>
          <el-button
            v-if="scope.row.status"
            v-auth.disabled="'HospitalSitterServerDisable'"
            size="small"
            type="warning"
            @click="handleItemAction(ItemActionType.DISABLE, scope.row)"
            >禁用
          </el-button>
          <el-button
            v-auth.disabled="'HospitalSitterServerDelete'"
            size="small"
            type="danger"
            @click="handleItemAction(ItemActionType.DELETE, scope.row)"
            >删除
          </el-button>
        </template>
      </el-table-column>
    </BaseTable>

    <base-pagination
      v-model:current-page="paginationData.currentPage"
      v-model:page-size="paginationData.pageSize"
      :total="total"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />
  </div>
</template>

<style scoped lang="scss">
.page-container {
  padding: 20px 0;
}

.input-container {
  width: 200px;
}

.table-container {
  margin: 12px 0 30px;
}
</style>
