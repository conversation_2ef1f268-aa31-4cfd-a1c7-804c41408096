/**
 * 压缩规则
 * code: 'c'
 * name: 'n'
 * province: 'p'
 * city: 'y'
 * area: 'a'
 * children: 'd'
 */

/**
 * 将原始 cityList 转换为 Element UI Cascader 组件所需的格式
 * @param cityList 原始省市区数据
 * @param useCode 是否使用区域编码，true 则输出 ['110000', '110101']，false 则输出 ['北京市', '东城区']
 */
export const formatCityList = (cityList: any[], useCode = false) => {
  return cityList.map((province) => ({
    value: useCode ? province.c : province.n,
    label: province.n,
    children:
      province.d?.map((city) => ({
        value: useCode ? city.c : city.n,
        label: city.n,
        children:
          city.d?.map((area) => ({
            value: useCode ? area.c : area.n,
            label: area.n
          })) || []
      })) || []
  }))
}

export const loadCityList = async (useCode = false) => {
  const cityList = await import('@/assets/other/city-list-min.json')
  return formatCityList(cityList.default, useCode)
}

/**
 * 格式化省市区数据，纯汉字
 */
export const getCityCascaderOptions = async () => {
  return await loadCityList()
}

/**
 * 格式化省市区数据，使用区域编码
 */
export const getCityCascaderOptionsByCode = async () => {
  return await loadCityList(true)
}
