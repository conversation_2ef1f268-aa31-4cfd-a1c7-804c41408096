<script setup lang="ts">
import { Plus } from '@element-plus/icons-vue'

import type { UploadProps, UploadUserFile } from 'element-plus'

const props = defineProps({
  // 上传数量
  limit: {
    required: true,
    type: Number
  },
  // 文件大小限制，单位 KB
  maxFileSize: {
    required: false,
    type: Number
  },
  // 文件类型限制，文件后缀，多个用逗号分隔，例如：['image/jpeg', 'image/png']
  fileType: {
    required: false,
    type: Array,
    default: () => []
  },
  // 提示
  tips: {
    required: false,
    type: String,
    default: ''
  },
  // 自定义样式
  height: {
    required: false,
    type: String,
    default: '120px'
  },
  width: {
    required: false,
    type: String,
    default: '120px'
  },
  // 是否使用base64模式，如果为true，则上传的文件会被转换为base64格式
  useBase64: {
    required: false,
    type: Boolean,
    default: false
  },
  // 是否禁用
  disabled: {
    required: false,
    type: Boolean,
    default: false
  }
})

// 预览窗口
const dialogImageUrl = ref('')
const dialogVisible = ref(false)

const fileList = defineModel<UploadUserFile[]>({ required: true, default: [] })

const hideUpload = computed(() => {
  return fileList.value.length >= props.limit
})

const handleChange: UploadProps['onChange'] = (uploadFile) => {
  if (props.fileType.length && !props.fileType.includes(uploadFile.raw?.type)) {
    ElMessage.error(`请上传指定的文件类型：${props.fileType.join(', ')}`)
    removeFileItem(uploadFile)
    return
  }

  // uploadFile.size 是字节数
  if (props.maxFileSize && uploadFile.size && uploadFile.size > props.maxFileSize * 1024) {
    ElMessage.error(`上传的文件大小超过限制：${props.maxFileSize}KB，请重新上传`)
    removeFileItem(uploadFile)
    return
  }

  // 如果启用了base64模式，将文件转换为base64
  if (props.useBase64 && uploadFile.raw) {
    convertToBase64(uploadFile)
  }
}

// 将文件转换为base64
const convertToBase64 = (file: UploadUserFile) => {
  if (!file.raw) return

  const reader = new FileReader()
  reader.readAsDataURL(file.raw)
  reader.onload = (e) => {
    if (e.target?.result) {
      // 更新文件列表中对应文件的url为base64数据
      const index = fileList.value.findIndex((item) => item.uid === file.uid)
      if (index !== -1) {
        const updatedFile = { ...fileList.value[index] }
        updatedFile.url = e.target.result as string

        // 创建新数组以触发响应式更新
        const newFileList = [...fileList.value]
        newFileList[index] = updatedFile
        fileList.value = newFileList
      }
    }
  }
}

const removeFileItem = (file: UploadUserFile) => {
  fileList.value = fileList.value.filter((item) => item.uid !== file.uid)
}

const handlePictureCardPreview: UploadProps['onPreview'] = (uploadFile) => {
  dialogImageUrl.value = uploadFile.url!
  dialogVisible.value = true
}
</script>

<template>
  <div>
    <el-upload
      v-model:file-list="fileList"
      class="thumbnail-uploader"
      :class="hideUpload ? 'hide-upload' : ''"
      list-type="picture-card"
      :accept="fileType.join(',')"
      :auto-upload="false"
      :show-file-list="true"
      :limit="limit"
      :disabled="disabled"
      :on-change="handleChange"
      :on-preview="handlePictureCardPreview"
    >
      <el-icon><Plus /></el-icon>

      <template #tip>
        <div class="tips">{{ tips }}</div>
      </template>
    </el-upload>

    <el-image-viewer
      v-if="dialogVisible"
      hide-on-click-modal
      :url-list="[dialogImageUrl]"
      @close="dialogVisible = false"
    />
  </div>
</template>

<style lang="scss" scoped>
.thumbnail-uploader :deep(.el-upload) {
  border: 1px dashed #c0ccda;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: 0.2s;
  background-color: #fbfdff;
  width: v-bind('width');
  height: v-bind('height');
}

.hide-upload :deep(.el-upload) {
  display: none;
}

.thumbnail-uploader :deep(.el-upload:hover) {
  border-color: var(--el-color-primary);
}

.thumbnail-uploader :deep(.el-icon--close-tip) {
  display: none !important;
}

.thumbnail-uploader :deep(.el-upload-list__item-status-label) {
  display: none !important;
}

.thumbnail-uploader {
  & :deep(.el-upload-list__item) {
    width: v-bind('width');
    height: v-bind('height');
  }
}

.tips {
  font-size: 14px;
  color: #909399;
}
</style>
