<script lang="ts" setup>
import type { StyleValue } from 'vue'
import type { AutocompleteFetchSuggestions, AutocompleteInstance } from 'element-plus'
import type { RouteRecordNormalized } from 'vue-router'
import { useLayoutStore } from '@/stores/layout.store'
import { storeToRefs } from 'pinia'
import type { RouteRecordRaw } from 'vue-router'

type SearchItemModel = {
  label: string
  value: string
  extData: RouteRecordNormalized
}

const route = useRoute()

const layoutStore = useLayoutStore()
const { menuList } = storeToRefs(layoutStore)
const menuMap = computed(() => {
  const map = new Map<string, RouteRecordRaw>()
  const getMap = (list: RouteRecordRaw[]) => {
    list.forEach((item) => {
      map.set(item.path, item)
      if (item.children?.length) {
        getMap(item.children)
      }
    })
  }
  getMap(menuList.value)
  return map
})

const autocompleteRef = ref<AutocompleteInstance | null>(null)

const showSearchInputFlag = ref(false)

const showSearchInput = () => {
  showSearchInputFlag.value = !showSearchInputFlag.value
  if (showSearchInputFlag.value) {
    setTimeout(async () => {
      autocompleteRef.value?.focus()

      // autocompleteRef.value?.highlightedIndex()
    }, 280)
  }
}

const searchInputStyle = computed<StyleValue>(() => {
  return {
    width: showSearchInputFlag.value ? '192px' : '0px',
    overflow: 'hidden'
  }
})

const searchKey = ref('')

const router = useRouter()

const routeIsHidden = (route: RouteRecordNormalized) => {
  return menuMap.value.get(route.path)?.hidden
}

/**匹配结果 */
const resolveList = ref<SearchItemModel[]>([])
/**当前页面在匹配结果的索引 */
const highlightedIndex = computed(() => resolveList.value.findIndex((itemRoute) => itemRoute.value === route.path))
/**搜索菜单 */
const querySearchAsync: AutocompleteFetchSuggestions = (searchKey, callback) => {
  let routes = router.getRoutes().filter((route) => !route.children.length && !routeIsHidden(route))

  if (searchKey) {
    routes = routes.filter((route) => route.meta.title?.includes(searchKey))
  }

  resolveList.value = routes.map((itemRouter) => {
    return {
      active: itemRouter.path === route.path,
      label: itemRouter.meta.title || itemRouter.path,
      value: itemRouter.path,
      extData: itemRouter
    }
  })

  callback(resolveList.value)

  nextTick(() => {
    autocompleteRef.value?.highlight(highlightedIndex.value)
  })
}

const handleSelect = (target: SearchItemModel) => {
  showSearchInputFlag.value = false
  searchKey.value = ''
  router.push(target.extData)
}
</script>

<template>
  <div class="layout_header_search-container">
    <el-tooltip content="站内搜索" placement="bottom">
      <svg-icon name="svg-search" id="headerSearchInputIcon" @click="showSearchInput"></svg-icon>
    </el-tooltip>

    <el-autocomplete
      ref="autocompleteRef"
      v-model="searchKey"
      placeholder="站内搜索"
      value-key="value"
      :fetch-suggestions="querySearchAsync"
      :style="searchInputStyle"
      @select="handleSelect as any"
    >
      <template #default="{ item }">
        <div class="flex flex-justify-between">
          <div>{{ item.label }}</div>
          <div style="color: var(--el-color-primary)">
            {{ item.active ? '当前页面' : '' }}
          </div>
        </div>
      </template>
    </el-autocomplete>
  </div>
</template>

<style lang="scss" scoped>
.layout_header_search-container {
  display: flex;
  align-items: center;
  & > svg {
    margin-right: 8px;
    cursor: pointer;
    font-size: 18px;
  }

  :deep(.el-autocomplete) {
    transition: width 0.28s;
  }
}
</style>
