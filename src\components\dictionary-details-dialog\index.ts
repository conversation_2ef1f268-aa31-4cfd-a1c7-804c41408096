import DictionaryDetailsDialog, { type OpenParams } from './dictionary-details-dialog.vue'

export type DictionaryDetailsDialogType = InstanceType<typeof DictionaryDetailsDialog>
export type DictionaryDetailsDialogProps = DictionaryDetailsDialogType['$props']

let overlayComponent: DictionaryDetailsDialogType | null = null

function createdOverlay(options?: DictionaryDetailsDialogProps) {
  const overlayElement = document.createElement('div')
  document.body.appendChild(overlayElement)

  const OverlayInstance = createApp(DictionaryDetailsDialog, options)
  overlayComponent = OverlayInstance.mount(overlayElement) as DictionaryDetailsDialogType
}

/**打开新增、修改字典值弹窗 */
export const useDictionaryDetailsDialogHook = () => {
  const open = async (openOpts: OpenParams, dialogOpts?: DictionaryDetailsDialogProps) => {
    if (!overlayComponent) {
      createdOverlay(dialogOpts)
    }
    return await overlayComponent!.__open.call(overlayComponent, openOpts)
  }

  const close = () => {
    overlayComponent?.__close.call(overlayComponent)
  }

  return { open, close }
}

export default DictionaryDetailsDialog
