import { ROUTER_PATH } from '@/router/router-path.ts'
import type { RouteRecordRaw } from 'vue-router'

const Layout = () => import('@/layout')

export const insuranceRegistrationRouter: RouteRecordRaw = {
  path: ROUTER_PATH.INSURANCE_REGISTRATION,
  name: 'InsuranceRegistration',
  redirect: ROUTER_PATH.INSURANCE_REGISTRATION_LIST,
  component: Layout,
  meta: { title: '医保登记管理', icon: 'el-icon-first-aid-kit' },
  children: [
    // {
    //   path: ROUTER_PATH.INSURANCE_REGISTRATION_LIST,
    //   name: 'InsuranceRegistrationList',
    //   component: () => import('@/views/insurance-registration/list'),
    //   meta: { title: '医保登记信息' }
    // },
    {
      path: ROUTER_PATH.INSURANCE_REGISTRATION_DETAIL,
      name: 'InsuranceRegistrationDetail',
      component: () => import('@/views/insurance-registration/detail'),
      hidden: true,
      meta: {
        title: '医保登记详情',
        leaveOff: true,
        activeMenu: ROUTER_PATH.INSURANCE_REGISTRATION_LIST,
        noCache: true,
        permissionFrom: ROUTER_PATH.INSURANCE_REGISTRATION_LIST,
        useTab: false
      }
    }
  ]
}
