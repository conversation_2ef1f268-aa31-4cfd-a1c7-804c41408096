<script setup lang="ts">
import type { ResExpenseInfoOfAllOrClassificationDTO } from '@/api/dto/discharge-settlement/patient-accounts.dto'
import { requestPatientExpenseInfoOfClassification } from '@/api/discharge-settlement.api'

/** 分类明细抽屉 */

interface CustomProps {
  /** 费用类别 */
  costtype: string | undefined
  /** 记账号 */
  inpno: string | undefined
}

const props = defineProps<CustomProps>()

const drawerVisible = defineModel<boolean>({ required: true })

const categoryDetailedBilling = ref<ResExpenseInfoOfAllOrClassificationDTO[]>(
  [] as ResExpenseInfoOfAllOrClassificationDTO[]
)

watch(drawerVisible, (newVal) => {
  if (newVal) {
    console.log('打开分类明细抽屉，ID：', props.costtype)
    categoryDetailedBilling.value = []
    // 调用接口获取分类明细
    getCategoryDetailedBilling()
  }
})

// 获取分类明细
async function getCategoryDetailedBilling() {
  if (!props.inpno || !props.costtype) {
    console.error('住院号或费用类别不能为空')
    return
  }
  const res = await requestPatientExpenseInfoOfClassification({ inpno: props.inpno, costtype: props.costtype })
  categoryDetailedBilling.value = res
}
</script>

<template>
  <el-drawer class="patient-category-detail-drawer" v-model="drawerVisible" title="分类明细" size="70%">
    <CommonHighlightTable :data="categoryDetailedBilling" style="height: 100%">
      <el-table-column prop="excutedate" label="执行日期" width="200" />
      <el-table-column prop="advtype" label="医嘱分类" width="160" />
      <el-table-column prop="advno" label="方号" width="80" align="center" />
      <el-table-column prop="itemcode" label="项目代码" width="180" />
      <el-table-column prop="itemname" label="项目名称" min-width="240" />
      <el-table-column prop="quantity" label="数量" width="80" align="center" />
      <el-table-column prop="totalamount" label="金额" width="100" align="right" class-name="amount-column" />
      <el-table-column prop="price" label="价格" width="100" align="right" />
      <el-table-column prop="rate" label="比例" width="80" align="right">
        <template #default="{ row }"> {{ row.rate }}% </template>
      </el-table-column>
      <el-table-column prop="selfamount" label="自付" width="80" align="right" />
    </CommonHighlightTable>
  </el-drawer>
</template>

<style lang="scss">
.patient-category-detail-drawer {
  .el-drawer__header {
    margin-bottom: 0 !important;
    background: #f5f5f5 !important;
    padding: 17px 20px !important;
    font-size: 16px !important;
    color: #303133 !important;
  }

  /* 表格高度自适应 */
  .el-table--fit {
    height: 100% !important;
  }

  .el-table__body .amount-column {
    color: #0007db !important;
  }
}
</style>
