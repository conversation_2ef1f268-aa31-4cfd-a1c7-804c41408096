import { useTooltip } from '@/hooks/useTooltip'
import type { Directive, DirectiveHook } from 'vue'
import { CssRender } from 'css-render'
import router from '@/router'
import { isArray, isString } from 'lodash-es'

type AuthorityModifiers = {
  /**ui上删除 */
  delete?: boolean
  /**ui上禁用 */
  disabled?: boolean
}

type AuthorityBinding = string | string[]

const { c: cssRender } = CssRender()

/**提示class名字 */
const tooltipClassName = 'pangu__tooltip-trigger-authority'
cssRender(`.${tooltipClassName}`, () => ({
  position: 'absolute',
  width: '100%',
  height: '100%'
})).mount({ id: tooltipClassName })

/**权限禁用class名字 */
const displayNoneClassName = 'pangu__auth-display-none'
cssRender(`.${displayNoneClassName}`, () => ({
  display: 'none !important'
})).mount({ id: displayNoneClassName })

const { visible, virtualRef, tooltipContent } = useTooltip()

const showTooltip = (e: Event) => {
  visible.value = true
  virtualRef.value = e.target as HTMLElement
  tooltipContent.value = '暂无操作权限'
}

const hideTooltip = () => {
  visible.value = false
  virtualRef.value = undefined
}

/**判断是否拥有权限操作 */
export const hasPermission = (authValue: AuthorityBinding) => {
  const permission = router.currentRoute.value.meta.extraData?.map((item) => item.encode)
  // 无权限限制,直接放行
  if (!permission) return true
  // 如果authValue是string，permission包含authValue,才算校验成功
  else if (isString(authValue) && permission.includes(authValue)) return true
  // 如果authValue是arrry，必须permission包含authValue每一项，才算校验成功
  else if (isArray(authValue) && authValue.every((item) => permission.includes(item))) return true
  else return false
}

const handleAddDirective: DirectiveHook<HTMLElement, any, AuthorityBinding> = (el, binding, vNode) => {
  const { value: authValue } = binding

  const modifiers: AuthorityModifiers = binding.modifiers

  // 页面不存在任何权限控制或者拥有权限则放行
  if (hasPermission(authValue)) {
    if (modifiers.disabled && el.className.includes('is-disabled') && !vNode.props?.disabled) {
      removeDisabled(el)
      removeTooltip(el)
    } else if (el.className.includes(displayNoneClassName)) {
      el.classList.remove(displayNoneClassName)
    }
    return
  }

  if (modifiers.disabled) {
    addDisabled(el)
    addTooltip(el)
  } else {
    el.classList.add(displayNoneClassName)
  }
}

const authority: Directive<HTMLElement, AuthorityBinding> = {
  mounted: handleAddDirective,
  updated: handleAddDirective,

  unmounted(el) {
    const tooltipTrigger = el.querySelector(`.${tooltipClassName}`)
    tooltipTrigger?.removeEventListener('mouseover', showTooltip)
    tooltipTrigger?.removeEventListener('mouseout', hideTooltip)
  }
}

/**设置不可用 */
const addDisabled = (el: HTMLElement) => {
  el.classList.add('is-disabled')
  el.setAttribute('disabled', '')

  const hasChildren = el.children
  hasChildren &&
    Array.from(hasChildren).forEach((children) => {
      addDisabled(children as HTMLElement)
    })
}

/**移除不可用 */
const removeDisabled = (el: HTMLElement) => {
  el.classList.remove('is-disabled')
  el.removeAttribute('disabled')

  const hasChildren = el.children
  hasChildren &&
    Array.from(hasChildren).forEach((children) => {
      removeDisabled(children as HTMLElement)
    })
}

/**添加tooltip */
const addTooltip = (el: HTMLElement) => {
  el.style.position = 'relative'
  let tooltipTrigger = el.querySelector(`.${tooltipClassName}`)
  if (tooltipTrigger) {
    tooltipTrigger.setAttribute('style', '')
  } else {
    tooltipTrigger = document.createElement('div')

    tooltipTrigger.classList.add(tooltipClassName)

    el.appendChild(tooltipTrigger)

    tooltipTrigger.addEventListener('mouseover', showTooltip)

    tooltipTrigger.addEventListener('mouseout', hideTooltip)
  }
}

/**移除tooltip */
const removeTooltip = (el: HTMLElement) => {
  const tooltipTrigger = el.querySelector(`.${tooltipClassName}`)
  tooltipTrigger?.setAttribute('style', 'display: none')
  hideTooltip()
}

export default authority
