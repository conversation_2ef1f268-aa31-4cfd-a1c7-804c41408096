<script lang="ts" setup>
import TreeCom from './components/tree.vue'
import TableCom from './components/table.vue'
import {
  deleteSysDictionaryInfoApi,
  getSysDictionaryInfoForPageApi,
  getSysDictionaryInfoForShowApi,
} from '@/api/system/dictionary.api'
import { formatTreeData } from '@/utils'
import type {
  GetSysDictionaryInfoForPageInput,
  GetSysDictionaryInfoForPageOutput,
  SysDictionaryDirModel,
  SysDictionaryInfoModel,
} from '@/api/dto/system/dictionary.dto'
import type { Tree } from '@/types/tree'
import { useLoadingHook } from '@/hooks/useLoading'
import { useDictionaryDialogHook } from '@/components/dictionary-dialog'
import { appMessage } from '@/hooks/useNaiveApi'
import { ROUTER_PATH } from '@/router/router-path'
import type { OperaRow } from '@/api/dto/index.dto'

const treeData = ref<Tree<SysDictionaryDirModel>[]>([])
const { loading: treeLoading, loadingFunc: treeLoadingFunc } = useLoadingHook()

/**获取字典tree */
const getSysDictionaryInfoForShow = async () => {
  try {
    const data = await getSysDictionaryInfoForShowApi({ loading: treeLoadingFunc })
    treeData.value = formatTreeData<SysDictionaryDirModel>(data, 'dictionaryInfoName', 'dictionaryInfoId')
  } catch (error) {
    treeData.value = []
  }
}

onMounted(getSysDictionaryInfoForShow)

const parentId = ref(0)

const handleSelectAll = () => {
  parentId.value = 0
}

const handleSelect = (target: Tree<SysDictionaryDirModel>) => {
  parentId.value = target.extData.mainId
}

const tableComRef = ref<null | InstanceType<typeof TableCom>>(null)
const tableData = ref<GetSysDictionaryInfoForPageOutput>([])
const total = ref(1)
const { loading: tableLoading, loadingFunc: tableLoadingFunc } = useLoadingHook()

/**获取字典信息列表 */
const getSysDictionaryInfoForPage = async (param: GetSysDictionaryInfoForPageInput) => {
  try {
    const { data, recordCount } = await getSysDictionaryInfoForPageApi(param, {
      loading: tableLoadingFunc,
      retonly: false,
      showNoData: false,
    })
    tableData.value = data
    total.value = recordCount!
  } catch (error) {
    tableData.value = []
    total.value = 1
  }
}

const { open } = useDictionaryDialogHook()
/**打开新增、修改字典dialog */
const handleOpenDictionaryDialog = async (target?: OperaRow<SysDictionaryInfoModel>) => {
  try {
    await open({ dictionaryInfoId: target?.dictionaryInfoId || '' })
    getSysDictionaryInfoForShow()
    tableComRef.value!.getData()
  } catch (error) {}
}
/**新增字典 */
const handleNewDictionary = () => {
  handleOpenDictionaryDialog()
}
/**修改字典 */
const handleEditInfo = async (target: OperaRow<SysDictionaryInfoModel>) => {
  handleOpenDictionaryDialog(target)
}

const router = useRouter()
/**编辑值内容 */
const handleEditVal = (target: OperaRow<SysDictionaryInfoModel>) => {
  const { dictionaryInfoId, dictionaryInfoName } = target
  router.push({
    path: ROUTER_PATH.DICTIONARY_DETAILS,
    query: { dictionaryInfoId, dictionaryInfoName },
  })
}

/**删除字典 */
const handleDeleteRow = async (target: OperaRow<SysDictionaryInfoModel>) => {
  try {
    await ElMessageBox.confirm(`确定要删除字典【${target.dictionaryInfoName}】吗?`, '提示', { type: 'warning' })
    await deleteSysDictionaryInfoApi({ id: target.dictionaryInfoId }, { loading: (flag) => (target.loading = flag) })
    appMessage.success('删除成功')
    getSysDictionaryInfoForShow()
    tableComRef.value!.getData()
  } catch (error) {}
}
</script>

<template>
  <div class="dictionary-container flex">
    <TreeCom v-loading="treeLoading" :tree-data="treeData" @select-all="handleSelectAll" @select="handleSelect" />

    <TableCom
      ref="tableComRef"
      v-loading="tableLoading"
      :parent-id="parentId"
      :table-data="tableData"
      :total="total"
      @search="getSysDictionaryInfoForPage"
      @new-dictionary="handleNewDictionary"
      @edit-info="handleEditInfo"
      @edit-val="handleEditVal"
      @delete-row="handleDeleteRow"
    />
  </div>
</template>

<style lang="scss" scoped>
.dictionary-container {
  height: calc(100vh - 90px);
  background-color: var(--el-bg-color-page);
}
</style>
