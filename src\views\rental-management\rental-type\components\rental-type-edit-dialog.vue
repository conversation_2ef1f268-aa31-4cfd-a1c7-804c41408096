<script setup lang="ts">
import {
  ReqAddInpatientCaregiverType,
  ResInpatientCaregiverTypeItem,
  ReqUpdateInpatientCaregiverType
} from '@/api/dto/rental-management/rental-type.dto.ts'
import { useFormHook } from '@/hooks/useForm.ts'
import { FormRules } from 'element-plus'

const props = defineProps<{
  detailInfo?: ResInpatientCaregiverTypeItem
  title: string
  confirmCallback: (data: ReqAddInpatientCaregiverType | ReqUpdateInpatientCaregiverType) => Promise<void>
}>()

const { formRef } = useFormHook()

const formData = ref<Record<string, any>>({
  serverName: '',
  price: '',
  status: undefined,
  remark: ''
})

const visible = defineModel({ default: false })

watch(
  visible,
  (v) => {
    if (v) {
      // 重置表单，避免在点击编辑后再点击新增时候，因为弹窗组件没有销毁导致数据不清空
      formRef.value?.resetFields()

      // 合并可能的字段名称，使得组件兼容不同数据来源
      const detail = (props.detailInfo || {}) as Partial<ResInpatientCaregiverTypeItem>
      formData.value = {
        inpatientCaregiverTypeId: detail.inpatientCaregiverTypeId,
        serverName: detail.serverName,
        price: detail.price,
        status: detail.status,
        remark: detail.remark
      }
    }
  },
  {
    immediate: true
  }
)

const rules = reactive<FormRules>({
  serverName: [{ required: true, message: '请输入类型名称', trigger: 'blur' }],
  price: [{ required: true, message: '请输入单价', trigger: 'blur' }],
  status: [{ required: true, message: '请选择状态', trigger: 'change' }],
  remark: [{ required: false, message: '请输入备注', trigger: 'blur' }]
})

const handleConfirm = async (done: (keepVisible: boolean) => void) => {
  await formRef.value!.validate(async (valid) => {
    if (valid) {
      try {
        await props.confirmCallback(JSON.parse(JSON.stringify(formData.value)))
        done(false)
        setTimeout(() => {
          formRef.value!.resetFields()
        })
      } catch (error) {
        // 结束按钮 loading，但是不关闭弹窗
        done(true)
      }
    } else {
      done(true)
    }
  })
}
</script>

<template>
  <CommonFormDialog
    v-model="visible"
    :title="title"
    width="732px"
    showLoading
    :showCancelTips="false"
    @confirm="handleConfirm"
  >
    <el-form ref="formRef" :model="formData" :rules="rules" label-width="auto" label-position="right" size="large">
      <el-row>
        <el-col :span="24">
          <el-form-item label="类型名称：" prop="serverName">
            <el-input v-model="formData.serverName" clearable placeholder="请输入" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row>
        <el-col :span="24">
          <el-form-item label="单价：" prop="price">
            <el-input v-model="formData.price" clearable placeholder="请输入">
              <template #append>元/天</template>
            </el-input>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row>
        <el-col :span="12">
          <el-form-item label="状态：" prop="status">
            <el-radio-group v-model="formData.status">
              <el-radio :value="true">启用</el-radio>
              <el-radio :value="false">禁用</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row>
        <el-col :span="24">
          <el-form-item label="备注：" prop="remark">
            <el-input
              v-model="formData.remark"
              clearable
              type="textarea"
              placeholder="请输入"
              :autosize="{ minRows: 2, maxRows: 6 }"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </CommonFormDialog>
</template>

<style lang="scss" scoped></style>
