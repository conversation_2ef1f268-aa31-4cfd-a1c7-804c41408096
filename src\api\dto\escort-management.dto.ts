/**
 * 留人陪护申请管理
 */

/**
 * 留人陪护信息 - 状态
 * 注意：后端API使用布尔值 true/false 表示状态
 * 这里使用字符串枚举是为了与UI组件交互和显示
 */
export enum EscortInfoStatus {
  /* 启用 */
  ENABLE = '1',
  /* 禁用 */
  DISABLE = '0'
}

// 定义状态常量对象，用于与后端API交互
export const HospitalSitterStatus = {
  // 启用
  ENABLE: true,
  // 禁用
  DISABLE: false
} as const

// 留人陪护信息 - 状态映射配置
export const EscortInfoStatusConfig = {
  [EscortInfoStatus.ENABLE]: { label: '启用', tagType: 'success' },
  [EscortInfoStatus.DISABLE]: { label: '禁用', tagType: 'warning' },
  // 默认配置
  default: { label: '-', tagType: 'info' }
}

/**
 * 将布尔值状态转换为枚举字符串
 * @param status 布尔值状态
 * @returns 对应的枚举字符串
 */
export function booleanToEscortInfoStatus(status: boolean): EscortInfoStatus {
  return status ? EscortInfoStatus.ENABLE : EscortInfoStatus.DISABLE
}

/**
 * 将枚举字符串转换为布尔值状态
 * @param status 枚举字符串
 * @returns 对应的布尔值状态
 */
export function escortInfoStatusToBoolean(status: EscortInfoStatus): boolean {
  return status === EscortInfoStatus.ENABLE
}

/**
 * 留人陪护信息 - 状态 可选项
 */
export const getEscortInfoStatusOptions = (useEnum = true) => {
  const keys = [EscortInfoStatus.ENABLE, EscortInfoStatus.DISABLE]

  if (useEnum) {
    return keys.map((value) => ({
      label: EscortInfoStatusConfig[value].label,
      value: value
    }))
  } else {
    // 返回布尔值类型的数据（用于表单）
    return [
      { label: EscortInfoStatusConfig[EscortInfoStatus.ENABLE].label, value: true },
      { label: EscortInfoStatusConfig[EscortInfoStatus.DISABLE].label, value: false }
    ]
  }
}

/**
 * 获取住院科室列表 - 响应数据
 */
export type ResGetDepartmentList = string[]

/**
 * 获取留人陪护信息分页列表 - 请求参数
 */
export interface ReqGetHospitalSitterServerByPage {
  /** 住院科室 */
  department?: string
  /** 状态 true启用 false禁用 */
  status?: boolean
  /** 当前页 */
  page: number
  /** 每页的记录数 */
  rows: number
}

/**
 * 获取留人陪护信息分页列表 - 列表项
 */
export interface HospitalSitterServerItem {
  /** ID */
  medicalHospitalSitterServerId: string
  /** 住院科室 */
  department: string
  /** 服务名称 */
  serverName: string
  /** 单价 */
  price: number
  /** 总数量 */
  totalNumber: number
  /** 剩余数量 */
  surplus: number
  /** 状态 true启用 false禁用 */
  status: boolean
  /** 备注 */
  remark: string
  /** 创建时间 */
  createTime: string
  /** 修改时间 */
  modifyTime: string
}

/**
 * 获取留人陪护信息分页列表 - 响应数据
 */
export interface ResGetHospitalSitterServerByPage {
  /** 实体列表 */
  data: HospitalSitterServerItem[]
  /** 当前页 */
  pageIndex: number
  /** 每页记录数 */
  pageSize: number
  /** 总记录数 */
  recordCount: number
  /** 总页数 */
  pageCount: number
}

/**
 * 新增留人陪护信息 - 请求参数
 */
export interface ReqAddHospitalSitterServer {
  /** 住院科室 */
  department: string
  /** 服务名称 */
  serverName: string
  /** 价格 */
  price: number
  /** 总数量 */
  totalNumber: number
  /** 状态 true启用 false禁用 */
  status: boolean
  /** 备注 */
  remark?: string
}

/**
 * 修改留人陪护信息 - 请求参数
 */
export interface ReqUpdateHospitalSitterServer {
  /** ID */
  medicalHospitalSitterServerId: string
  /** 住院科室 */
  department: string
  /** 服务名称 */
  serverName: string
  /** 价格 */
  price: number
  /** 总数量 */
  totalNumber: number
  /** 状态 true启用 false禁用 */
  status: boolean
  /** 备注 */
  remark?: string
}

/**
 * 启用留人陪护信息 - 请求参数
 */
export interface ReqEnableHospitalSitterServer {
  /** ID */
  medicalHospitalSitterServerId: string
}

/**
 * 禁用留人陪护信息 - 请求参数
 */
export interface ReqDisableHospitalSitterServer {
  /** ID */
  medicalHospitalSitterServerId: string
}

/**
 * 删除留人陪护信息 - 请求参数
 */
export interface ReqDeleteHospitalSitterServer {
  /** ID */
  medicalHospitalSitterServerId: string
}

// ========= 服务订单管理 ==========
/**
 * 服务订单 - 状态
 */
export enum EscortOrderStatus {
  /* 已预约 */
  RESERVE = 0,
  /* 待归还 */
  RETURN = 1,
  /* 已完成 */
  FINISHED = 2,
  /* 已取消 */
  CANCEL = 3
}

// 服务订单 - 状态映射配置
export const EscortOrderStatusConfig = {
  [EscortOrderStatus.RESERVE]: { label: '已预约', tagType: 'success' },
  [EscortOrderStatus.RETURN]: { label: '待归还', tagType: 'warning' },
  [EscortOrderStatus.FINISHED]: { label: '已完成', tagType: 'info' },
  [EscortOrderStatus.CANCEL]: { label: '已取消', tagType: 'info' },
  // 默认配置
  default: { label: '-', tagType: 'info' }
}

/**
 * 服务订单 - 状态 可选项
 */
export const EscortOrderStatusOptions = Object.entries(EscortOrderStatusConfig)
  .filter(([key]) => key !== 'default')
  .map(([value, config]) => ({
    label: config.label,
    value: value
  }))

/**
 * 服务订单管理 - 分页获取列表 - 请求参数
 */
export interface ReqGetHospitalSitterByPage {
  /** 平台单号 */
  platformOrderNo?: string
  /** 住院人姓名 */
  patientName?: string
  /** 证件号 */
  patientIdNo?: string
  /** 状态 */
  orderStatus?: number
  /** 当前页 */
  page: number
  /** 每页的记录数 */
  rows: number
}

/**
 * 服务订单管理 - 分页获取列表 - 列表项
 */
export interface HospitalSitterItem {
  /** ID */
  hospitalSitterId: string
  /** 住院人姓名 */
  patientName: string
  /** 证件号 */
  patientIdNo: string
  /** 住院科室 */
  department: string
  /** 住院号 */
  patno: string
  /** 状态 */
  orderStatus: number
  /** 订单金额 */
  payFee: number
  /** 平台单号 */
  platformOrderNo: string
  /** 下单时间 */
  createTime: string
  /** 服务时间 */
  serverTime: string
}

/**
 * 服务订单管理 - 分页获取列表 - 响应数据
 */
export interface ResGetHospitalSitterByPage {
  /** 实体列表 */
  data: HospitalSitterItem[]
  /** 当前页 */
  pageIndex: number
  /** 每页记录数 */
  pageSize: number
  /** 总记录数 */
  recordCount: number
  /** 总页数 */
  pageCount: number
}

/**
 * 服务订单管理 - 获取详情 - 请求参数
 */
export interface ReqHospitalSitterInfo {
  /** ID */
  hospitalSitterId: string
}

/**
 * 支付状态
 */
export enum HospitalSitterInfoPayStatus {
  /* 待支付 */
  PENDING = 0,
  /* 支付成功 */
  SUCCESS = 1,
  /* 支付失败 */
  FAILED = 2,
  /* 支付异常 */
  EXCEPTION = 3
}

/**
 * 订单支付状态映射配置
 */
export const HospitalSitterInfoPayStatusConfig = {
  [HospitalSitterInfoPayStatus.PENDING]: { label: '待支付', tagType: '' },
  [HospitalSitterInfoPayStatus.SUCCESS]: { label: '支付成功', tagType: '' },
  [HospitalSitterInfoPayStatus.FAILED]: { label: '支付失败', tagType: '' },
  [HospitalSitterInfoPayStatus.EXCEPTION]: { label: '支付异常', tagType: '' },
  default: { label: '-', tagType: '' }
}

/**
 * 服务订单管理 - 获取详情 - 响应数据
 */
export interface ResHospitalSitterInfo {
  /** ID */
  hospitalSitterId: string
  /** 住院人姓名 */
  patientName: string
  /** 住院科室 */
  department: string
  /** 住院床号 */
  bedno: string
  /** 主治医生 */
  doctor: string
  /** 住院号 */
  patno: string
  /** 价格 */
  price: number
  /** 服务时间 */
  serverTime: string
  /** 状态 */
  orderStatus: number
  /** 联系人姓名 */
  contactName: string
  /** 联系人电话 */
  contactPhone: string
  /** 订单金额 */
  payFee: number
  /** 支付时间 */
  payTime: string
  /** 支付状态 */
  payStatus: number
  /** 业务类型 */
  businessType: string
  /** 支付流水号 */
  payTradeNo: string
  /** 平台单号 */
  platformOrderNo: string
  /** 证件号 */
  patientIdNo: string
  /** 服务名称 */
  serverName: string
  /** 入院时间 */
  indate: string
  /** 确定时间 */
  confirmTime?: string
  /** 完成时间 */
  completeTime?: string
  /** 取消时间 */
  cancelTime?: string
  /** 自提码 */
  selfPickupCode?: string
}

/**
 * 服务订单管理 - 确定租赁 - 请求参数
 */
export interface ReqConfirmService {
  /** ID */
  hospitalSitterId: string
  /** 自提码 */
  selfPickupCode: string
}

/**
 * 服务订单管理 - 确定归还 - 请求参数
 */
export interface ReqConfirmReturn {
  /** ID */
  hospitalSitterId: string
}
