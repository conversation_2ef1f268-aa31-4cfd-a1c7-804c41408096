import { defineStore } from 'pinia'
import type { AxiosRequestConfig, Canceler } from 'axios'
import { isString } from 'lodash-es'

import type { ElLoading } from 'element-plus'

type axiosQueue = {
  token: string
  callback?: Canceler
}

/**生成请求队列标识 */
const getAxiosQueueToken = (config: AxiosRequestConfig) => {
  return `${config.url}&${config.method}&${JSON.stringify(config.params)}&${
    isString(config.data) ? config.data : JSON.stringify(config.data)
  }`
}

export const useAxiosStore = defineStore('axiosStore', {
  state() {
    return {
      /**全局loading对象 */
      loading: null as ReturnType<typeof ElLoading.service> | null,
      /**请求队列 */
      queue: [] as axiosQueue[]
    }
  },

  actions: {
    /**设置请求队列 */
    addAxiosQueue({ config, callback }: { config: AxiosRequestConfig; callback: Canceler }) {
      const token = getAxiosQueueToken(config)
      const hasQueue = this.queue.find((item) => item.token == token)
      if (hasQueue) {
        callback()
      } else {
        this.queue.push({ token, callback })
      }
      return !!hasQueue
    },

    /**移除请求队列 */
    removeAxiosPending(config?: AxiosRequestConfig) {
      for (const index in this.queue) {
        if (config) {
          const token = getAxiosQueueToken(config)
          if (this.queue[index].token == token) {
            //当当前请求在数组中存在时执行函数体
            this.queue[index]?.callback!() //执行取消操作
            if (this.queue.length) {
              this.queue.splice(+index, 1) //把这条记录从数组中移除
            }
          }
        } else {
          this.queue[index]?.callback!() //执行取消操作
        }
      }
      if (!config) this.queue = []
    },

    /**设置全局loading对象 */
    setLoading(loading: ReturnType<typeof ElLoading.service>) {
      this.loading = loading as any
    },

    /**关闭loading */
    closeLoading() {
      this.loading && this.loading.close()
    }
  }
})
