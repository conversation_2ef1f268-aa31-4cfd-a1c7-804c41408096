<script lang="tsx" setup>
import { computed, ref, watch, h } from 'vue'
import { ElCheckbox, TableV2FixedDir, TableV2SortOrder } from 'element-plus'
import type { CheckboxValueType, SortBy } from 'element-plus'

/**
 * 通用虚拟表格组件
 * 基于 Element Plus 的 el-table-v2 封装，用于高效渲染大量数据
 */

const props = withDefaults(
  defineProps<{
    // 表格数据
    data: any[]
    // 表格高度
    height?: number | string
    // 表格宽度
    width?: number | string
    // 表格行的唯一标识字段
    rowKey: string
    // 表格列配置
    columns?: any[]
    // 是否显示表头
    showHeader?: boolean
    // 是否启用行选择
    selectable?: boolean
    // 是否固定表头
    fixed?: boolean
    // 行高
    rowHeight?: number
    // 表头行高
    headerHeight?: number
    // 是否允许多选
    multiple?: boolean
    // 当前选中行的值
    modelValue?: any[]
    // 是否显示网格边框
    showBorder?: boolean
    // 排序状态
    sortBy?: SortBy
  }>(),
  {
    height: 400,
    width: '100%',
    showHeader: true,
    selectable: false,
    fixed: true,
    rowHeight: 50,
    headerHeight: 50,
    multiple: true,
    modelValue: () => [],
    columns: () => [],
    showBorder: true,
    sortBy: () => ({ key: '', order: TableV2SortOrder.ASC })
  }
)

// 选中的行数据
const selectedRows = ref<any[]>(props.modelValue || [])

// 暴露选中行变化事件
const emit = defineEmits<{
  (e: 'update:modelValue', rows: any[]): void
  (e: 'selection-change', rows: any[]): void
  (e: 'column-sort', sortBy: SortBy): void
}>()

// 监听 modelValue 变化
watch(
  () => props.modelValue,
  (val) => {
    if (val) {
      selectedRows.value = val
    }
  },
  { deep: true }
)

// 处理行选择变化
const handleSelectionChange = (rows: any[]) => {
  selectedRows.value = rows
  emit('update:modelValue', rows)
  emit('selection-change', rows)
}

// 处理表格排序
const onSort = (sortBy: SortBy) => {
  emit('column-sort', sortBy)
}

// 处理列配置，支持插槽和固定列
const processedColumns = computed(() => {
  if (!props.selectable && props.columns && props.columns.length > 0) {
    return props.columns
  }

  const finalColumns = [...(props.columns || [])]

  // 如果启用了行选择，添加选择列
  if (props.selectable) {
    // 检查是否已经有选择列
    const hasSelectionColumn = finalColumns.some((col) => col.key === 'selection')

    if (!hasSelectionColumn) {
      finalColumns.unshift({
        key: 'selection',
        width: 55,
        fixed: TableV2FixedDir.LEFT,
        cellRenderer: ({ rowData }: any) => {
          const isSelected = selectedRows.value.some((item) => item[props.rowKey] === rowData[props.rowKey])

          return h(ElCheckbox, {
            modelValue: isSelected,
            class: 'selection-checkbox',
            onChange: (value: CheckboxValueType) => {
              const index = selectedRows.value.findIndex((item) => item[props.rowKey] === rowData[props.rowKey])
              if (index > -1) {
                if (value === false) {
                  selectedRows.value.splice(index, 1)
                }
              } else {
                if (value === true) {
                  if (!props.multiple) {
                    selectedRows.value = []
                  }
                  selectedRows.value.push(rowData)
                }
              }
              handleSelectionChange(selectedRows.value)
            }
          })
        },
        headerCellRenderer: () => {
          const allSelected = props.data.length > 0 && selectedRows.value.length === props.data.length
          const indeterminate = selectedRows.value.length > 0 && selectedRows.value.length < props.data.length

          return h(ElCheckbox, {
            modelValue: allSelected,
            indeterminate: indeterminate,
            onChange: (value: CheckboxValueType) => {
              if (value) {
                selectedRows.value = [...props.data]
              } else {
                selectedRows.value = []
              }
              handleSelectionChange(selectedRows.value)
            }
          })
        }
      })
    }
  }

  return finalColumns
})

// 计算表格实际高度
const tableHeight = computed(() => {
  if (typeof props.height === 'string') {
    if (props.height.endsWith('px')) {
      return parseInt(props.height)
    }
    return 400 // 默认高度
  }
  return props.height
})

// 计算表格实际宽度
const tableWidth = computed(() => {
  if (typeof props.width === 'string') {
    if (props.width.endsWith('px')) {
      return parseInt(props.width)
    }
    return window.innerWidth // 如果是百分比或其他单位，使用窗口宽度
  }
  return props.width
})

// 表格类名
const tableClass = computed(() => {
  return props.showBorder ? 'with-border' : ''
})
</script>

<template>
  <div class="common-virtual-table">
    <el-table-v2
      :columns="processedColumns"
      :data="data"
      :width="Number(tableWidth)"
      :height="Number(tableHeight)"
      :row-key="rowKey"
      :fixed="fixed"
      :row-height="rowHeight"
      :header-height="headerHeight"
      :header-class="showHeader ? '' : 'hidden-header'"
      :sort-by="sortBy"
      :class="tableClass"
      @column-sort="onSort"
      @row-click="
        (row: any) => {
          if (selectable && !processedColumns.some(col => col.key === 'selection')) {
            const index = selectedRows.findIndex((item) => item[rowKey] === row[rowKey])
            if (index > -1) {
              if (multiple) {
                selectedRows.splice(index, 1)
              } else {
                selectedRows.length = 0
              }
            } else {
              if (!multiple) {
                selectedRows.length = 0
              }
              selectedRows.push(row)
            }
            handleSelectionChange(selectedRows)
          }
        }
      "
    />
  </div>
</template>

<style scoped lang="scss">
.common-virtual-table {
  --border-color: #dfe6ec;

  :deep(.el-table-v2) {
    // 表格样式调整
    --el-table-border-color: var(--border-color);
    --el-table-header-bg-color: #f5f7fa;
    --el-table-row-hover-bg-color: #f5f7fa;

    .el-table-v2__header-row {
      font-weight: bold;
      background-color: var(--el-table-header-bg-color);

      .el-table-v2__header-cell[data-key='selection'] {
        justify-content: center;
      }

      .el-table-v2__header-cell {
        justify-content: center;
      }
    }

    .el-table-v2__row {
      &:hover {
        background-color: var(--el-table-row-hover-bg-color);
      }

      &.selected {
        background-color: #ecf5ff;
      }

      .el-table-v2__row-cell .selection-checkbox {
        margin: 0 auto;
      }
    }

    &.with-border {
      .el-table-v2__header-cell,
      .el-table-v2__row-cell {
        border-right: 1px solid var(--border-color);
      }

      .el-table-v2__header {
        border-top: 1px solid var(--border-color);
        border-bottom: 1px solid var(--border-color);
      }

      .el-table-v2__header-row {
        .el-table-v2__header-cell:first-child {
          border-left: 1px solid var(--border-color);
        }
      }

      .el-table-v2__row {
        .el-table-v2__row-cell:first-child {
          border-left: 1px solid var(--border-color);
        }
      }
    }
  }

  :deep(.hidden-header) {
    display: none;
  }
}
</style>
