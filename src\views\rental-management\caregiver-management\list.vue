<script setup lang="ts">
/**
 * 陪护人租赁管理 - 陪护人管理
 */
import {
  ReqAddInpatientCaregiverPerson,
  ReqGetInpatientCaregiverPersonByPage,
  ReqInpatientCaregiverPersonId,
  ReqUpdateInpatientCaregiverPerson,
  ResInpatientCaregiverPersonItem
} from '@/api/dto/rental-management/caregiver-management.dto.ts'
import {
  requestAddInpatientCaregiverPerson,
  requestDeleteInpatientCaregiverPerson,
  requestDisabledInpatientCaregiverPerson,
  requestEnabledInpatientCaregiverPerson,
  requestGetInpatientCaregiverPersonByPage,
  requestUpdateInpatientCaregiverPerson
} from '@/api/rental-management.api.ts'
import { appMessage } from '@/hooks/useNaiveApi.ts'
import CaregiverList from '@/views/rental-management/caregiver-management/components/caregiver-list.vue'

// 表格数据
const infoData = reactive({
  listData: [] as ResInpatientCaregiverPersonItem[],
  total: 0
})

const caregiverListRef = ref<InstanceType<typeof CaregiverList>>()

// 搜索
async function handleSearchData(e: {
  currentPage: number
  pageSize: number
  searchData: ReqGetInpatientCaregiverPersonByPage
}) {
  try {
    const params: ReqGetInpatientCaregiverPersonByPage = {
      ...e.searchData,
      page: e.currentPage,
      rows: e.pageSize
    }

    const { data, recordCount } = await requestGetInpatientCaregiverPersonByPage(params)

    infoData.listData = data
    infoData.total = recordCount
  } catch (error) {
    console.error('获取陪护人列表失败：', error)
    appMessage.error('获取陪护人列表失败')
    infoData.listData = []
    infoData.total = 0
  }
}

// 新增
async function handleAddInfo(data: ReqAddInpatientCaregiverPerson, resolve: () => void, reject: () => void) {
  try {
    const { msg } = await requestAddInpatientCaregiverPerson(data)

    resolve()
    appMessage.success(msg || '新增陪护人成功')

    // 刷新列表
    caregiverListRef.value?.handleSearch()
  } catch (error) {
    console.error('新增陪护人失败：', error)
    reject()
    appMessage.error('新增陪护人失败')
  }
}

// 编辑
async function handleEditInfo(data: ReqUpdateInpatientCaregiverPerson, resolve: () => void, reject: () => void) {
  try {
    const { msg } = await requestUpdateInpatientCaregiverPerson(data)

    resolve()
    appMessage.success(msg || '修改陪护人成功')

    // 刷新列表
    caregiverListRef.value?.handleSearch()
  } catch (error) {
    console.error('编辑陪护人失败：', error)
    reject()
    appMessage.error('编辑陪护人失败')
  }
}

// 启用/禁用
async function handleChangeInfoStatus(e: { inpatientCaregiverPersonId: string; status: boolean }) {
  try {
    const params: ReqInpatientCaregiverPersonId = {
      inpatientCaregiverPersonId: e.inpatientCaregiverPersonId
    }

    let result
    if (e.status) {
      result = await requestEnabledInpatientCaregiverPerson(params)
    } else {
      result = await requestDisabledInpatientCaregiverPerson(params)
    }

    appMessage.success(result.msg || (e.status ? '启用成功' : '禁用成功'))

    // 刷新列表
    caregiverListRef.value?.handleSearch()
  } catch (error) {
    console.error('修改陪护人状态失败：', error)
    appMessage.error('修改陪护人状态失败')
  }
}

// 删除
async function handleDeleteInfo(e: ResInpatientCaregiverPersonItem) {
  try {
    const { msg } = await requestDeleteInpatientCaregiverPerson({
      inpatientCaregiverPersonId: e.inpatientCaregiverPersonId
    })

    appMessage.success(msg || '删除陪护人成功')

    // 刷新列表
    caregiverListRef.value?.handleSearch()
  } catch (error) {
    console.error('删除陪护人失败：', error)
    appMessage.error('删除陪护人失败')
  }
}
</script>

<template>
  <CaregiverList
    ref="caregiverListRef"
    class="container"
    :list-data="infoData.listData"
    :total="infoData.total"
    @search="handleSearchData"
    @add="handleAddInfo"
    @edit="handleEditInfo"
    @changeStatus="handleChangeInfoStatus"
    @delete="handleDeleteInfo"
  />
</template>

<style scoped lang="scss">
.container {
  padding: 30px 20px;
}
</style>
