<script setup lang="ts">
import {
  BankCardRefundAuditStatus,
  BankCardRefundAuditStatusConfig,
  ResBankCardRefundRecordInfo
} from '@/api/dto/discharge-settlement-refunds/bank-card-refund-records.dto.ts'
import CirculationInfo from '@/views/discharge-settlement-refunds/bank-card-refund-records/detail/components/circulation-info.vue'

defineProps<{
  detailInfo: ResBankCardRefundRecordInfo
}>()

defineEmits<{
  (e: 'audit'): void
}>()
</script>

<template>
  <div class="content-wrapper">
    <div class="content">
      <div class="title">
        <span>住院信息</span>
        <div v-if="detailInfo.status === BankCardRefundAuditStatus.PENDING">
          <el-button type="primary" size="large" @click="$emit('audit')"> 结算审核 </el-button>
        </div>
      </div>
      <el-descriptions border :column="2">
        <el-descriptions-item label="住院人姓名">{{ detailInfo.patientName }}</el-descriptions-item>
        <el-descriptions-item label="住院号">{{ detailInfo.patientNo }}</el-descriptions-item>
        <el-descriptions-item label="住院科室">{{ detailInfo.patientDept }}</el-descriptions-item>
        <el-descriptions-item label="主治医生">{{ detailInfo.doctorName }}</el-descriptions-item>
        <el-descriptions-item label="住院床号">{{ detailInfo.bedNo }}</el-descriptions-item>
        <el-descriptions-item label="入院时间">{{ detailInfo.patientInTime }}</el-descriptions-item>
        <el-descriptions-item label="出院时间">{{ detailInfo.patientOutTime }}</el-descriptions-item>
        <el-descriptions-item label="费用总金额（元）">{{ detailInfo.totalAmount }}</el-descriptions-item>
        <el-descriptions-item label="医保金额（元）">{{ detailInfo.medicalInsuranceAmount }}</el-descriptions-item>
        <el-descriptions-item label="自费金额">{{ detailInfo.selfPayAmount }}</el-descriptions-item>
        <el-descriptions-item label="已交押金金额（元）">{{ detailInfo.depositAmount }}</el-descriptions-item>
        <el-descriptions-item label="结算退费金额（元）">{{ detailInfo.refundAmount }}</el-descriptions-item>
      </el-descriptions>
    </div>

    <div class="content">
      <div class="title">
        <span>结算信息</span>
      </div>

      <el-descriptions border :column="2">
        <el-descriptions-item label="开户名">{{ detailInfo.accountName }}</el-descriptions-item>
        <el-descriptions-item label="开户行">{{ detailInfo.accountBank }}</el-descriptions-item>
        <el-descriptions-item label="银行卡号">{{ detailInfo.accountCardNo }}</el-descriptions-item>
        <el-descriptions-item label="预留号码">{{ detailInfo.accountPhone }}</el-descriptions-item>
        <el-descriptions-item label="状态">
          <el-tag
            :type="
              BankCardRefundAuditStatusConfig[detailInfo.status]?.tagType ||
              BankCardRefundAuditStatusConfig.default.tagType
            "
          >
            {{
              BankCardRefundAuditStatusConfig[detailInfo.status]?.label || BankCardRefundAuditStatusConfig.default.label
            }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="创建时间">{{ detailInfo.createTime }}</el-descriptions-item>
      </el-descriptions>
    </div>

    <div class="content">
      <div class="title">
        <span>流转信息</span>
      </div>
      <CirculationInfo :circulation-info="detailInfo.resCirculationInfoDTOList ?? []" />
    </div>
  </div>
</template>

<style scoped lang="scss">
.content {
  & .title {
    margin: 30px 0 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 16px;
  }

  &-wrapper {
    padding: 0 20px 30px;
    display: flex;
    flex-direction: column;
    flex: 1;
    overflow: auto;
  }
}
</style>
