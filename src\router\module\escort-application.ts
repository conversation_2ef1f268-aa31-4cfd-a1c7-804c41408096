import { ROUTER_PATH } from '@/router/router-path.ts'
import type { RouteRecordRaw } from 'vue-router'

const Layout = () => import('@/layout')

export const escortManagementRouter: RouteRecordRaw = {
  path: ROUTER_PATH.ESCORT_MANAGEMENT,
  name: 'EscortManagement',
  redirect: ROUTER_PATH.ESCORT_INFO,
  component: Layout,
  meta: { title: '留人陪护申请管理', icon: 'el-icon-moon-night' },
  children: [
    // 根级路由从动态路由接口获取
    // {
    //   path: ROUTER_PATH.ESCORT_INFO,
    //   name: 'EscortInfo',
    //   component: () => import('@/views/escort-management/info'),
    //   meta: { title: '留人陪护信息管理' }
    // },
    // {
    //   path: ROUTER_PATH.ESCORT_ORDERS,
    //   name: 'EscortOrders',
    //   component: () => import('@/views/escort-management/orders'),
    //   meta: { title: '服务订单管理' }
    // },
    {
      path: ROUTER_PATH.ESCORT_ORDER_DETAIL,
      name: 'EscortOrderDetail',
      component: () => import('@/views/escort-management/order-detail'),
      hidden: true,
      meta: {
        title: '服务订单详情',
        leaveOff: true,
        activeMenu: ROUTER_PATH.ESCORT_ORDERS,
        noCache: true,
        permissionFrom: ROUTER_PATH.ESCORT_ORDERS,
        useTab: false
      }
    }
  ]
}
