/**
 * 入院登记状态
 */
export enum AdmissionRegistrationStatus {
  /** 已提交登记 */
  SUBMITTED = '1',
  /* 已提交登记并完成入院办理，只有这个状态才显示“接受处理” */
  COMPLETED = '2',
  /** 已处理 */
  PROCESSED = '3'
}

// 入院登记状态可选项
export const admissionRegistrationStatusOptions = [
  { label: '已提交登记', value: AdmissionRegistrationStatus.SUBMITTED },
  { label: '已提交登记并完成入院办理', value: AdmissionRegistrationStatus.COMPLETED },
  { label: '已处理', value: AdmissionRegistrationStatus.PROCESSED }
]

/**
 * 性别选项
 */
export enum GenderType {
  MALE = '男',
  FEMALE = '女'
}


/**
 * 医保类型 适用于 el-cascader 级联选择
 */
export const insuranceTypeCascaderOptions = [
  {
    value: '职工基本医疗保险',
    label: '职工基本医疗保险',
    children: [
      { value: '佛山医保', label: '佛山医保' },
      { value: '省内异地医保', label: '省内异地医保' },
      { value: '跨省异地医保', label: '跨省异地医保' }
    ]
  },
  {
    value: '城乡居民基本医疗保险',
    label: '城乡居民基本医疗保险',
    children: [
      { value: '佛山医保', label: '佛山医保' },
      { value: '省内异地医保', label: '省内异地医保' },
      { value: '跨省异地医保', label: '跨省异地医保' }
    ]
  },
  {
    value: '工伤',
    label: '工伤',
    children: [
      { value: '佛山工伤', label: '佛山工伤' },
      { value: '省内异地工伤', label: '省内异地工伤' },
      { value: '跨省异地工伤', label: '跨省异地工伤' },
      { value: '工伤康复', label: '工伤康复' }
    ]
  },
  {
    value: '佛山离休',
    label: '佛山离休',
    children: [
      { value: '市直', label: '市直' },
      { value: '禅城', label: '禅城' },
      { value: '南海', label: '南海' },
      { value: '高明', label: '高明' },
      { value: '三水', label: '三水' },
      { value: '顺德', label: '顺德' }
    ]
  },
  {
    value: '自费',
    label: '自费',
    children: [
      { value: '全自费', label: '全自费' },
      { value: '由于交通事故、工伤、其他意外不报基本医疗保险', label: '由于交通事故、工伤、其他意外不报基本医疗保险' }
    ]
  },
  {
    value: '其他',
    label: '其他',
    children: [
      { value: '省公医', label: '省公医' },
      { value: '贫困救助', label: '贫困救助' },
      { value: '商业医保', label: '商业医保' },
      { value: '全公费', label: '全公费' },
      { value: '其他社保', label: '其他社保' }
    ]
  }
]

/**
 * 民族选项
 */
export const nationOptions = [
  '汉族',
  '蒙古族',
  '回族',
  '藏族',
  '维吾尔族',
  '苗族',
  '彝族',
  '壮族',
  '布依族',
  '朝鲜族',
  '满族',
  '侗族',
  '瑶族',
  '白族',
  '土家族',
  '哈尼族',
  '哈萨克族',
  '傣族',
  '黎族',
  '傈僳族',
  '佤族',
  '畲族',
  '高山族',
  '拉祜族',
  '水族',
  '东乡族',
  '纳西族',
  '景颇族',
  '柯尔克孜族',
  '土族',
  '达斡尔族',
  '仫佬族',
  '羌族',
  '布朗族',
  '撒拉族',
  '毛南族',
  '仡佬族',
  '锡伯族',
  '阿昌族',
  '普米族',
  '塔吉克族',
  '怒族',
  '乌孜别克族',
  '俄罗斯族',
  '鄂温克族',
  '德昂族',
  '保安族',
  '裕固族',
  '京族',
  '塔塔尔族',
  '独龙族',
  '鄂伦春族',
  '赫哲族',
  '门巴族',
  '珞巴族',
  '基诺族',
  '其他'
] as const

/**
 * 婚姻状态选项
 */
export enum MaritalStatus {
  MARRIED = '已婚',
  UNMARRIED = '未婚',
  FIRST_MARRIAGE = '初婚',
  REMARRIED = '再婚',
  REMARRIAGE = '复婚',
  WIDOWED = '丧偶',
  DIVORCED = '离婚',
  UNKNOWN = '未说明的婚姻状况'
}

/**
 * 职业类型选项
 */
export const jobOptions = [
  '国家公务员',
  '专业技术人员',
  '职员',
  '企业管理人员',
  '工人',
  '农民',
  '学生',
  '现役军人',
  '自由职业者',
  '个体经营者',
  '无业人员',
  '退(离)休人员',
  '其他'
] as const

/**
 * 关系类型选项
 */
export const relationOptions = [
  '本人或户主',
  '配偶',
  '子',
  '女',
  '(外)孙子女',
  '父母',
  '(外)祖父母',
  '兄弟姐妹',
  '其他'
] as const


/**
 * 入院途径选项
 */
export const admissionWayOptions = [
  {
    value: '1',
    label: '急诊'
  },
  {
    value: '2',
    label: '门诊'
  },
  {
    value: '3',
    label: '其他医疗机构转入'
  },
  {
    value: '9',
    label: '其他'
  }
]
