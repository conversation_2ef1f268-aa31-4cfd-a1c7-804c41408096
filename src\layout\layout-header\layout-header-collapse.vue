<script lang="ts" setup>
import { useLayoutStore } from '@/stores/layout.store'
import { storeToRefs } from 'pinia'

const { menuIsCollapse } = storeToRefs(useLayoutStore())

const handleMenuCollapse = () => {
  menuIsCollapse.value = !menuIsCollapse.value
}
</script>

<template>
  <XyzTransition :xyz="`fade ${menuIsCollapse ? 'left' : 'right'}`">
    <svg-icon
      v-if="menuIsCollapse"
      name="svg-menu-expand"
      class="collapse-container"
      @click="handleMenuCollapse"
    ></svg-icon>
    <svg-icon v-else name="svg-menu-fold" class="collapse-container" @click="handleMenuCollapse"></svg-icon>
  </XyzTransition>
</template>

<style lang="scss" scoped>
.collapse-container {
  flex-shrink: 0;
  font-size: 24px;
  color: var(--el-text-color-primary);
  cursor: pointer;
  &.xyz-out {
    position: absolute;
  }
}
</style>
