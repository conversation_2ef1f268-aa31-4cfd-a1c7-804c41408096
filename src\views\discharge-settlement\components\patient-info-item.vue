<template>
  <div class="item">
    <div v-if="!hideLabel" class="label">{{ label }}：</div>
    <el-tooltip :content="contentValue" placement="top" :visible="tooltipVisible" manual>
      <div
        class="value"
        :class="{ emphasize: emphasize }"
        :style="{ minWidth: minWidth + 'px', textAlign: valueAlign }"
        @mouseenter="checkOverflow"
        @mouseleave="tooltipVisible = false"
      >
        <div ref="valueRef" class="value-content">
          {{ displayValue ?? '-' }}
        </div>

        <span v-if="suffix" class="value-suffix">{{ suffix }}</span>
      </div>
    </el-tooltip>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'

const props = defineProps({
  // 名称
  label: {
    type: String,
    required: true
  },
  // 不显示 label
  hideLabel: {
    type: Boolean,
    default: false
  },
  // 值
  value: {
    type: [String, Number],
    required: false
  },
  // 值最小宽度
  minWidth: {
    type: Number,
    default: 0
  },
  // 值对齐方式
  valueAlign: {
    type: String as PropType<'left' | 'right'>,
    default: 'left'
  },
  // 是否强调
  emphasize: {
    type: Boolean,
    default: false
  },
  // 值后缀
  suffix: {
    type: String,
    required: false
  }
})

// 计算用于显示和工具提示的值
const displayValue = computed(() => props.value)
const contentValue = computed(() => (typeof props.value === 'number' ? props.value.toString() : props.value))

// 值的元素引用
const valueRef = ref<HTMLElement | null>(null)
// 控制工具提示显示与否
const tooltipVisible = ref(false)

// 检查元素是否溢出，并根据结果显示或隐藏工具提示
const checkOverflow = async () => {
  if (valueRef.value) {
    // 比较实际内容宽度与可见宽度
    tooltipVisible.value = valueRef.value.scrollWidth > valueRef.value.clientWidth
  }
}
</script>

<style scoped lang="scss">
.item {
  display: flex;
  align-items: center;
  font-size: 14px;

  .label {
    flex-shrink: 0;
  }

  .value {
    min-height: 34px;
    border-radius: 4px;
    background: #fafafa;
    border: 1px solid #dcdfe6;
    flex: 1;
    display: flex;
    align-items: center;
    box-sizing: border-box;
    cursor: default; // 为所有值添加默认鼠标样式

    // 当鼠标悬停且内容溢出时，显示"指针"鼠标样式
    &:hover {
      &::after {
        content: '';
        display: block;
      }
    }

    .value-content {
      padding: 6px 10px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      box-sizing: border-box;
      flex: 1;
    }

    .value-suffix {
      width: 32px;
      border-left: 1px solid #dcdfe6;
      height: 34px;
      line-height: 34px;
      text-align: center;
      background: #f5f7fa;
    }
  }

  .emphasize {
    color: #e10000;
  }
}
</style>
