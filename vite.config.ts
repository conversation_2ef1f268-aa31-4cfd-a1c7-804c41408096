import { defineConfig, loadEnv } from 'vite'
import node from 'node:process'
import injectPlugins from './vite/plugins/index'
import injectResolve from './vite/resolve/index'
import injectCss from './vite/css/index'
import injectServer from './vite/server/index'
import injectBuild from './vite/build/index'
import injectEsBuild from './vite/esbuild/index'
import { accessSync, readdirSync } from 'fs'

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => {
  const optimizeDepsIncludes: string[] = []
  const env = loadEnv(mode, node.cwd())
  const isDevMode = mode === 'development'

  if (isDevMode) {
    // 预加载element-plus
    readdirSync('node_modules/element-plus/es/components').forEach((dirname) => {
      try {
        // 同步检查文件是否存在，如果存在则添加到优化列表。这里不能使用异步，否则不会生效
        accessSync(`node_modules/element-plus/es/components/${dirname}/style/css.mjs`)
        optimizeDepsIncludes.push(`element-plus/es/components/${dirname}/style/css`)
        optimizeDepsIncludes.push(`element-plus/es/components/${dirname}/style/index`)
      } catch (e) {
        // 文件不存在，跳过
      }
    })
  }

  return {
    root: process.cwd(),

    base: './',

    plugins: injectPlugins(env),

    resolve: injectResolve(),

    css: injectCss(),

    server: injectServer(),

    build: injectBuild(),

    esbuild: injectEsBuild(isDevMode),

    optimizeDeps: {
      include: optimizeDepsIncludes,
      // 依赖构建 https://vitejs.cn/guide/dep-pre-bundling.html#the-why
      // 在开发模式下，设置为false可以减少不必要的重新构建
      force: isDevMode ? false : true
    },

    define: {
      __VUE_PROD_HYDRATION_MISMATCH_DETAILS__: 'true'
    }
  }
})
