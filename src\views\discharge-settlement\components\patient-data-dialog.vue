<script setup lang="ts">
import type { PatientAccountsBaseInfo } from '@/api/dto/discharge-settlement/patient-accounts.dto'
import PatientInfoItem from './patient-info-item.vue'

defineProps<{
  /** 病人信息 */
  patientInfo: PatientAccountsBaseInfo
}>()
</script>

<template>
  <el-dialog top="4vh" title="病人资料" width="1300px">
    <div class="patient-data-content">
      <el-row :gutter="32">
        <el-col :span="6">
          <PatientInfoItem label="住院号" :value="patientInfo.patno" />
        </el-col>
        <el-col :span="6">
          <PatientInfoItem label="记账号" :value="patientInfo.inpno" />
        </el-col>
        <el-col :span="6">
          <PatientInfoItem label="病区" :value="patientInfo.curdptnm" />
        </el-col>
        <el-col :span="6">
          <!-- TODO 待补充 -->
          <PatientInfoItem label="主管医生" value="" />
        </el-col>
      </el-row>

      <el-divider border-style="dashed" />

      <el-row :gutter="32">
        <el-col :span="6">
          <PatientInfoItem label="姓名" :value="patientInfo.name" />
        </el-col>
        <el-col :span="12" class="flex-row">
          <PatientInfoItem label="性别" :value="patientInfo.sex === '1' ? '男' : patientInfo.sex === '2' ? '女' : ''" />
          <PatientInfoItem label="生日" :value="patientInfo.birthday" />
          <PatientInfoItem label="年龄" :value="patientInfo.age" />
          <PatientInfoItem label="婚姻" :value="patientInfo.marry" />
        </el-col>
        <el-col :span="6">
          <!-- TODO 待补充 -->
          <PatientInfoItem label="地域" value="" />
        </el-col>
      </el-row>

      <el-row :gutter="32">
        <el-col :span="6">
          <!-- TODO 待补充 -->
          <PatientInfoItem label="职业" value="" />
        </el-col>
        <el-col :span="6">
          <PatientInfoItem label="生地" :value="patientInfo.addr" />
        </el-col>
        <el-col :span="6">
          <PatientInfoItem label="民族" :value="patientInfo.caty" />
        </el-col>
        <el-col :span="6">
          <PatientInfoItem label="国籍" :value="patientInfo.naty" />
        </el-col>
      </el-row>

      <el-row :gutter="32">
        <el-col :span="10">
          <!-- TODO 待补充 -->
          <PatientInfoItem label="单位" value="" />
        </el-col>
        <el-col :span="14">
          <!-- TODO 待补充 -->
          <PatientInfoItem label="地址" value="" />
        </el-col>
      </el-row>

      <el-row :gutter="32">
        <el-col :span="6">
          <PatientInfoItem label="电话" :value="patientInfo.phone" />
        </el-col>
        <el-col :span="4">
          <!-- TODO 待补充 -->
          <PatientInfoItem label="邮编" value="" />
        </el-col>
        <el-col :span="14">
          <!-- TODO 待补充 -->
          <PatientInfoItem label="户址" value="" />
        </el-col>
      </el-row>

      <el-row :gutter="32">
        <el-col :span="10">
          <PatientInfoItem label="身份证" :value="patientInfo.certno" />
        </el-col>
        <el-col :span="8">
          <PatientInfoItem label="电话" :value="patientInfo.contactPhone" />
        </el-col>
        <el-col :span="6">
          <PatientInfoItem label="邮编" :value="patientInfo.contactPostalCode" />
        </el-col>
      </el-row>

      <el-divider border-style="dashed" />

      <el-row :gutter="32">
        <el-col :span="6">
          <PatientInfoItem label="联系人" :value="patientInfo.contact" />
        </el-col>
        <el-col :span="4">
          <PatientInfoItem label="关系" :value="patientInfo.relation" />
        </el-col>
        <el-col :span="14">
          <!-- TODO 待补充 -->
          <PatientInfoItem label="地址" value="" />
        </el-col>
      </el-row>

      <el-row :gutter="32">
        <el-col :span="10">
          <PatientInfoItem label="电话" :value="patientInfo.contel" />
        </el-col>
        <el-col :span="6">
          <!-- TODO 待补充 -->
          <PatientInfoItem label="邮编" value="" />
        </el-col>
      </el-row>

      <el-divider border-style="dashed" />
      <el-row :gutter="32">
        <el-col :span="5">
          <PatientInfoItem label="入院科室" :value="patientInfo.indptnm" />
        </el-col>
        <el-col :span="5">
          <PatientInfoItem label="入院日期" :value="patientInfo.indate" />
        </el-col>
        <el-col :span="14" class="flex-row">
          <!-- TODO 待补充 -->
          <PatientInfoItem label="入院情况" value="" :min-width="120" />
          <!-- TODO 待补充 -->
          <PatientInfoItem label="入院途径" value="" :min-width="120" />
          <!-- TODO 待补充 -->
          <PatientInfoItem label="门诊医生" value="" :min-width="120" />
        </el-col>
      </el-row>

      <el-row :gutter="32">
        <el-col :span="10">
          <!-- TODO 待补充 -->
          <PatientInfoItem label="门诊诊断" value="" />
        </el-col>
        <el-col :span="14">
          <!-- TODO 待补充 -->
          <PatientInfoItem label="入院诊断" value="" />
        </el-col>
      </el-row>

      <el-row :gutter="32">
        <el-col :span="6">
          <PatientInfoItem label="结算方式" :value="patientInfo.setmeth" />
        </el-col>
        <el-col :span="6">
          <!-- TODO 待补充 -->
          <PatientInfoItem label="医疗证号" value="" />
        </el-col>
        <el-col :span="5">
          <PatientInfoItem label="自付%" :value="patientInfo.selfFundedPercentage" suffix="%" />
        </el-col>
        <el-col :span="7">
          <!-- TODO 待补充 -->
          <PatientInfoItem label="医保就医登记号" value="" />
        </el-col>
      </el-row>

      <el-row :gutter="32">
        <el-col :span="5">
          <!-- TODO 待补充 -->
          <PatientInfoItem label="医保号" value="" />
        </el-col>
        <el-col :span="7">
          <!-- TODO 待补充 -->
          <PatientInfoItem label="统筹基金累计已支付" value="" />
        </el-col>
        <el-col :span="5">
          <!-- TODO 待补充 -->
          <PatientInfoItem label="起付标准" value="" />
        </el-col>
        <el-col :span="7">
          <!-- TODO 待补充 -->
          <PatientInfoItem label="医保住院次数" value="" suffix="次" />
        </el-col>
      </el-row>

      <el-row :gutter="32">
        <el-col :span="6">
          <!-- TODO 待补充 -->
          <PatientInfoItem label="医保定额" value="" />
        </el-col>
        <el-col :span="6">
          <!-- TODO 待补充 -->
          <PatientInfoItem label="超标普通" value="" />
        </el-col>
        <el-col :span="6">
          <!-- TODO 待补充 -->
          <PatientInfoItem label="床位标准" value="" />
        </el-col>
        <el-col :span="6">
          <PatientInfoItem label="入院床位" :value="patientInfo.bedno" />
        </el-col>
      </el-row>

      <el-row :gutter="32">
        <el-col :span="5">
          <!-- TODO 待补充 -->
          <PatientInfoItem label="住院标志" value="" />
        </el-col>
        <el-col :span="5">
          <PatientInfoItem label="出院日期" :value="patientInfo.outdate" />
        </el-col>
        <el-col :span="14">
          <!-- TODO 待补充 -->
          <PatientInfoItem label="出院诊断" value="" />
        </el-col>
      </el-row>

      <el-row :gutter="32">
        <el-col :span="5">
          <!-- TODO 待补充 -->
          <PatientInfoItem label="医院名称" value="" />
        </el-col>
        <el-col :span="5">
          <!-- TODO 待补充 -->
          <PatientInfoItem label="医院名称" value="" />
        </el-col>
        <el-col :span="14" class="flex-row">
          <!-- TODO 待补充 -->
          <PatientInfoItem label="医院名称" value="" :min-width="126" />
          <!-- TODO 待补充 -->
          <PatientInfoItem label="医院名称" value="" :min-width="126" />
          <!-- TODO 待补充 -->
          <PatientInfoItem label="医院名称" value="" :min-width="126" />
        </el-col>
      </el-row>

      <el-divider border-style="dashed" />

      <el-row :gutter="32">
        <el-col :span="10" class="flex-row">
          <!-- TODO 待补充 -->
          <PatientInfoItem label="登记员号" value="" :min-width="130" />
          <!-- TODO 待补充 -->
          <PatientInfoItem hide-label label="" value="" :min-width="222" />
        </el-col>
        <el-col :span="6">
          <!-- TODO 待补充 -->
          <PatientInfoItem label="登记日期" value="" />
        </el-col>
      </el-row>

      <el-divider border-style="dashed" />

      <el-row :gutter="32">
        <el-col :span="24">
          <!-- TODO 待补充 -->
          <PatientInfoItem class="special-notes" label="特注" value="" />
        </el-col>
      </el-row>

      <el-row :gutter="32">
        <el-col :span="8">
          <!-- TODO 待补充 -->
          <PatientInfoItem label="总预交人民币" value="" value-align="right" />
        </el-col>
        <el-col :span="8">
          <!-- TODO 待补充 -->
          <PatientInfoItem label="剩余预交数" value="" value-align="right" />
        </el-col>
        <el-col :span="8">
          <PatientInfoItem label="欠款" :value="patientInfo.debt" value-align="right" />
        </el-col>
      </el-row>
    </div>
  </el-dialog>
</template>

<style scoped lang="scss">
.patient-data-content {
  display: flex;
  flex-direction: column;
  gap: 10px;
  height: 80vh;
  overflow-y: auto;
  overflow-x: hidden;

  .flex-row {
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: 20px;
    justify-content: space-between;
  }

  :deep(.el-divider.el-divider--horizontal) {
    margin: 0;
  }

  .special-notes {
    :deep(.label) {
      color: #0007db !important;
    }
  }
}
</style>
