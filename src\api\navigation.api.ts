import { request } from '@/utils/axios-utils'
import type { ApiFunc } from 'axios'
import type { GetNavigationMenuTreeListInput, GetNavigationMenuTreeListOutput } from './dto/navigation.dto'

/**获取管理页面左则导航菜单 */
export const getRouter_C: ApiFunc<GetNavigationMenuTreeListInput, GetNavigationMenuTreeListOutput> = (
  params,
  options
) => {
  return request({
    url: '/foying/web/system/Navigation/GetNavigationMenuTreeList',
    method: 'get',
    params,
    ...options,
  })
}

export const getRouter_Java: ApiFunc<undefined, GetNavigationMenuTreeListOutput> = (options) => {
  return request({
    url: '/foying/web/admin/SysNavigation/getLeftSysNavigationForShow',
    ...options,
  })
}
