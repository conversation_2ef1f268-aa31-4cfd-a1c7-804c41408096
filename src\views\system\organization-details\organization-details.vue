<script lang="ts" setup>
import type { RightSysOrganizationModel, AddSysOrganizationInput } from '@/api/dto/system/organization.dto'
import {
  addSysOrganizationApi,
  getRightSysOrganizationForShowApi,
  getSysOrganizationForUpdateApi,
  updateSysOrganizationApi,
} from '@/api/system/organization.api'
import { appMessage } from '@/hooks/useNaiveApi'
import { ROUTER_PATH } from '@/router/router-path'
import type { Tree } from '@/types/tree'
import { formatTreeData } from '@/utils'
import type { CascaderOption, FormRules } from 'element-plus'

export type FormData = AddSysOrganizationInput

const route = useRoute()
const router = useRouter()

const formData = reactive<FormData>({
  organizationName: '',
  encode: '',
  shortName: '',
  parentId: 0,
  managerName: '',
  outerTelephone: '',
  innerTelephone: '',
  address: '',
  sortCode: 0,
  description: '',
  enabledMark: true,
  isSys: false,
})
/**表单验证规则 */
const rules = reactive<FormRules<FormData>>({
  organizationName: [{ required: true, message: '请输入组织机构名字', trigger: 'blur' }],
  encode: [{ required: true, message: '请输入组织机构编码', trigger: 'blur' }],
})

/**机构id，有得话说明是编辑 */
const organizationId = computed(() => route.query.organizationId as string | undefined)
/**父级机构id */
const parentId = computed(() => route.query.parentId as string)
watch(
  parentId,
  (val) => {
    formData.parentId = +val
  },
  { immediate: true }
)

const layer = ref(0)
/**获取机构详情 */
const getSysOrganizationForUpdate = async () => {
  if (!organizationId.value) return
  try {
    const data = await getSysOrganizationForUpdateApi({ organizationId: organizationId.value! })
    for (const [k] of Object.entries(formData)) {
      const key = k as keyof FormData
      formData[key] = data[key] as never
    }
    layer.value = data.layer
  } catch (error) {}
}

onMounted(getSysOrganizationForUpdate)

const addSysOrganization = async () => {
  const param = { ...formData }
  if (!param.parentId) {
    delete param.parentId
  }
  await addSysOrganizationApi(param)
}

const updateSysOrganization = async () => {
  await updateSysOrganizationApi({
    ...formData,
    layer: layer.value,
    organizationId: organizationId.value!,
  })
}

/**保存 */
const handleSave = async () => {
  if (organizationId.value) {
    // 编辑\修改
    await updateSysOrganization()
    appMessage.success('更新成功')
  } else {
    // 新增
    await addSysOrganization()
    appMessage.success('新增成功')
  }
  handleBack()
}

const handleBack = () => {
  router.replace(ROUTER_PATH.ORGANIZATION)
}

const cascaderProps = { checkStrictly: true }

const organizationList = ref<CascaderOption[]>([])

const handleTreeData = (tree: Tree<RightSysOrganizationModel>[]): CascaderOption[] => {
  return tree.map((item) => {
    const target: CascaderOption = {
      label: item.extData.organizationName,
      value: item.extData.mainId,
    }

    if (item.children) {
      target.children = handleTreeData(item.children)
    }

    return target
  })
}
const getRightSysOrganizationForShow = async () => {
  try {
    const reslut = await getRightSysOrganizationForShowApi({ showNoData: false })
    const data = formatTreeData<RightSysOrganizationModel>(reslut, 'organizationName', 'organizationId')
    organizationList.value = handleTreeData(data)
    organizationList.value.unshift({
      label: '顶级',
      value: 0,
    })
  } catch (error) {
    organizationList.value = []
    handleBack()
  }
}

onMounted(getRightSysOrganizationForShow)

const handleSelectVisibleChange = (flag: boolean) => {
  flag && !organizationList.value.length && getRightSysOrganizationForShow()
}
</script>

<template>
  <div class="organization_details-container">
    <div class="back-bar flex">
      <el-button type="primary" @click="handleSave">确定{{ organizationId ? '保存' : '新增' }}</el-button>
      <el-button type="warning" @click="handleBack">返回</el-button>
    </div>

    <el-form ref="formRef" :model="formData" :rules="rules" label-width="100" class="layout-page-pd">
      <el-form-item label="名称：" prop="organizationName">
        <el-input
          v-model="formData.organizationName"
          clearable
          class="inputClass"
          size="large"
          placeholder="请输入名称"
        ></el-input>
      </el-form-item>

      <el-form-item label="机构编码：" prop="encode">
        <el-input
          v-model="formData.encode"
          clearable
          class="inputClass"
          size="large"
          placeholder="请输入机构编码"
        ></el-input>
      </el-form-item>

      <el-form-item label="机构缩写：" prop="shortName">
        <el-input
          v-model="formData.shortName"
          clearable
          class="inputClass"
          size="large"
          placeholder="请输入机构缩写"
        ></el-input>
      </el-form-item>

      <el-form-item prop="parentId" label="上级：">
        <el-cascader
          v-model="formData.parentId"
          :options="organizationList"
          :props="cascaderProps"
          separator=">"
          style="width: 100%"
          @visible-change="handleSelectVisibleChange"
        />
      </el-form-item>

      <el-form-item label="负责人：" prop="managerName">
        <el-input
          v-model="formData.managerName"
          clearable
          class="inputClass"
          size="large"
          placeholder="请输入负责人"
        ></el-input>
      </el-form-item>

      <el-form-item label="联系电话：" prop="outerTelephone">
        <el-input
          v-model="formData.outerTelephone"
          clearable
          class="inputClass"
          size="large"
          placeholder="请输入联系电话"
        ></el-input>
      </el-form-item>

      <el-form-item label="内部电话：" prop="innerTelephone">
        <el-input
          v-model="formData.innerTelephone"
          clearable
          class="inputClass"
          size="large"
          placeholder="请输入联系电话"
        ></el-input>
      </el-form-item>

      <el-form-item label="地址：" prop="address">
        <el-input
          v-model="formData.address"
          clearable
          class="inputClass"
          size="large"
          placeholder="请输入地址"
        ></el-input>
      </el-form-item>

      <el-form-item label="显示排序：" prop="sortCode">
        <el-input-number
          v-model="formData.sortCode"
          controls-position="right"
          size="large"
          :min="0"
          placeholder="请输入显示排序"
        ></el-input-number>
      </el-form-item>

      <el-form-item label="备注：" prop="description">
        <el-input
          v-model="formData.description"
          :autosize="{ minRows: 3, maxRows: 4 }"
          type="textarea"
          class="textareaClass"
          placeholder="请输入"
        ></el-input>
      </el-form-item>

      <el-form-item label="是否启用：" prop="enabledMark">
        <el-switch v-model="formData.enabledMark" active-text="启用后机构才会显示"> </el-switch>
      </el-form-item>

      <el-form-item label="系统管理：" prop="isSys">
        <el-switch v-model="formData.isSys" active-text="开启后无法删除"> </el-switch>
      </el-form-item>
    </el-form>
  </div>
</template>
