import {
  ReqAddHospitalSitterServer,
  ReqConfirmReturn,
  ReqConfirmService,
  ReqDeleteHospitalSitterServer,
  ReqDisableHospitalSitterServer,
  ReqEnableHospitalSitterServer,
  ReqGetHospitalSitterByPage,
  ReqGetHospitalSitterServerByPage,
  ReqHospitalSitterInfo,
  ReqUpdateHospitalSitterServer,
  ResGetDepartmentList,
  ResGetHospitalSitterByPage,
  ResGetHospitalSitterServerByPage,
  ResHospitalSitterInfo
} from '@/api/dto/escort-management.dto.ts'
import { request } from '@/utils/axios-utils'
import type { ApiFunc } from 'axios'

/**
 * 留人陪护申请管理 模块
 */

/**
 * 获取住院科室列表
 */
export const requestGetDepartmentList: ApiFunc<void, ResGetDepartmentList> = () => {
  return request({
    url: '/foying/web/HospitalSitterServer/getDepartmentList',
    method: 'post'
  })
}

/**
 * 获取留人陪护信息分页列表
 */
export const requestGetHospitalSitterServerByPage: ApiFunc<
  ReqGetHospitalSitterServerByPage,
  ResGetHospitalSitterServerByPage
> = (data) => {
  return request({
    url: '/foying/web/HospitalSitterServer/getHospitalSitterServerByPage',
    method: 'post',
    data,
    retonly: false
  })
}

/**
 * 新增留人陪护信息
 */
export const requestAddHospitalSitterServer: ApiFunc<ReqAddHospitalSitterServer, string> = (data) => {
  return request({
    url: '/foying/web/HospitalSitterServer/addHospitalSitterServer',
    method: 'post',
    data
  })
}

/**
 * 修改留人陪护信息
 */
export const requestUpdateHospitalSitterServer: ApiFunc<ReqUpdateHospitalSitterServer, string> = (data) => {
  return request({
    url: '/foying/web/HospitalSitterServer/updateHospitalSitterServer',
    method: 'post',
    data
  })
}

/**
 * 启用留人陪护信息
 */
export const requestEnableHospitalSitterServer: ApiFunc<ReqEnableHospitalSitterServer, string> = (data) => {
  return request({
    url: '/foying/web/HospitalSitterServer/enableHospitalSitterServer',
    method: 'post',
    data
  })
}

/**
 * 禁用留人陪护信息
 */
export const requestDisableHospitalSitterServer: ApiFunc<ReqDisableHospitalSitterServer, string> = (data) => {
  return request({
    url: '/foying/web/HospitalSitterServer/disableHospitalSitterServer',
    method: 'post',
    data
  })
}

/**
 * 删除留人陪护信息
 */
export const requestDeleteHospitalSitterServer: ApiFunc<ReqDeleteHospitalSitterServer, string> = (data) => {
  return request({
    url: '/foying/web/HospitalSitterServer/deleteHospitalSitterServer',
    method: 'post',
    data
  })
}

/**
 * 服务订单管理 - 分页获取列表
 */
export const requestGetHospitalSitterByPage: ApiFunc<ReqGetHospitalSitterByPage, ResGetHospitalSitterByPage> = (
  data
) => {
  return request({
    url: '/foying/web/HospitalSitter/getHospitalSitterByPage',
    method: 'post',
    data,
    retonly: false
  })
}

/**
 * 服务订单管理 - 获取详情
 */
export const requestHospitalSitterInfo: ApiFunc<ReqHospitalSitterInfo, ResHospitalSitterInfo> = (data) => {
  return request({
    url: '/foying/web/HospitalSitter/hospitalSitterInfo',
    method: 'post',
    data
  })
}

/**
 * 服务订单管理 - 确定租赁
 */
export const requestConfirmService: ApiFunc<ReqConfirmService, string> = (data) => {
  return request({
    url: '/foying/web/HospitalSitter/confirmService',
    method: 'post',
    data
  })
}

/**
 * 服务订单管理 - 确定归还
 */
export const requestConfirmReturn: ApiFunc<ReqConfirmReturn, string> = (data) => {
  return request({
    url: '/foying/web/HospitalSitter/confirmReturn',
    method: 'post',
    data
  })
}
