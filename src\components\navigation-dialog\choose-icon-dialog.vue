<script setup lang="ts">
import * as ElementPlusIconsVue from '@element-plus/icons-vue'
import SvgIcon, { svgNames } from '~virtual/svg-component'
import { toKebabCase } from '@/utils'

defineProps<{
  modelValue: boolean
}>()

const modelValue = defineModel<boolean>({ required: true, default: false })

const emit = defineEmits<{
  choose: [key: string]
}>()

const elIconElems = Object.entries(ElementPlusIconsVue).map(([key, element]) => ({
  title: key,
  name: `el-icon${toKebabCase(key)}`,
  element,
}))

const handleCloseDialog = () => {
  modelValue.value = false
}

const handleElIconItemClick = (item: (typeof elIconElems)[0]) => {
  emit('choose', item.name)
  handleCloseDialog()
}
const handleSvgItemClick = (name: string) => {
  emit('choose', name)
  handleCloseDialog()
}
</script>

<template>
  <el-dialog :model-value="modelValue" width="1200" @close="handleCloseDialog">
    <div class="dialog-header" slot="header"><h3>图标选择</h3></div>
    <div class="container">
      <div class="grid">
        <div v-for="item in elIconElems" :key="item.title" class="icon-wrapper" @click="handleElIconItemClick(item)">
          <el-icon :size="26">
            <component :is="item.element" />
          </el-icon>
          <span class="title">{{ item.title }}</span>
        </div>
        <div v-for="iconName in svgNames" :key="iconName" class="icon-wrapper" @click="handleSvgItemClick(iconName)">
          <SvgIcon :name="iconName" class="svg-icon"></SvgIcon>
          <span class="title">{{ iconName }}</span>
        </div>
      </div>
    </div>
  </el-dialog>
</template>

<style lang="scss" scoped>
.dialog-header {
  padding: 10px 0;
}
.container {
  max-height: 700px;
  overflow-y: auto;
  .grid {
    padding: 0 !important;
    border-top: 1px solid var(--el-border-color);
    border-left: 1px solid var(--el-border-color);
    border-radius: 4px;
    display: grid;
    grid-template-columns: repeat(7, 1fr);
  }
  .icon-wrapper {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    cursor: pointer;
    height: 90px;
    font-size: 13px;
    border-right: 1px solid var(--el-border-color);
    border-bottom: 1px solid var(--el-border-color);
    transition: background-color var(--el-transition-duration);

    .title {
      margin-top: 5px;
      text-align: center;
      word-break: keep-all;
    }
    .svg-icon {
      font-size: 26px;
      width: 26px;
      fill: var(--el-text-color-regular);
    }
  }
}
</style>
