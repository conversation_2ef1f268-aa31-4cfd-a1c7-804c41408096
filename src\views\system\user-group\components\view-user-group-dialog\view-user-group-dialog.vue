<script lang="ts" setup>
import ElConfig from '@/components/el-config'
import { useElDialogHook } from '@/hooks/useDialog'
import RoleTable from './role-table.vue'
import ManagerTable from './manager-table.vue'

export type DialogData = {
  title: string
  managerGroupId: string
}

const { dialogVisible, __open, __close } = useElDialogHook(false)

const roleTableRef = ref<null | InstanceType<typeof RoleTable>>(null)
const managerTableRef = ref<null | InstanceType<typeof ManagerTable>>(null)

const dialogData = reactive<DialogData>({
  title: '',
  managerGroupId: '',
})

const tabsVal = ref<'role' | 'manager'>('role')

const getData = async (val: 'role' | 'manager') => {
  await nextTick()

  if (val === 'role') {
    roleTableRef.value?.getRelationForPageById()
  } else {
    managerTableRef.value?.getRelationForPageById()
  }
}

watch(tabsVal, getData)

const handleOpenDialog = (options: DialogData) => {
  __open()

  for (const [key, val] of Object.entries(options)) {
    dialogData[key as keyof DialogData] = val
  }

  getData(tabsVal.value)
}

const handleCloseDialog = () => {
  __close()
}

defineExpose({
  __open: handleOpenDialog,
  __close: handleCloseDialog,
})
</script>

<template>
  <ElConfig>
    <el-dialog v-model="dialogVisible" width="780" class="view_user_group_dialog-container">
      <template #header>
        <div class="title">{{ dialogData.title }}</div>
      </template>

      <el-tabs v-model="tabsVal">
        <TransitionGroup name="fade">
          <el-tab-pane label="关联的角色" key="role" name="role">
            <RoleTable ref="roleTableRef" :manager-group-id="dialogData.managerGroupId" />
          </el-tab-pane>
          <el-tab-pane label="关联的用户" key="manager" name="manager">
            <ManagerTable ref="managerTableRef" :manager-group-id="dialogData.managerGroupId" />
          </el-tab-pane>
        </TransitionGroup>
      </el-tabs>
    </el-dialog>
  </ElConfig>
</template>

<style lang="scss" scoped>
.view_user_group_dialog-container {
  .title {
    height: 44px;
    padding: 10px 16px;
    font-size: 18px;
    color: #303133;
  }
}
</style>
