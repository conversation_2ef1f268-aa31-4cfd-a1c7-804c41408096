<script setup lang="ts">
import { ResRentalOrderDetail } from '@/api/dto/rental-management/rental-order-management.dto.ts'
import { maskIdCard } from '@/utils/index.ts'

defineProps({
  escortInfo: {
    type: Object as PropType<ResRentalOrderDetail>,
    required: true,
    default: () => ({})
  }
})
</script>

<template>
  <div>
    <div>
      <span class="common-panel-title">陪护人信息</span>
    </div>
    <el-descriptions border :column="2">
      <el-descriptions-item label="陪护人名称">{{ escortInfo.caregiverPersonName }}</el-descriptions-item>
      <el-descriptions-item label="性别">{{ escortInfo.caregiverPersonGender }}</el-descriptions-item>
      <el-descriptions-item label="年龄">{{ escortInfo.caregiverPersonAge }}</el-descriptions-item>
      <el-descriptions-item label="身份证号">{{ maskIdCard(escortInfo.caregiverPersonIdCard) }}</el-descriptions-item>
      <el-descriptions-item label="联系电话">{{ escortInfo.caregiverPersonPhone }}</el-descriptions-item>
    </el-descriptions>
  </div>
</template>

<style scoped lang="scss"></style>
