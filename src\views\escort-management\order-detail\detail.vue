<script setup lang="ts">
/**
 * 留人陪护申请管理 - 服务订单 详情
 */
import { ResHospitalSitterInfo } from '@/api/dto/escort-management.dto.ts'
import { requestHospitalSitterInfo, requestConfirmService, requestConfirmReturn } from '@/api/escort-management.api.ts'
import { useMountDialog } from '@/hooks/useMountDialog.ts'
import { appMessage } from '@/hooks/useNaiveApi.ts'
import { ROUTER_PATH } from '@/router/router-path.ts'
import EscortOrderInfo from '@/views/escort-management/order-detail/components/escort-order-info.vue'
import EscortConfirmServiceDialog from '@/views/escort-management/orders/components/escort-confirm-service-dialog.vue'

const { open: openConfirmServiceDialog } = useMountDialog(EscortConfirmServiceDialog)

const router = useRouter()
const route = useRoute()

let hospitalSitterId = ''

onMounted(() => {
  console.log('服务订单列表传入 -> 详情：', route.query)
  if (!route.query.id) {
    appMessage.error('获取服务订单详情失败：服务订单 ID 为空')
    return
  }
  hospitalSitterId = route.query.id as string
  requestDetailData()
})

// 详情数据
const detailInfo = reactive<ResHospitalSitterInfo>({} as ResHospitalSitterInfo)

// 获取详情数据
async function requestDetailData() {
  try {
    const data = await requestHospitalSitterInfo({
      hospitalSitterId: hospitalSitterId
    })

    // 更新详情数据
    Object.assign(detailInfo, data)
  } catch (error) {
    console.error('获取服务订单详情失败:', error)
    appMessage.error('获取服务订单详情失败')
  }
}

function handleBack() {
  router.push({
    path: ROUTER_PATH.ESCORT_ORDERS
  })
}

// 确定服务
function handleConfirmService() {
  openConfirmServiceDialog({
    name: detailInfo.patientName,
    hospitalizationNumber: detailInfo.patno,
    confirmCallback: async (selfLiftingCode: string) => {
      try {
        await requestConfirmService({
          hospitalSitterId: detailInfo.hospitalSitterId,
          selfPickupCode: selfLiftingCode
        })

        await ElMessageBox({
          title: '提示',
          type: 'success',
          message: '确定服务成功',
          callback: () => {
            // 重新获取详情数据
            requestDetailData()
          }
        })

        return Promise.resolve()
      } catch (error) {
        console.error('确定服务失败：', error)
        appMessage.error('确定服务失败')
        return Promise.reject()
      }
    }
  })
}

// 确定归还
function handleConfirmReturn() {
  ElMessageBox.confirm('确定归还吗？')
    .then(async () => {
      try {
        await requestConfirmReturn({
          hospitalSitterId: detailInfo.hospitalSitterId
        })

        appMessage.success('确定归还成功')

        // 重新获取详情数据
        requestDetailData()
      } catch (error) {
        console.error('确定归还失败：', error)
        appMessage.error('确定归还失败')
      }
    })
    .catch(() => {
      // 用户取消操作
    })
}
</script>

<template>
  <div class="page-container">
    <div class="header">
      <el-button class="header-back-btn" type="warning" @click="handleBack">返回</el-button>
    </div>

    <EscortOrderInfo
      :detail-info="detailInfo"
      @confirmService="handleConfirmService"
      @confirmReturn="handleConfirmReturn"
    />
  </div>
</template>

<style scoped lang="scss">
.page-container {
  height: calc(100vh - 50px - 34px);
  display: flex;
  flex-direction: column;
}

.header {
  height: 50px;
  background: #f4f4f5;
  display: flex;
  justify-content: right;
  align-items: center;
  padding: 0 20px;
  position: sticky;
  flex: none;
  z-index: 2;

  & .header-back-btn {
    width: 68px;
    height: 36px;
  }
}
</style>
