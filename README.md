### 注意事项

1. yarn install 报错 error vue-plugin-hiprint@0.0.58-fix: The engine "node" is incompatible with this module. Expected version "16.x". Got "18.20.2" ?

原因：[vue-plugin-hiprint 插件需要 NodeJs 16.x 版本](https://gitee.com/CcSimple/vue-plugin-hiprint#注意事项)，可以使用 临时忽略 engines 检查，已经测试该插件在node.js 18.20.2 正常使用。
如果暂时无法升级 Node.js，可以让 Yarn 忽略引擎检查：

```shell
yarn config set ignore-engines true
yarn install
```

运行完毕后可以重置设置：

```shell
yarn config delete ignore-engines
```

之后正常运行 dev 或 build 即可。


2. 路由名称唯一性、KeepAlive 缓存问题

在 src/utils/router-utils.ts 中，根据接口返回的动态路由 linkUrl 生成全局唯一的路由名称，替换斜杠为中横线，用于 keep-alive 组件缓存。
例如: 'declaration-data-management/list' -> 'declaration-data-management-list'
因为有些页面路径是 aaa/list.vue， bbb/list.vue 这样的路径，如果不指定单文件组件 name，vue 会默认使用文件名作为组件 name，导致 name 不唯一，使用 keep-alive 缓存时能会存在问题。

**所以在本项目正确使用方式是：**
1、在后台菜单配置中，把页面路径配置为 ROUTER_PATH 中的路径，例如：ROUTER_PATH.INPATIENT_REGISTRATION_LIST 对应的路径是 /inpatient-registrationinpatient-registration-list。
2、在单文件组件中指定 name，对应 ROUTER_PATH 中的路径，例如：
```ts
import { getUniqueRouteName } from '@/utils/router-utils.ts'
import { ROUTER_PATH } from '@/router/router-path'
// 在单文件组件中指定 name 用于 keep-alive 缓存
defineOptions({
  name: getUniqueRouteName(ROUTER_PATH.INPATIENT_REGISTRATION_LIST)
})
```
这样在 src/utils/router-utils.ts 中就会生成 name 为 `inpatient-registration-inpatient-registration-list` 的路由，保存于 tab.store.ts cachedViews 中。访问该路时，由于同样指定了单文件组件 name 为 `inpatient-registration-inpatient-registration-list`，所以会缓存该组件。
同时，还会受到路由配置的 noCache 影响，如果 noCache 为 true，则不会缓存该组件（见 tabs.store.ts 中的 addCachedView 方法）。
如果不需要缓存，则不指定单文件组件的 name 即可。
3、结合 onActivated 钩子，在组件重新激活时，重新触发搜索，一开始进入页面时候不触发

```ts
 // 结合 onMounted 钩子，在组件挂载时，获取不需要每次进入页面都重新获取的数据，例如下拉选择器可选项等
 onMounted(async () => {
   // 例如获取科室列表...
 })
// 如果需要第一次进入页面就触发，则可以不需要 hasActivated，直接在 onActivated 中触发搜索
let _hasActivated = false
// 适用于从详情页返回列表页，或者切换 tab 的场景
onActivated(() => {
  if (_hasActivated) {
    // 再次进入页面时，重新触发搜索
    handleSearch()
  } else {
    // 第一次进入页面时，不触发搜索，设置 true 后，再次进入页面时，会触发搜索
    _hasActivated = true
  }
})
```

