<script lang="ts" setup>
import { useUserStore } from '@/stores/user.store'
import { storeToRefs } from 'pinia'
import userDefaultAvatar from '@/assets/image/global/user-default-avatar.png'
import type { SvgName } from '~virtual/svg-component'
import { logoutApi } from '@/api/auth.api'
import { ROUTER_PATH } from '@/router/router-path'
import { useLayoutStore } from '@/stores/layout.store'
import { useEditPasswordHook } from '@/hooks/useEditPassword'
import { useGlobalStore } from '@/stores/global.store'
import { Sunny, Moon } from '@element-plus/icons-vue'
import { isString } from 'lodash-es'
import { useLoadingHook } from '@/hooks/useLoading'
import { appMessage } from '@/hooks/useNaiveApi'

enum USER_DROPDOWN_MENU_ENUM {
  /**白色 */
  WHITE = 'white',
  /**暗色 */
  DARK = 'dark',
  /**修改密码 */
  EDIT_PASSWORD = 'editPassword',
  /**退出登录 */
  LOGOUT = 'logout'
}

type UserDropdownMenuModel = {
  icon: SvgName | Component
  name: string
  type: USER_DROPDOWN_MENU_ENUM
}

const router = useRouter()

const userStore = useUserStore()
const { managerInfo } = storeToRefs(userStore)

const { isMobile } = storeToRefs(useLayoutStore())

const { isDark } = storeToRefs(useGlobalStore())

/**用户信息模块 */
const userDropdownMenu = computed<UserDropdownMenuModel[]>(() => [
  {
    icon: isDark.value ? Sunny : Moon,
    name: isDark.value ? '白色模式' : '暗色模式',
    type: isDark.value ? USER_DROPDOWN_MENU_ENUM.WHITE : USER_DROPDOWN_MENU_ENUM.DARK
  },
  {
    icon: 'svg-user',
    name: '修改密码',
    type: USER_DROPDOWN_MENU_ENUM.EDIT_PASSWORD
  },
  {
    icon: 'svg-logout',
    name: '退出登录',
    type: USER_DROPDOWN_MENU_ENUM.LOGOUT
  }
])

const { loading, loadingFunc } = useLoadingHook()

const userDropdownMethods: Record<USER_DROPDOWN_MENU_ENUM, Function> = {
  white: () => (isDark.value = false),
  dark: () => (isDark.value = true),
  editPassword: async () => await useEditPasswordHook(),
  logout: async () => {
    try {
      if (!managerInfo.value) {
        appMessage.error('请先登录')
      } else {
        await logoutApi({ managerId: managerInfo.value.uid }, { loading: loadingFunc })
      }
    } finally {
      userStore.initUserInfo()
      userStore.initUserRouter()
      router.push({ path: ROUTER_PATH.LOGIN, query: { redirect: router.currentRoute.value.fullPath } })
    }
  }
}

const clickUserDropdownItem = (type: USER_DROPDOWN_MENU_ENUM) => {
  userDropdownMethods[type] && userDropdownMethods[type]()
}
</script>

<template>
  <el-dropdown trigger="click" class="layout_header_user-container">
    <span class="user_info">
      <el-avatar v-loading="loading" :src="userDefaultAvatar"></el-avatar>
      <XyzTransition xyz="fade right">
        <span v-show="!isMobile"> {{ managerInfo?.account || '未登录' }} </span>
      </XyzTransition>
    </span>

    <template #dropdown>
      <el-dropdown-menu>
        <el-dropdown-item v-for="item in userDropdownMenu" :key="item.type" @click="clickUserDropdownItem(item.type)">
          <svg-icon v-if="isString(item.icon)" :name="item.icon" class="dropdown_item-icon" />
          <el-icon v-else :name="item.icon" class="dropdown_item-icon">
            <component :is="item.icon"></component>
          </el-icon>

          {{ item.name }}
        </el-dropdown-item>
      </el-dropdown-menu>
    </template>
  </el-dropdown>
</template>

<style lang="scss" scoped>
.layout_header_user-container {
  cursor: pointer;
  .user_info {
    display: flex;
    align-items: center;
    color: var(--el-text-color-primary);
    :deep(.el-avatar) {
      width: 28px;
      height: 28px;
      margin-right: 8px;
    }
  }
}
.dropdown_item-icon {
  font-size: 16px;
  margin-right: 3px;
}
</style>
