<script lang="ts" setup>
import type { Tree } from '@/types/tree'

defineProps<{
  treeData: Tree[]
}>()

const emit = defineEmits<{
  selectAll: []
  select: [payload: Tree]
}>()

const defaultProps = {
  children: 'children',
  label: 'label',
}

const highlightCurrent = ref(false)

const handleSelectAll = () => {
  highlightCurrent.value = false
  emit('selectAll')
}

const handleNodeClick = (data: Tree) => {
  highlightCurrent.value = true
  emit('select', data)
}
</script>

<template>
  <div class="tree-container">
    <div
      class="tree-title flex flex-align-center cursor"
      :class="{ active: !highlightCurrent }"
      @click="handleSelectAll"
    >
      <svg-icon name="svg-organization_icon"></svg-icon>
      <span class="ml-8">菜单管理</span>
    </div>

    <el-scrollbar class="tree-scrollbar">
      <el-tree
        :data="treeData"
        :props="defaultProps"
        default-expand-all
        :highlight-current="highlightCurrent"
        :expand-on-click-node="false"
        class="tree-main"
        @node-click="handleNodeClick"
      />
    </el-scrollbar>
  </div>
</template>

<style lang="scss" scoped>
.tree-container {
  flex-shrink: 0;
  width: 240px;
  background-color: var(--el-bg-color-overlay);

  .tree-title {
    height: 54px;
    padding: 0 20px;
    border-bottom: 1px solid var(--el-border-color-light);
    line-height: 0;
    transition: all 0.25s;
    color: var(--el-text-color-regular);
    &:hover {
      background-color: var(--el-fill-color-light);
    }
    &.active {
      background-color: var(--el-color-primary-light-9);
      color: var(--el-color-primary);
    }
  }

  .tree-scrollbar {
    height: calc(100% - 54px);

    .tree-main {
      padding: 0 20px 20px;
      :deep(.el-tree-node) {
        white-space: wrap;
        .el-tree-node__content {
          min-height: 36px;
          height: auto;
          padding: 6px 0;
        }
      }
    }
  }
}
</style>
