<script lang="ts" setup>
import type { NavigationMenuItem, NavigationMenuItemExtraData } from '@/api/dto/navigation.dto'
import type { GetSysNavigationForShowOutput, UpdateSysRoleAuthNavListModel } from '@/api/dto/system/roles-manage.dto'
import { ROLE_TYPE_EUNM } from '@/api/dto/system/roles-manage.dto'
import { getSysNavigationForShowApi } from '@/api/system/roles-manage.api'
import type { Tree } from '@/types/tree'
import { formatTreeData } from '@/utils'
import { ElTree, type CheckboxValueType } from 'element-plus'
import type Node from 'element-plus/es/components/tree/src/model/node'

const props = defineProps<{
  roleId?: string
  roleType: ROLE_TYPE_EUNM
}>()

const emit = defineEmits<{
  changeRoleAuthNavList: [payload: UpdateSysRoleAuthNavListModel[]]
}>()

const treeRef = ref<null | InstanceType<typeof ElTree>>(null)

/**组件数据 */
const roleAuthTreeData = reactive({
  /**原始数据 */
  rowData: [] as GetSysNavigationForShowOutput,
  /**权限映射 */
  roleAuth: {} as Record<string, string[]>,
  /**是否全选 */
  isCheckAll: false,
})

/**数结构数据 */
const treeData = ref<Tree<NavigationMenuItem>[]>([])

/**获取角色菜单权限 */
const getSysNavigationForShow = async () => {
  try {
    roleAuthTreeData.rowData = await getSysNavigationForShowApi({ roleId: props.roleId })
    treeData.value = formatTreeData<NavigationMenuItem>(roleAuthTreeData.rowData, 'navigationName', 'navigationId')
  } catch (error) {
    roleAuthTreeData.rowData = []
  }
}
onMounted(getSysNavigationForShow)

/**获取权限映射 */
const getRoleAuth = async (list: GetSysNavigationForShowOutput) => {
  await nextTick()

  roleAuthTreeData.roleAuth = list.reduce((target, item) => {
    const { navigationId, checkMark } = item

    // 设置tree的checkbox样式
    treeRef.value?.setChecked(navigationId, checkMark, false)

    target[navigationId] = item.authList.filter((item) => item.checkMark).map((item) => item.functionAuthId)
    return target
  }, roleAuthTreeData.roleAuth)
}

// 获取到原始数据后做操作权限映射
watch(() => roleAuthTreeData.rowData, getRoleAuth)

watch(
  () => props.roleType,
  (roleType) => {
    if (roleType === ROLE_TYPE_EUNM.SUPER_MANAGE) {
      emit(
        'changeRoleAuthNavList',
        roleAuthTreeData.rowData.map((item) => ({
          navigationId: item.navigationId,
          functionAuthIdList: item.authList.map((item) => item.functionAuthId).join(','),
        }))
      )
    } else {
      getRoleAuth(roleAuthTreeData.rowData)
    }
  }
)

/**发送组件事件，将改变后的菜单权限发送给父级组件 */
const changeRoleAuthNavList = async (authMap: Record<string, string[]>) => {
  await nextTick()

  const currentAuth = authMap[currentTreeId.value]
  if (currentAuth) {
    const len = currentAuth.length
    roleAuthTreeData.isCheckAll = !!len && len === currentAuth.length
  }

  /**父级 */
  const treeHalfCheckedKeys = treeRef.value!.getHalfCheckedKeys()
  /**子级 */
  const treeCheckedKeys = treeRef.value!.getCheckedKeys()
  /**总 */
  const totalCheckedKeys = [...treeHalfCheckedKeys, ...treeCheckedKeys]

  /**接口请求的结构数据 */
  const roleAuthNavList: UpdateSysRoleAuthNavListModel[] = Object.entries(authMap).map(([key, functionAuthIdList]) => {
    return {
      navigationId: key,
      functionAuthIdList: functionAuthIdList.join(','),
    }
  })

  emit(
    'changeRoleAuthNavList',
    roleAuthNavList.filter((item) => totalCheckedKeys.includes(item.navigationId))
  )
}

// 操作全选发生变化的时候，修改modelValue
watch(() => roleAuthTreeData.roleAuth, changeRoleAuthNavList, { deep: true })

/**当前点击得tree节点 */
const currentTreeId = ref('')
const checkboxLabelForCheckAll = ref('操作权限')

/**操作权限列表 */
const functionList = ref<NavigationMenuItemExtraData[]>([])

/**点击tree节点 */
const handleNodeClick = (data: Tree<NavigationMenuItem>) => {
  const { navigationId, navigationName, authList } = data.extData
  currentTreeId.value = navigationId
  checkboxLabelForCheckAll.value = `${navigationName} - 操作权限`
  functionList.value = authList

  // 改变全选checkbox的状态
  const { roleAuth } = roleAuthTreeData
  const len = roleAuth[currentTreeId.value].length
  roleAuthTreeData.isCheckAll = !!len && len === authList.length
}

/**点击tree节点复选框 */
const handleCheckChange = (data: Tree<NavigationMenuItem>, flag: boolean) => {
  const { navigationId, parentId } = data.extData

  if (!flag && parentId) {
    roleAuthTreeData.roleAuth[navigationId] = []
  } else {
    changeRoleAuthNavList(roleAuthTreeData.roleAuth)
  }
}

/**是否复选框是否中置状态 */
const isIndeterminate = computed(() => {
  if (currentTreeId.value) {
    const authList = roleAuthTreeData.roleAuth[currentTreeId.value]
    return !!authList.length && authList.length < functionList.value.length
  } else {
    return false
  }
})

/**全选checkbox点击事件 */
const handleCheckAllChange = (val: CheckboxValueType) => {
  if (val) {
    roleAuthTreeData.roleAuth[currentTreeId.value] = functionList.value.map((item) => item.functionAuthId)
    treeRef.value?.setChecked(currentTreeId.value, true, false)
  } else {
    roleAuthTreeData.roleAuth[currentTreeId.value] = []
  }
}
</script>

<template>
  <div class="role_auth_tree-container flex">
    <el-scrollbar always class="navigation-tree">
      <el-tree
        ref="treeRef"
        :data="treeData"
        default-expand-all
        :expand-on-click-node="false"
        node-key="nodeKey"
        show-checkbox
        @check-change="handleCheckChange"
        @node-click="handleNodeClick"
      >
        <template #default="{ node }: { node: Node }">
          <div class="cursor">
            {{ node.label }}
            <span v-if="node.data.extData.authList.length">
              ({{ roleAuthTreeData.roleAuth[node.data.extData.navigationId]?.length }}/{{
                node.data.extData.authList.length
              }})
            </span>
          </div>
        </template>
      </el-tree>
    </el-scrollbar>

    <el-tabs style="flex-grow: 1">
      <el-tab-pane>
        <template #label>
          <el-checkbox
            v-model="roleAuthTreeData.isCheckAll"
            :indeterminate="isIndeterminate"
            :disabled="!functionList.length"
            @change="handleCheckAllChange"
            :label="checkboxLabelForCheckAll"
          >
          </el-checkbox>
        </template>

        <el-scrollbar always height="320">
          <el-checkbox-group v-show="functionList.length" v-model="roleAuthTreeData.roleAuth[currentTreeId]">
            <el-checkbox v-for="item in functionList" :label="item.functionAuthId" class="mt-6">
              {{ item.functionAuthName }}
            </el-checkbox>
          </el-checkbox-group>

          <div v-show="!functionList.length" class="mt-6">尚未添加任何权限</div>
        </el-scrollbar>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<style lang="scss">
.role_auth_tree-container {
  width: 100%;
  box-shadow: 0 0 0 1px var(--el-border-color-light);

  .navigation-tree {
    flex-shrink: 0;
    width: 250px;
    height: 360px;
    box-shadow: 1px 0 0 0 var(--el-border-color-light);
  }
}
</style>
