/**
 * 陪护人租赁管理 - 陪护介绍管理
 */

/**
 * 获取陪护介绍信息 - 响应数据
 */
export interface ResInpatientCaregiverIntroduceInfo {
  /** 服务名称 */
  serverName: string
  /** 联系电话 */
  phone: string
  /** 主图，http url 格式 */
  picture: string
  /** 服务介绍 */
  serverIntroduce: string
  /** 团队介绍 */
  teamIntroduce: string
}

/**
 * 保存陪护介绍信息 - 请求参数
 */
export interface ReqSaveInpatientCaregiverIntroduceInfo {
  /** 服务名称 */
  serverName: string
  /** 联系电话 */
  phone: string
  /** 主图 */
  base64picture: string
  /** 服务介绍 */
  serverIntroduce: string
  /** 团队介绍 */
  teamIntroduce: string
}

/**
 * 陪护介绍管理 - 响应
 */
export interface ResSaveInpatientCaregiverIntroduceInfo {
  msg: string
}
