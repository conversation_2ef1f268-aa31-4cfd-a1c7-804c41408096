<script setup lang="ts">
/**
 * 出院结算退费 - 银行卡退费记录
 */

import { ROUTER_PATH } from '@/router/router-path.ts'
import BankCardRefundInfo from './components/bank-card-refund-info.vue'
import { BankCardRefundRecordsItem } from '@/api/dto/discharge-settlement-refunds/bank-card-refund-records.dto.ts'
import { useMountDialog } from '@/hooks/useMountDialog'
import BankCardRefundAuditDialog from '../components/bank-card-refund-audit-dialog.vue'

const router = useRouter()

const { open: openBankCardRefundAuditDialog } = useMountDialog(BankCardRefundAuditDialog)

// 模拟数据
const mockData: BankCardRefundRecordsItem[] = [
  {
    id: '1001',
    patientName: '张三',
    patientNo: 'ZY20230001',
    patientDept: '内科',
    patientInTime: '2023-10-01 08:30:00',
    patientOutTime: '2023-10-15 14:00:00',
    totalAmount: 12500.5,
    medicalInsuranceAmount: 8750.35,
    depositAmount: 5000.0,
    refundAmount: 1250.85,
    accountName: '张三',
    accountBank: '中国建设银行',
    accountCardNo: '6217 **** **** 3456',
    accountPhone: '138 **** 5678',
    status: 0
  },
  {
    id: '1002',
    patientName: '李四',
    patientNo: 'ZY20230002',
    patientDept: '外科',
    patientInTime: '2023-09-15 10:20:00',
    patientOutTime: '2023-10-05 11:30:00',
    totalAmount: 18300.75,
    medicalInsuranceAmount: 12810.52,
    depositAmount: 8000.0,
    refundAmount: 2509.77,
    accountName: '李四',
    accountBank: '中国工商银行',
    accountCardNo: '6222 **** **** 7890',
    accountPhone: '139 **** 1234',
    status: 1
  },
  {
    id: '1003',
    patientName: '王五',
    patientNo: 'ZY20230003',
    patientDept: '骨科',
    patientInTime: '2023-10-10 09:15:00',
    patientOutTime: '2023-10-25 16:45:00',
    totalAmount: 21750.25,
    medicalInsuranceAmount: 15225.18,
    depositAmount: 10000.0,
    refundAmount: 3475.07,
    accountName: '王五',
    accountBank: '中国农业银行',
    accountCardNo: '6228 **** **** 1357',
    accountPhone: '135 **** 2468',
    status: 2
  }
]

// 银行卡退费记录表格数据
const bankCardRefundData = reactive({
  listData: [] as BankCardRefundRecordsItem[],
  total: 0
})

// 搜索 - 银行卡退费记录
async function handleSearchBankCardRefundData({ currentPage, pageSize, searchData }) {
  console.log('搜索 - 银行卡退费记录：', currentPage, pageSize, searchData)
  try {
    setTimeout(() => {
      console.log('获取 银行卡退费记录 列表 成功：', mockData)
      bankCardRefundData.listData = mockData
      bankCardRefundData.total = mockData.length
    }, 1000)
  } catch (e) {
    console.error('搜索 银行卡退费记录 列表 失败：', e)
    bankCardRefundData.listData = []
    bankCardRefundData.total = 0
  }
}

// 查看详情 - 银行卡退费记录
function handleViewBankCardRefundDataDetail(e: BankCardRefundRecordsItem) {
  console.log('查看详情：', e)
  router.push({
    path: ROUTER_PATH.BANK_CARD_REFUND_RECORDS_DETAIL,
    query: { bankCardRefundId: e.id } // 银行卡退费记录 ID
  })
}

// 审核 - 银行卡退费记录
function handleAuditBankCardRefundData(e: BankCardRefundRecordsItem) {
  console.log('审核：', e)
  openBankCardRefundAuditDialog({
    id: e.id,
    confirmCallback: (data: { auditStatus: number; rejectReason: string; done: () => void }) => {
      console.log('审核：', data)
      data.done()
    }
  })
}
</script>

<template>
  <div class="bank-card-refund-records-page-container">
    <BankCardRefundInfo
      :list-data="bankCardRefundData.listData"
      :total="bankCardRefundData.total"
      @search="handleSearchBankCardRefundData"
      @view-detail="handleViewBankCardRefundDataDetail"
      @audit="handleAuditBankCardRefundData"
    />
  </div>
</template>

<style scoped lang="scss">
.bank-card-refund-records-page-container {
  padding: 10px 20px;
}
</style>
