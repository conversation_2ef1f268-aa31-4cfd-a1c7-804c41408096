<script lang="ts" setup>
import { usePaginationHook } from '@/hooks/usePagination'
import SearchBar, { type SearchData } from './components/search-bar.vue'
import { useLoadingHook } from '@/hooks/useLoading'
import type { GetSysOperationLogForPageOutput } from '@/api/dto/system/operation-log.dto'
import { getSysOperationLogForPageApi } from '@/api/system/operation-log.api'

const { currentPage, pageSize, total } = usePaginationHook()

const { loading, loadingFunc } = useLoadingHook()

const searchData = reactive<SearchData>({
  operateTime: ['', ''],
  account: '',
  businessmodule: '',
  operResult: '',
  operUrl: '',
  operationType: '',
  status: '',
})

const tableData = ref<GetSysOperationLogForPageOutput>([])

const getSysOperationLogForPage = async () => {
  try {
    const { data, recordCount } = await getSysOperationLogForPageApi(
      {
        account: searchData.account,
        businessmodule: searchData.businessmodule,
        operResult: searchData.operResult,
        operUrl: searchData.operUrl,
        operationType: searchData.operationType,
        status: searchData.status,
        operTimeBegin: searchData.operateTime[0],
        operTimeEnd: searchData.operateTime[1],
        page: currentPage.value,
        rows: pageSize.value,
      },
      { retonly: false, loading: loadingFunc, showNoData: false }
    )

    tableData.value = data
    total.value = recordCount!
  } catch (error) {
    tableData.value = []
    total.value = 1
  }
}

const handleSearch = (data: SearchData) => {
  for (const [k, v] of Object.entries(data)) {
    const key = k as keyof SearchData
    const value = v as any
    searchData[key] = value
  }
  getSysOperationLogForPage()
}

watch([currentPage, pageSize], () => {
  getSysOperationLogForPage()
})

onMounted(getSysOperationLogForPage)
</script>

<template>
  <div class="operation_log-container layout-page-pd">
    <SearchBar @search="handleSearch" />

    <BaseTable v-loading="loading" :data="tableData" border scrollbar-always-on height="630">
      <el-table-column prop="accessMode" label="接口访问方式" width="120"></el-table-column>
      <el-table-column prop="account" label="账号" width="80"></el-table-column>
      <el-table-column prop="browser" label="浏览器" show-overflow-tooltip width="100"></el-table-column>
      <el-table-column prop="businessmodule" label="系统模块" width="120"></el-table-column>
      <el-table-column prop="hostIp" label="客户端IP" width="160"></el-table-column>
      <el-table-column prop="operParams" label="请求参数" show-overflow-tooltip width="200"></el-table-column>
      <el-table-column prop="operResult" label="访问结果" show-overflow-tooltip width="200"></el-table-column>
      <el-table-column prop="operTime" label="操作时间" width="200"> </el-table-column>
      <el-table-column prop="operUrl" label="请求地址" show-overflow-tooltip width="200"></el-table-column>
      <el-table-column prop="operationSystem" label="操作系统" width="120"></el-table-column>
      <el-table-column prop="responseTime" label="响应时间" width="90"></el-table-column>
      <el-table-column prop="resultCode" label="结果码" width="80"></el-table-column>
      <el-table-column prop="status" label="状态" width="90"></el-table-column>
    </BaseTable>

    <BasePagination v-model:current-page="currentPage" v-model:page-size="pageSize" :total="total" />
  </div>
</template>
