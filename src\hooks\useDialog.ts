/**单例模式el-dialog */
export const useElDialogHook = <T = any, P extends boolean = true>(usePromise?: P) => {
  const dialogVisible = ref(false)

  const promiseFunc = reactive({
    resolve: null as null | ((value: T) => void),
    reject: null as null | ((reason?: string) => void),
  })

  const __open = (): P extends true ? Promise<T> : undefined => {
    dialogVisible.value = true
    if (usePromise === undefined || usePromise === true) {
      return new Promise((resolve, reject) => {
        promiseFunc.resolve = resolve
        promiseFunc.reject = reject
      }) as any
    } else {
      return undefined as any
    }
  }

  const __close = () => {
    dialogVisible.value = false
  }

  return { dialogVisible, __open, __close, promiseFunc }
}
