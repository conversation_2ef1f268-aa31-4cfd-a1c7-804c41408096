/**
 * 压缩规则
 * code: 'c'
 * name: 'n'
 */

/**
 * 将原始 nationList 转换为 Element UI Select 组件所需的格式
 * @param nationList 原始省市区数据
 * @param useCode 是否使用区域编码，true 则输出 CHN，false 则输出 中国
 */
export const formatNationList = (nationList: any[], useCode = false) => {
  return nationList.map((nation) => ({
    value: useCode ? nation.c : nation.n,
    label: nation.n
  }))
}

export const loadNationList = async (useCode = false) => {
  const nationList = await import('@/assets/other/nation-list-min.json')
  return formatNationList(nationList.default, useCode)
}

/**
 * 纯汉字，如 中国
 */
export const getNationCascaderOptions = async () => {
  return await loadNationList()
}

/**
 * 国家区域编码，如 CHN
 */
export const getNationCascaderOptionsByCode = async () => {
  return await loadNationList(true)
}
