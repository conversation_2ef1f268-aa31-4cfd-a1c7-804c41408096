export interface SysDictionaryDirModel {
  classLayer: number
  dictionaryInfoId: string
  dictionaryInfoName: string
  enCode: string
  expanded: 'false' | 'true'
  isLeaf: 'false' | 'true'
  mainId: number
  parentId: number
}

export type GetSysDictionaryInfoForShowOutput = SysDictionaryDirModel[]

export interface GetSysDictionaryInfoForPageInput {
  dictionaryInfoName: string
  enCode: string
  enabledMark: boolean | ''
  parentId: number
  page: number
  rows: number
}
export interface SysDictionaryInfoModel {
  dictionaryInfoId: string
  dictionaryInfoName: string
  enCode: string
  enabledMark: boolean
  isSys: boolean
  sortCode: number
}
export type GetSysDictionaryInfoForPageOutput = SysDictionaryInfoModel[]

export interface GetSysDictionaryInfoForUpdateInput {
  dictionaryInfoId: string
}
export interface GetSysDictionaryInfoForUpdateOutput {
  dictionaryInfoName: string
  enCode: string
  enabledMark: boolean
  isSys: boolean
  parentId: number
  sortCode: number
}

export interface AddSysDictionaryInfoInput {
  description: string
  dictionaryInfoName: string
  enCode: string
  enabledMark: boolean
  isSys: boolean
  parentId: number
  sortCode: number
}

export interface UpdateSysDictionaryInfoInput extends AddSysDictionaryInfoInput {
  dictionaryInfoId: string
}

export interface DeleteSysDictionaryInfoInput {
  id: string
}

export interface GetSysDictionaryDetailForPageInput {
  dictionaryInfoId: string
  enabledMark: '' | boolean
  page: number
  rows: number
}
export interface SysDictionaryDetailItemModel {
  dictionaryDetailId: string
  dictionaryDetailName: string
  enabledMark: boolean
  encode: string
  sortCode: number
}
export type GetSysDictionaryDetailForPageOutput = SysDictionaryDetailItemModel[]

export interface GetSysDictionaryDetailForUpdateInput {
  dictionaryDetailId: string
}
export interface GetSysDictionaryDetailForUpdateOutput {
  dictionaryDetailName: string
  enabledMark: boolean
  encode: string
  isSys: boolean
  sortCode: number
  description: string
}

export interface AddSysDictionaryDetailInput {
  description: string
  dictionaryDetailName: string
  /**父级id */
  dictionaryInfoId: string
  enabledMark: boolean
  encode: string
  isSys: boolean
  sortCode: number
}

export interface UpdateSysDictionaryDetailInput extends AddSysDictionaryDetailInput {
  /**自己id */
  dictionaryDetailId: string
}

export interface DeleteSysDictionaryDetailInput {
  dictionaryDetailId: string
}
