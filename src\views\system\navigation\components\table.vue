<script lang="ts" setup>
import type { OperaRow } from '@/api/dto/index.dto'
import type { GetSysNavigationForPageInput, SysNavigationMenuItem } from '@/api/dto/system/navigation.dto'
import BasePagination from '@/components/base-pagination'
import { useFormHook } from '@/hooks/useForm'
import { usePaginationHook } from '@/hooks/usePagination'

export type SearchData = Omit<GetSysNavigationForPageInput, 'page' | 'rows' | 'parentId'>

const props = defineProps<{
  tableData: any[]
  parentId: number
  total: number
}>()

const { formRef, resetForm } = useFormHook()
const { currentPage, pageSize } = usePaginationHook()
const formData = reactive<SearchData>({
  navigationName: '',
  enabledMark: '',
})

const emit = defineEmits<{
  search: [payload: GetSysNavigationForPageInput]
  newNavigation: []
  editInfo: [payload: OperaRow<SysNavigationMenuItem>]
  editRoles: [payload: OperaRow<SysNavigationMenuItem>]
  deleteRow: [payload: OperaRow<SysNavigationMenuItem>]
}>()

const searchFunc = () => {
  emit('search', {
    ...formData,
    parentId: props.parentId,
    page: currentPage.value,
    rows: pageSize.value,
  })
}

const handleSearch = () => {
  currentPage.value = 1
  searchFunc()
}

watch([currentPage, pageSize], () => searchFunc())

watch(
  () => props.parentId,
  () => searchFunc(),
  { immediate: true }
)

const handleReset = () => {
  resetForm()
  handleSearch()
}

const handleNewNavigation = () => {
  emit('newNavigation')
}

const handleEditInfo = (target: OperaRow<SysNavigationMenuItem>) => {
  emit('editInfo', target)
}
const handleEditRoles = (target: OperaRow<SysNavigationMenuItem>) => {
  emit('editRoles', target)
}

const handleDeleteRow = (target: OperaRow<SysNavigationMenuItem>) => {
  emit('deleteRow', target)
}

defineExpose({ getData: searchFunc })
</script>

<template>
  <div class="table-container ml-10">
    <el-form ref="formRef" :model="formData" inline>
      <el-form-item prop="navigationName" label="菜单名称：">
        <el-input v-model="formData.navigationName" @keydown.enter="handleSearch"></el-input>
      </el-form-item>

      <el-form-item prop="enabledMark" label="是否启用：">
        <el-select v-model="formData.enabledMark" width="120">
          <el-option label="全部" value=""></el-option>
          <el-option label="已启用" :value="true"></el-option>
          <el-option label="未启用" :value="false"></el-option>
        </el-select>
      </el-form-item>

      <el-form-item>
        <el-button type="primary" @click="handleSearch">搜索</el-button>
        <el-button @click="handleReset">重置</el-button>
        <el-button type="success" @click="handleNewNavigation">
          {{ parentId === 0 ? '新增一级' : '新增子级' }}
        </el-button>
      </el-form-item>
    </el-form>

    <BaseTable :data="tableData" border height="660">
      <el-table-column prop="navigationName" label="名称" min-width="120"></el-table-column>
      <el-table-column label="层级" align="center" width="90">
        <template #default="{ row }: { row: SysNavigationMenuItem }">
          {{ row.layer ? `${row.layer}级` : '顶级' }}
        </template>
      </el-table-column>
      <el-table-column label="是否启用" align="center" width="90">
        <template #default="{ row }: { row: SysNavigationMenuItem }">
          <el-tag :type="row.enabledMark ? 'success' : 'danger'">{{ row.enabledMark ? '启用' : '停用' }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="createTime" label="创建时间" width="200"></el-table-column>
      <el-table-column prop="modifyTime" label="更新时间" width="200"></el-table-column>
      <el-table-column prop="sortCode" label="排序" width="60" align="center"></el-table-column>
      <el-table-column label="操作" min-width="220">
        <template #default="{ row }: { row: OperaRow<SysNavigationMenuItem> }">
          <el-button v-auth="'getSysNavigationForUpdate'" @click="handleEditInfo(row)"> 编辑 </el-button>
          <el-button v-auth="'getSysFunctionauthForListByNavigationId'" type="primary" @click="handleEditRoles(row)">
            权限
          </el-button>
          <el-button v-auth="'deleteSysNavigation'" :loading="row.loading" type="danger" @click="handleDeleteRow(row)">
            删除
          </el-button>
        </template>
      </el-table-column>
    </BaseTable>

    <BasePagination v-model:current-page="currentPage" v-model:page-size="pageSize" :total="total" />
  </div>
</template>

<style lang="scss" scoped>
.table-container {
  flex-grow: 1;
  padding: 20px;
  background-color: var(--el-bg-color-overlay);
}
</style>
