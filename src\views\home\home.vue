<script lang="ts" setup>
import { useGlobalStore } from '@/stores/global.store'
import { storeToRefs } from 'pinia'

const { appConfig } = storeToRefs(useGlobalStore())
</script>

<template>
  <div class="home-container flex-center">
    <img class="icon" src="~@/assets/image/global/welcome.png" />
    <div class="title">{{ appConfig.appTitle }}</div>
  </div>
</template>

<style lang="scss" scoped>
.home-container {
  flex-direction: column;
  height: calc(100vh - $layoutHeaderHeight - $layoutTabsHeight);
  overflow: hidden;
  .icon {
    width: 240px;
  }
  .title {
    margin-top: 54px;
    font-size: 24px;
    color: var(--el-color-primary);
    font-weight: 400;
    line-height: 36px;
  }
}
</style>
