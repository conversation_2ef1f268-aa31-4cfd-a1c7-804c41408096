<script setup lang="ts">
/**
 * 医保登记管理 - 医保登记信息
 */
import { RegisteredInsuranceInfo, WaitingInsuranceRegistrationInfo } from '@/api/dto/insurance-registration.dto.ts'
import {
  requestGetInsuredPlaceTypes,
  requestGetInsuranceTypeOptions,
  requestInsuranceRegistrationByPage,
  requestWaitingInsuranceRegistrationByPage
} from '@/api/insurance-registration.api.ts'
import { ROUTER_PATH } from '@/router/router-path.ts'
import RegistrationInfo from '@/views/insurance-registration/list/components/registration-info.vue'
import { requestRentalSelectDepartmentList } from '@/api/rental-management.api.ts'
import { ResRentalQuerydeptDTO } from '@/api/dto/rental-management/rental-order-management.dto.ts'
import { getUniqueRouteName } from '@/utils/router-utils.ts'

// 在单文件组件中指定 name 用于 keep-alive 缓存
defineOptions({
  name: getUniqueRouteName(ROUTER_PATH.INSURANCE_REGISTRATION_LIST)
})

const router = useRouter()

// 获取所有参保类别
const insuredPlaceOptions = ref([] as string[])
// 获取所有医保住院类型
const insuranceTypeOptions = ref([] as string[])

// 科室列表
const departmentOptions = ref<ResRentalQuerydeptDTO[]>([] as ResRentalQuerydeptDTO[])

onMounted(async () => {
  // 挂载时获取可选项，表格数据由 RegistrationInfo 组件触发搜索
  await getDepartmentOptions()
  await getInsuredPlaceOptions()
  await getInsuranceTypeOptions()
})

// 未登记表格数据
const waitingRegistrationData = reactive({
  listData: [] as WaitingInsuranceRegistrationInfo[],
  total: 0
})

// 搜索 - 未登记
async function handleSearchWaitingRegistrationData({ currentPage, pageSize, searchData }) {
  console.log('搜索 - 未登记：', currentPage, pageSize, searchData)
  try {
    const { data, recordCount } = await requestWaitingInsuranceRegistrationByPage({
      admissionName: searchData.admissionName,
      admissionNo: searchData.admissionNo,
      insuredPlaceType: searchData.insuredPlaceType,
      insuranceType: searchData.insuranceType,
      hospitalizationDepartmentName: searchData.hospitalizationDepartmentName,
      hasRegistration: false,
      page: currentPage,
      rows: pageSize
    })
    console.log('获取 未登记 列表 成功：', data)
    waitingRegistrationData.listData = data
    waitingRegistrationData.total = recordCount
  } catch (e) {
    console.error('搜索 未登记 列表 失败：', e)
    waitingRegistrationData.listData = []
    waitingRegistrationData.total = 0
  }
}

// 查看详情 - 未登记
function handleViewWaitingRegistrationDataDetail(e: WaitingInsuranceRegistrationInfo | RegisteredInsuranceInfo) {
  console.log('查看详情：', e)
  router.push({
    path: ROUTER_PATH.INSURANCE_REGISTRATION_DETAIL,
    query: { insuranceregistrationId: e.insuranceregistrationId }
  })
}

// 已登记表格数据
const registeredInsuranceData = reactive({
  listData: [] as RegisteredInsuranceInfo[],
  total: 0
})

// 搜索 - 已登记
async function handleSearchRegisteredData({ currentPage, pageSize, searchData }) {
  console.log('搜索 - 已登记：', currentPage, pageSize, searchData)
  try {
    const { data, recordCount } = await requestInsuranceRegistrationByPage({
      admissionName: searchData.admissionName,
      admissionNo: searchData.admissionNo,
      insuredPlaceType: searchData.insuredPlaceType,
      insuranceType: searchData.insuranceType,
      hasRegistration: true,
      hospitalizationDepartmentName: searchData.hospitalizationDepartmentName,
      page: currentPage,
      rows: pageSize
    })
    console.log('获取 已登记 列表 成功：', data)
    registeredInsuranceData.listData = data
    registeredInsuranceData.total = recordCount
  } catch (e) {
    console.error('搜索 已登记 列表 失败：', e)
    registeredInsuranceData.listData = []
    registeredInsuranceData.total = 0
  }
}

// 查看详情 - 已登记
function handleViewRegisteredDataDetail(e: WaitingInsuranceRegistrationInfo | RegisteredInsuranceInfo) {
  console.log('查看详情：', e)
  router.push({
    path: ROUTER_PATH.INSURANCE_REGISTRATION_DETAIL,
    query: { insuranceregistrationId: e.insuranceregistrationId }
  })
}

// 获取所有参保类别
async function getInsuredPlaceOptions() {
  try {
    insuredPlaceOptions.value = await requestGetInsuredPlaceTypes()
  } catch (e) {
    console.error('获取所有参保类别失败：', e)
    insuredPlaceOptions.value = []
  }
}

// 获取所有医保住院类型
async function getInsuranceTypeOptions() {
  try {
    insuranceTypeOptions.value = await requestGetInsuranceTypeOptions()
  } catch (e) {
    console.error('获取所有医保住院类型失败：', e)
    insuranceTypeOptions.value = []
  }
}

// 获取科室列表
async function getDepartmentOptions() {
  const res = await requestRentalSelectDepartmentList()
  departmentOptions.value = res || []
}
</script>

<template>
  <el-tabs class="tab-container">
    <el-tab-pane label="待登记">
      <RegistrationInfo
        mode="waiting"
        :department-options="departmentOptions"
        :insured-place-options="insuredPlaceOptions"
        :insurance-type-options="insuranceTypeOptions"
        :list-data="waitingRegistrationData.listData"
        :total="waitingRegistrationData.total"
        @search="handleSearchWaitingRegistrationData"
        @view-detail="handleViewWaitingRegistrationDataDetail"
      />
    </el-tab-pane>
    <el-tab-pane label="已登记">
      <RegistrationInfo
        mode="registered"
        :department-options="departmentOptions"
        :insured-place-options="insuredPlaceOptions"
        :insurance-type-options="insuranceTypeOptions"
        :list-data="registeredInsuranceData.listData"
        :total="registeredInsuranceData.total"
        @search="handleSearchRegisteredData"
        @view-detail="handleViewRegisteredDataDetail"
      />
    </el-tab-pane>
  </el-tabs>
</template>

<style scoped lang="scss">
.tab-container {
  margin-top: 20px;
}
</style>
