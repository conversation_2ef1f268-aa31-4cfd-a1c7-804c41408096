export interface LeftSysOrganizationModel {
  classLayer: number
  encode: string
  expanded: 'true' | 'false'
  isLeaf: 'true' | 'false'
  mainId: number
  organizationId: string
  organizationName: string
  parentId: number
}
export type GetLeftSysOrganizationForShowOutput = LeftSysOrganizationModel[]

export interface GetSysOrganizationForPageInput {
  enabledMark: string
  organizationName: string
  page: number
  parentId: number
  rows: number
}
export interface OrganizationPageItemModel {
  address: string
  description: string
  enabledMark: boolean
  encode: string
  managerName: string
  organizationId: string
  organizationName: string
  outerTelephone: string
  sortCode: number
}
export type GetSysOrganizationForPageOutput = OrganizationPageItemModel[]

export interface DeleteSysOrganizationInput {
  organizationId: string
}

export interface RightSysOrganizationModel {
  classLayer: number
  encode: string
  expanded: 'true' | 'false'
  isLeaf: 'true' | 'false'
  mainId: number
  organizationId: string
  organizationName: string
  parentId: number
}
export type GetRightSysOrganizationForShowOutput = RightSysOrganizationModel[]

export interface AddSysOrganizationInput {
  address: string
  description: string
  enabledMark: boolean
  encode: string
  innerTelephone: string
  isSys: boolean
  managerName: string
  organizationName: string
  outerTelephone: string
  parentId?: number
  shortName: string
  sortCode: number
}

export interface GetSysOrganizationForUpdateInput {
  organizationId: string
}
export interface GetSysOrganizationForUpdateOutput extends AddSysOrganizationInput {
  layer: number
}

export interface UpdateSysOrganizationInput extends AddSysOrganizationInput {
  layer: number
  organizationId: string
}
