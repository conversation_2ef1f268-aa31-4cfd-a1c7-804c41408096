import type { ApiFunc } from 'axios'
import { authRequest } from '../utils.api'
import type {
  AddSysFunctionauthInput,
  AddSysNavigationInput,
  DeleteSysFunctionauthInput,
  DeleteSysNavigationInput,
  GetRightSysNavigationForShowOutput,
  GetSysFunctionauthForPageInput,
  GetSysFunctionauthForPageOutput,
  GetSysFunctionauthForUpdateInput,
  GetSysFunctionauthForUpdateOutput,
  GetSysNavigationForPageInput,
  GetSysNavigationForPageOutput,
  GetSysNavigationForUpdateInput,
  GetSysNavigationForUpdateOutput,
  UpdateSysFunctionauthInput,
  UpdateSysNavigationInput,
} from '../dto/system/navigation.dto'
import { NAVIGATION_TYPE_ENUM } from '../dto/navigation.dto'

export const navigationTypeOptions = [
  { label: '目录', value: NAVIGATION_TYPE_ENUM.DIR },
  { label: '页面', value: NAVIGATION_TYPE_ENUM.PAGE },
  { label: '控件', value: NAVIGATION_TYPE_ENUM.CONTROL },
]

/**获取菜单tree */
export const getRightSysNavigationForShowApi: ApiFunc<undefined, GetRightSysNavigationForShowOutput> = (options) => {
  return authRequest({ url: 'getRightSysNavigationForShow', ...options })
}

/**获取菜单分页 */
export const getSysNavigationForPageApi: ApiFunc<GetSysNavigationForPageInput, GetSysNavigationForPageOutput> = (
  data,
  options
) => {
  return authRequest({ url: 'getSysNavigationForPage', data, ...options })
}

/**获取菜单项详情 */
export const getSysNavigationForUpdateApi: ApiFunc<GetSysNavigationForUpdateInput, GetSysNavigationForUpdateOutput> = (
  data,
  options
) => {
  return authRequest({ url: 'getSysNavigationForUpdate', data, ...options })
}

/**新增菜单项 */
export const addSysNavigationApi: ApiFunc<AddSysNavigationInput, undefined> = (data, options) => {
  return authRequest({ url: 'addSysNavigation', data, ...options })
}

/**更新菜单项详情 */
export const updateSysNavigationApi: ApiFunc<UpdateSysNavigationInput, undefined> = (data, options) => {
  return authRequest({ url: 'updateSysNavigation', data, ...options })
}

/**删除菜单项 */
export const deleteSysNavigationApi: ApiFunc<DeleteSysNavigationInput, undefined> = (data, options) => {
  return authRequest({ url: 'deleteSysNavigation', data, ...options })
}

/**获取菜单按钮权限列表 */
export const getSysFunctionauthForPageApi: ApiFunc<GetSysFunctionauthForPageInput, GetSysFunctionauthForPageOutput> = (
  data,
  options
) => {
  return authRequest({ url: 'getSysFunctionauthForPage', data, ...options })
}

/**添加菜单按钮权限 */
export const addSysFunctionauthApi: ApiFunc<AddSysFunctionauthInput, undefined> = (data, options) => {
  return authRequest({ url: 'addSysFunctionauth', data, ...options })
}

/**获取菜单按钮权限详情 */
export const getSysFunctionauthForUpdateApi: ApiFunc<
  GetSysFunctionauthForUpdateInput,
  GetSysFunctionauthForUpdateOutput
> = (data, options) => {
  return authRequest({ url: 'getSysFunctionauthForUpdate', data, ...options })
}

/**编辑菜单按钮权限 */
export const updateSysFunctionauthApi: ApiFunc<UpdateSysFunctionauthInput, undefined> = (data, options) => {
  return authRequest({ url: 'updateSysFunctionauth', data, ...options })
}

/**删除菜单按钮权限 */
export const deleteSysFunctionauthApi: ApiFunc<DeleteSysFunctionauthInput, undefined> = (data, options) => {
  return authRequest({ url: 'deleteSysFunctionauth', data, ...options })
}
