import { defineStore } from 'pinia'
import type { RouteRecordRaw } from 'vue-router'

export enum DEVICE {
  /**移动端 */
  MOBILE = 'mobile',
  /**桌面端 */
  DESKTOP = 'desktop',
}

interface LayoutStoreState {
  /**菜单列表 */
  menuList: RouteRecordRaw[]
  /**菜单是否折叠 */
  menuIsCollapse: boolean
  /**设备 */
  device: DEVICE
}

export const useLayoutStore = defineStore('layoutStore', {
  state(): LayoutStoreState {
    return {
      menuList: [],
      menuIsCollapse: false,
      device: DEVICE.DESKTOP,
    }
  },

  getters: {
    isMobile(state) {
      return state.device === DEVICE.MOBILE
    },
  },

  actions: {},
})
