/**
 * 出院结算退费 - 银行卡退费记录
 */

/**
 * 审核状态
 */
export enum BankCardRefundAuditStatus {
  /* 待审核 */
  PENDING = 0,
  /* 审核通过 */
  APPROVED = 1,
  /* 审核不通过 */
  REJECTED = 2
}

// 审核状态映射配置
export const BankCardRefundAuditStatusConfig = {
  [BankCardRefundAuditStatus.PENDING]: { label: '待审核', tagType: 'warning' },
  [BankCardRefundAuditStatus.APPROVED]: { label: '审核通过', tagType: 'success' },
  [BankCardRefundAuditStatus.REJECTED]: { label: '审核不通过', tagType: 'danger' },
  default: { label: '-', tagType: 'info' }
}

/**
 * 银行卡退费记录 - 状态 可选项
 */
export const BankCardRefundAuditStatusOptions = Object.entries(BankCardRefundAuditStatusConfig)
  .filter(([key]) => key !== 'default')
  .map(([value, config]) => ({
    label: config.label,
    value: parseInt(value)
  }))

/**
 * 银行卡退费记录 列表请求
 */
export interface ReqGetBankCardRefundRecordsByPage {
  /* 当前页 */
  page: number
  /* 每页的记录数 */
  rows: number
  /* 住院号 */
  patientNo?: string
  /* 住院人姓名 */
  patientName?: string
  /* 状态 */
  status?: number
}

/**
 * 银行卡退费记录 列表响应
 */
export interface ResBankCardRefundRecordsByPage {
  data: BankCardRefundRecordsItem[] // 实体列表
  pageIndex: number // 当前页
  pageSize: number // 每页记录数
  recordCount: number // 总记录数
  pageCount: number // 总页数
}

/**
 * 出院结算退费 - 银行卡退费记录 - 列表 - 项
 */
export interface BankCardRefundRecordsItem {
  id: string
  /* 住院人姓名 */
  patientName: string
  /* 住院号 */
  patientNo: string
  /* 住院科室 */
  patientDept: string
  /* 入院时间 */
  patientInTime: string
  /* 出院时间 */
  patientOutTime: string
  /* 费用总金额（元） */
  totalAmount: number
  /* 医保金额（元） */
  medicalInsuranceAmount: number
  /* 已交押金金额（元） */
  depositAmount: number
  /* 结算退费金额（元） */
  refundAmount: number
  /* 开户名 */
  accountName: string
  /* 开户行 */
  accountBank: string
  /* 银行卡号 */
  accountCardNo: string
  /* 预留号码 */
  accountPhone: string
  /* 状态 */
  status: number
}


/**
 * 银行卡退费记录 - 详情 - 请求
 */
export interface ReqBankCardRefundRecordInfo {
  /* 退费记录 ID */
  refundId: string
}

/**
 * 银行卡退费记录 - 详情 - 响应
 */
export interface ResBankCardRefundRecordInfo extends BankCardRefundRecordsItem {
  /* 主治医生 */
  doctorName: string
  /* 住院床号 */
  bedNo: string
  /* 自费金额 */
  selfPayAmount: number
  /* 创建时间 */
  createTime: string
  /** 流转信息 */
  resCirculationInfoDTOList: RefundCirculationInfoItem[]
}


/**
 * 流转信息 item
 */
export interface RefundCirculationInfoItem {
  /* 流转状态 */
  status: number
  /** 流转时间 */
  createTime: string
  /** 流转描述 */
  content: string
}

