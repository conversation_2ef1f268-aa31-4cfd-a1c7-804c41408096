<script lang="tsx" setup>
import { getVerify<PERSON>odeApi, loginApi } from "@/api/auth.api";
import type { LoginInput } from "@/api/dto/auth.dto";
import { useFormHook } from "@/hooks/useForm";
import { useLoadingHook } from "@/hooks/useLoading";
import { appMessage } from "@/hooks/useNaiveApi";
import { ElMessageBox, type FormRules } from "element-plus";
import { useUserStore } from "@/stores/user.store";
import { jwtDecode } from "jwt-decode";
import { sha256 } from "js-sha256";
import { ROUTER_PATH } from "@/router/router-path";
import { JWT_K3, type PangeJavaJwtData } from "@/types";
import { closeLoading } from "@/utils/axios-utils/axios-loading";
import { useEditPasswordHook } from "@/hooks/useEditPassword";
import { storeToRefs } from "pinia";
import { useGlobalStore } from "@/stores/global.store";

const route = useRoute();
const router = useRouter();
const { appConfig } = storeToRefs(useGlobalStore());

const { formRef: loginFormRef, validate } = useFormHook();

const userStore = useUserStore();

const loginForm = reactive<LoginInput>({
  account: "",
  password: "",
  code: "",
});
const loginRules = reactive<FormRules<LoginInput>>({
  account: [{ required: true, message: "请输入账号", trigger: "blur" }],
  password: [{ required: true, message: "请输入密码", trigger: "blur" }],
  code: [{ required: true, message: "请输入验证码", trigger: "blur" }],
});

const { loading: verifyLoading, loadingFunc: verifyLoadingFunc } = useLoadingHook();

/**验证码图片 */
const verifyImg = ref("");
/**验证码Token */
const { captchaimagetoken } = storeToRefs(userStore);
/**获取验证码 */
const getVerifyCode = async () => {
  try {
    const data = await getVerifyCodeApi({
      handleHeadersDataCallback(config) {
        captchaimagetoken.value = config.headers.captchaimagetoken;
      },
      loading: verifyLoadingFunc,
    });
    verifyImg.value = `data:image/png;base64,${data}`;
  } catch (error) {
    verifyImg.value = "";
  }
};

onMounted(() => {
  closeLoading();
  getVerifyCode();
});

const { loading: loginLoading } = useLoadingHook();

/**登录 */
const handleLogin = async () => {
  if (!verifyImg.value) {
    const message = verifyLoading.value ? "正在获取验证码" : "请先获取验证码";
    appMessage.warning(message);
    return;
  }
  try {
    await validate();
    const data = await loginApi(
      {
        ...loginForm,
        password: sha256(loginForm.password),
      },
      {
        setHeadersDataCallback(config) {
          config.headers.captchaimagetoken = captchaimagetoken.value;
        },
        loadingOptions: { text: "正在登录，请稍后..." },
      }
    );

    userStore.$patch((state) => {
      state.userInfo = data;
      const managerInfo = jwtDecode<PangeJavaJwtData>(data.authToken);
      state.managerInfo = {
        tokenExpTime: managerInfo.exp * 1000,
        uid: managerInfo.k1,
        account: managerInfo.k2,
        isAdmin: managerInfo.k3 === JWT_K3.ADMIN ? true : false,
      };
    });
    if (route.query.redirect) {
      await router.push(route.query.redirect as string);
    } else {
      await router.push(ROUTER_PATH.HOME);
    }

    // 如果有提示则显示提示
    if (data.expireTip) {
      await ElMessageBox.alert(data.expireTip);

      // 如果提示需要修改密码，需要强制修改密码
      if (/.*修改.*密码.*/.test(data.expireTip)) {
        await useEditPasswordHook({ force: true });
      }
    }
  } catch (error) {
    getVerifyCode();
    loginForm.code = "";
  }
};
</script>

<template>
  <div class="login-container">
    <el-form v-loading="loginLoading" ref="loginFormRef" :model="loginForm" :rules="loginRules">
      <div class="system-name">{{ appConfig.appTitle }}</div>

      <el-form-item prop="account">
        <el-input v-model="loginForm.account" size="large" placeholder="请输入账号" @keydown.enter="handleLogin">
          <template #prefix>
            <svg-icon name="svg-login-user"></svg-icon>
          </template>
        </el-input>
      </el-form-item>
      <el-form-item prop="password">
        <el-input
          v-model="loginForm.password"
          type="password"
          size="large"
          placeholder="请输入密码"
          show-password
          @keydown.enter="handleLogin"
        >
          <template #prefix>
            <svg-icon name="svg-login-password"></svg-icon>
          </template>
        </el-input>
      </el-form-item>

      <el-form-item prop="code">
        <el-input
          v-model="loginForm.code"
          size="large"
          placeholder="请输入验证码"
          class="code-input"
          @keydown.enter="handleLogin"
        >
          <template #prefix>
            <svg-icon name="svg-login-verify-code"></svg-icon>
          </template>
        </el-input>

        <el-tooltip content="点击刷新验证码">
          <img
            v-show="verifyImg"
            v-loading="verifyLoading"
            :src="verifyImg"
            class="verify-code code-img cursor"
            @click="getVerifyCode"
          />
        </el-tooltip>
        <el-button
          v-show="!verifyImg"
          :loading="verifyLoading"
          type="primary"
          class="verify-code"
          @click="getVerifyCode"
        >
          获取验证码</el-button
        >
      </el-form-item>

      <el-form-item>
        <el-button type="primary" class="handle-button" @click="handleLogin">登录</el-button>
      </el-form-item>
    </el-form>

    <div class="illustrate">
      <span>{{ appConfig.appTitle }}©版权所有</span>
      <el-divider direction="vertical" />
      <span>技术支持：佛山市佛盈盘古信息科技有限公司</span>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.login-container {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100vw;
  height: 100vh;
  background: url(@/assets/image/login/login_bg.png) no-repeat center center;
  background-size: 100% 100%;

  :deep(.el-form) {
    box-sizing: border-box;
    width: 480px;
    padding: 32px 30px;
    background: var(--el-bg-color-overlay);
    border-radius: 4px;
    .system-name {
      margin-bottom: 28px;
      font-weight: bold;
      font-size: 20px;
      text-align: center;
      letter-spacing: 0.1em;
      line-height: 1.4em;
      color: var(--el-color-primary-dark-2);
    }

    .el-form-item {
      margin-bottom: 20px;
      &:last-child {
        margin-bottom: 0;
      }
    }

    .el-form-item__content {
      flex-wrap: nowrap;
    }
    .code-input {
      flex-grow: 1;
      width: auto;
    }

    .verify-code {
      flex-shrink: 0;
      width: 130px;
      height: 40px;
      margin-left: 10px;
      border-radius: 4px;
      &.code-img {
        background: var(--el-color-white);
      }
    }

    .handle-button {
      width: 100%;
      height: 44px;
      font-size: 18px;
    }
  }

  .illustrate {
    position: absolute;
    bottom: 48px;
    font-size: 14px;
    color: #898e97;
    :deep(.el-divider) {
      border-color: #898e97;
    }
  }
}
</style>
