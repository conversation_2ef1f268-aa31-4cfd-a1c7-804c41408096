<script lang="ts" setup>
import { storeToRefs } from 'pinia'
import SidebarItem from './sidebar-item.vue'
import { useLayoutStore } from '@/stores/layout.store'
import { ROUTER_PATH } from '@/router/router-path'
import { useGlobalStore } from '@/stores/global.store'

const route = useRoute()

const baseStyle = useCssModule()

const { appConfig } = storeToRefs(useGlobalStore())

const layoutStore = useLayoutStore()

const { menuList, menuIsCollapse, isMobile } = storeToRefs(layoutStore)

const mentWidth = computed(() => {
  if (isMobile.value) {
    return menuIsCollapse.value ? 0 : baseStyle.sideBarWidth
  } else {
    return menuIsCollapse.value ? baseStyle.sideBarCollapseWidth : baseStyle.sideBarWidth
  }
})

const defaultActive = computed(() => {
  return route.meta.activeMenu || route.path
})
</script>

<template>
  <div :class="baseStyle['layout_menu-container']">
    <div class="logo" @click="$router.push(ROUTER_PATH.HOME)">
      <XyzTransition xyz="fade small">
        <div v-if="!menuIsCollapse" class="logo-title">{{ appConfig.appTitle }}</div>
      </XyzTransition>
    </div>

    <el-scrollbar>
      <el-menu
        :default-active="defaultActive"
        :collapse="!isMobile && menuIsCollapse"
        :collapse-transition="false"
        :background-color="baseStyle.menuBg"
        :text-color="baseStyle.menuText"
        :active-text-color="baseStyle.menuActiveText"
      >
        <template v-for="item in menuList">
          <SidebarItem
            v-if="!item.hidden && item.children?.filter((item) => !item.hidden).length"
            :key="item.path"
            :item="item"
          />
        </template>
      </el-menu>
    </el-scrollbar>
  </div>
</template>

<style lang="scss" module>
.layout_menu-container {
  position: fixed;
  width: v-bind(mentWidth);
  height: 100vh;
  background-color: $menuBg;
  transition: width 0.28s;
  box-shadow: -8px 6px 19px 0px rgb(0 0 0 / 50%);
  z-index: 1001;
}
</style>

<style lang="scss" scoped>
.logo {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 56px;
  color: $menuText;
  white-space: nowrap;
  cursor: pointer;
  .logo-img {
    width: 40px;
    height: 40px;
    border-radius: 50%;
  }

  .logo-title,
  .logo-img {
    padding: 0 20px;
    white-space: break-spaces;
    text-align: center;
    color: $subMenuActiveText;
    &.xyz-out {
      position: absolute;
    }
  }
}

:deep(.el-scrollbar) {
  height: calc(100% - 56px);
}

:deep(.el-menu) {
  border: none;
  .el-sub-menu > .el-sub-menu__title:hover {
    background-color: $menuHover !important;
  }
  .el-sub-menu.is-active > .el-sub-menu__title {
    color: $subMenuActiveText !important;
  }
}
</style>
