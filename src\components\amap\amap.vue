<script lang="ts" setup>
import { useGlobalStore } from '@/stores/global.store'
import { useAMap } from '@/hooks/useAMap'
import { storeToRefs } from 'pinia'

const mapContainerRef = ref<HTMLDivElement>()

const loading = ref(true)

const { isDark } = storeToRefs(useGlobalStore())

const style = ref('normal')

const mapStyle = computed(() => `amap://styles/${style.value}`)

// const map = useAMap(mapContainerRef, () => (loading.value = false), { mapStyle: mapStyle.value, viewMode: '3D' })
const map = useAMap(mapContainerRef, () => (loading.value = false), { mapStyle: mapStyle.value, rotateEnable: true })

watchEffect(() => {
  if (map.value) {
    if (isDark.value) style.value = 'dark'
    else style.value = 'normal'

    map.value?.setMapStyle(mapStyle.value)
  }
})

watch(isDark, () => {
  loading.value = true
  setTimeout(() => {
    loading.value = false
  }, 500)
})

defineExpose({ map })
</script>

<template>
  <div ref="mapContainerRef" v-loading="loading" class="amap_container"></div>
</template>

<style lang="scss" scoped>
.amap_container {
  width: 100%;
  height: 100%;
}
</style>
