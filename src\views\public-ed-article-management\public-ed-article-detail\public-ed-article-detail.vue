<script setup lang="ts">
import { useFormHook } from '@/hooks/useForm.ts'
import { ROUTER_PATH } from '@/router/router-path'
import type { UploadUserFile } from 'element-plus'

const { formRef } = useFormHook()

const router = useRouter()

const uploadFileList = ref<UploadUserFile[]>([])

const formData = reactive({
  articleTitle: '',
  department: '',
  articleType: '1',
  isPublish: false,
  carousel: '',
  sortCode: 0,
  textarea: ''
})

watch(uploadFileList, (val) => {
  formData.carousel = val[0]?.url ?? ''
  if (formData.carousel) {
    // 单独触发验证图片验证，去除可能存在的校验错误信息提示
    formRef.value!.validateField('carousel')
  }
})

const isVideoType = computed(() => {
  return formData.articleType === '2'
})

function handlePublic() {
  console.log('确定发布', formData)
}

function handleBack() {
  router.push({
    path: ROUTER_PATH.PUBLIC_ED_ARTICLE_LIST
  })
}
</script>

<template>
  <div class="public-ed-article_details-container">
    <div class="back-bar flex">
      <el-button type="primary" @click="handlePublic">确定发布</el-button>
      <el-button type="warning" @click="handleBack">返回</el-button>
    </div>

    <div class="content-container">
      <el-form ref="formRef" size="large" label-width="auto" label-position="right">
        <el-form-item label="标题：" prop="articleTitle" required show-message>
          <el-input v-model="formData.articleTitle" placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item label="住院科室：" prop="articleTitle" required>
          <el-select class="w-680" v-model="formData.department">
            <el-option label="全部" value=""></el-option>
            <el-option label="已启用" :value="true"></el-option>
            <el-option label="未启用" :value="false"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="文章类型：" prop="articleTitle" required>
          <el-select class="w-680" v-model="formData.articleType">
            <el-option label="图文类型" value="1"></el-option>
            <el-option label="视频类型" value="2"></el-option>
          </el-select>
        </el-form-item>
        <template v-if="isVideoType">
          <el-form-item label="视频号ID：" prop="articleTitle" required>
            <div style="display: flex; flex-wrap: wrap">
              <el-input class="w-680" v-model="formData.articleTitle" placeholder="请输入"></el-input>
              <span class="tips">视频号 id，以“sph”开头的id，可在视频号助手获取</span>
            </div>
          </el-form-item>
          <el-form-item label="视频ID：" prop="articleTitle" required>
            <el-input class="w-680" v-model="formData.articleTitle" placeholder="请输入"></el-input>
            <span class="tips">视频 feedId，用于视频的正常跳转，请正确输入</span>
          </el-form-item>
        </template>
        <el-form-item label="是否发布：" prop="isPublish" required>
          <el-switch v-model="formData.isPublish" />
          <span class="tips">不发布，内容暂存于后台</span>
        </el-form-item>
        <el-form-item label="轮缩略图：" prop="carousel" required>
          <CommonImgUpload
            v-model="uploadFileList"
            tips="建议上传大小300*200（3:2）像素的jpg/png格式图片，且不超过200kb"
            width="180px"
            :limit="1"
            :max-file-size="200"
            :file-type="['image/jpeg', 'image/png']"
          />
        </el-form-item>

        <el-form-item label="排序：" prop="slideshow">
          <el-input v-model="formData.sortCode" class="w-680" type="number"></el-input>
          <span class="tips">主页轮播排序，数字越小排序越前，默认以时间排序</span>
        </el-form-item>

        <el-form-item v-if="!isVideoType" label="宣教内容：" prop="textarea">
          <CommonArticleEditor v-model="formData.textarea" />
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.public-ed-article_details-container {
  .content-container {
    padding: 20px;

    :deep(.el-form .el-form-item) {
      align-items: flex-start !important;
    }

    .w-680 {
      width: 680px;
    }

    .tips {
      margin-left: 12px;
      font-size: 14px;
      color: #909399;
    }
  }
}
</style>
