<script lang="ts" setup>
import screenfull from 'screenfull'

const isFullscreen = ref(false)

const handleFullScreen = () => {
  if (screenfull.isEnabled) {
    screenfull.toggle()
  }
}

const changeStatus = () => {
  isFullscreen.value = screenfull.isFullscreen
}

const init = () => {
  if (screenfull.isEnabled) {
    screenfull.on('change', changeStatus)
  }
}

const destroy = () => {
  if (screenfull.isEnabled) {
    screenfull.off('change', changeStatus)
  }
}

onMounted(init)

onUnmounted(destroy)
</script>

<template>
  <div class="screenfull-container">
    <el-tooltip content="全屏" placement="bottom">
      <XyzTransition :xyz="`fade ${isFullscreen ? 'small' : 'big'}`">
        <svg-icon v-if="isFullscreen" name="svg-exit-fullscreen" class="screentfull-icon" @click="handleFullScreen" />
        <svg-icon v-else name="svg-fullscreen" class="screentfull-icon" @click="handleFullScreen" />
      </XyzTransition>
    </el-tooltip>
  </div>
</template>

<style lang="scss" scoped>
.screenfull-container {
  display: flex;
  align-items: center;
}
.screentfull-icon {
  font-size: 24px;
  color: var(--el-text-color-primary);
  cursor: pointer;
  &.xyz-out {
    position: absolute;
  }
}
</style>
