import type { ApiFunc } from 'axios'
import { authRequest } from '../utils.api'
import type {
  getSysDataAuditInfoLogForPageInput,
  getSysDataAuditInfoLogForPageOutput,
} from '../dto/system/audit-log.dto'

/**操作日志分页 */
export const getSysDataAuditInfoLogForPageApi: ApiFunc<
  getSysDataAuditInfoLogForPageInput,
  getSysDataAuditInfoLogForPageOutput
> = (data, options) => {
  return authRequest({ url: 'getSysDataAuditInfoLogForPage', data, ...options })
}
