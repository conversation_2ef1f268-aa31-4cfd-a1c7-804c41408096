/**
 * 出院结算办理 - 出院清账结算
 */

/**
 * 出院结算办理 - 出院清账结算 - 病人费用列表
 */
export interface PatientBillList {
  accountsInfoList: PatientBillItem[]
  /* 开始日期 */
  startDate: string

  /* 结束日期 */
  endDate: string

  /* 待清金额 */
  pendingAmount: number
}

/**
 * 出院结算办理 - 出院清账结算 - 病人费用 item
 */
export interface PatientBillItem {
  /* 期数 */
  datenum: string

  /* 结算日期 */
  billdate: string

  /* 病区名称 */
  ward: string

  /* 本期费用 */
  totalamount: number

  /* 累计费用 */
  totalamountadd: number
}

/**
 * 结构费用 Item
 */
export interface StructureCostItem {
  /* 费用明细 id */
  detailsn: string

  /* 结构名称 */
  costtype: string

  /* 金额 */
  totalAmount: number

  /* 打折优惠前金额 */
  amountBeforeDiscountOffer: number
}

/**
 * 按期清账-费用结构请求DTO
 */
export interface ReqSettleAnAccountByPhaseOfCostStructureDTO {
  /* 住院号 */
  inpno: string

  /* 结算日期列表 */
  billdateList: string[]
}

/**
 * 全部清账-费用结构请求DTO
 */
export interface ReqSettleAnAccountByAllOfCostStructureDTO {
  /* 住院号 */
  inpno: string
}

/**
 * 明细清账-费用结构请求DTO
 */
export interface ReqSettleAnAccountByDetailsOfCostStructureDTO {
  /* 住院号 */
  inpno: string

  /* 费用明细id列表 */
  detailsnList: string[]
}

/**
 * 清账结算-费用结构响应DTO
 */
export interface ResSettleAnAccountOfCostStructureDTO {
  /* 费用信息列表 */
  expenseInfoList: StructureCostItem[]

  /* 合计项 */
  amountToTerm: number

  /* 合计金额 */
  amountToYuan: number
}

/**
 * 清账结算-确认选中的押金预收单号的金额请求DTO
 */
export interface ReqCheckTheDepositAmountDTO {
  /* 预收单号列表 */
  prepayidList: string[]

  /* 住院号 */
  inpno: string

  /* 病人号 */
  patno: string
}

/**
 * 清账结算-确认选中的押金预收单号的金额响应DTO
 */
export interface ResCheckTheDepositAmountDTO {
  /* 费用总金额 */
  totalCostAmount: number

  /* 冲销预交人民币 */
  writeOffPrepaidRMB: number

  /* 补收人民币 */
  supplementRMB: number

  /* 发票日期开始 */
  invoiceStartDate: string

  /* 发票日期结束 */
  invoiceEndDate: string

  /* 是否补收 true补收false退款 */
  isSupplement: boolean

  /* 选择病区列表 */
  deptList: string[]
}

/**
 * 清账结算-清账金额-发票号请求DTO
 */
export interface ReqClearingAmountOfInvoiceNumberDTO {
  /* 住院号 */
  inpno: string

  /* 费用明细id */
  details: string[] | undefined
}

/**
 * 清账结算-清账金额-发票号响应DTO
 */
export interface ResClearingAmountOfInvoiceNumberDTO {
  /* 发票号 */
  invoiceno: string
}
