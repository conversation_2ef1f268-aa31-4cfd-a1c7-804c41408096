export enum NAVIGATION_TYPE_ENUM {
  /**菜单 */
  DIR = 'dir',
  /**页面 */
  PAGE = 'page',
  /**控件 */
  CONTROL = 'control',
}

/**获取管理页面左则导航菜单入参 */
export interface GetNavigationMenuTreeListInput {
  /**用户主键 */
  managerID: string
}

/**用户路由操作按钮权限 */
export type NavigationMenuItemExtraData = {
  // 公共
  functionAuthName: string
  encode: string

  /**C# */
  resultCode: string
  /**C# */
  resultMsg: string
  /**C# */
  elementClass?: string
  /**C# */
  functionAuthID: string
  /**C# */
  area: string
  /**C# */
  controller: string
  /**C# */
  action: string
  /**C# */
  createTime: string

  /**java */
  checkMark: boolean
  /**java */
  functionAuthId: string
  /**java */
  url: string
}

/**用户路由 */
export type NavigationMenuItem = {
  // 公共
  navigationName: string
  iconUrl: string
  linkUrl: string

  /**C# */
  childrenNavigations?: NavigationMenuItem[]
  /**C# */
  extraData: NavigationMenuItemExtraData[]
  /**C# */
  navigationID: string
  /**C# */
  mainID: string
  /**C# */
  parentID: string
  /**C# */
  layer: number
  /**C# */
  isLock: number
  /**C# */
  title?: string
  /**C# */
  subTitle?: string
  /**C# */
  sortCode: number
  /**C# */
  removeMark: number
  /**C# */
  enabledMark: number
  /**C# */
  isThirdParty: boolean
  /**C# */
  thirdPartyURL?: string

  /**java */
  authList: NavigationMenuItemExtraData[]
  /**java */
  checkMark: boolean
  /**java */
  classLayer: number
  /**java */
  expanded: 'true' | 'false'
  /**java */
  isLeaf: 'true' | 'false'
  /**java */
  mainId: number
  /**java */
  navigationId: string
  /**java */
  navigationType: NAVIGATION_TYPE_ENUM
  /**java */
  parentId: number
}

/**获取管理页面左则导航菜单出参 */
export type GetNavigationMenuTreeListOutput = NavigationMenuItem[]
