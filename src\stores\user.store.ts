import { refreshTokenApi } from "@/api/auth.api";
import type { LoginOutput } from "@/api/dto/auth.dto";
import type { GetNavigationMenuTreeListOutput } from "@/api/dto/navigation.dto";
import { getRouter_Java } from "@/api/navigation.api";
import { appMessage } from "@/hooks/useNaiveApi";
import { JWT_K3, type PangeJavaJwtData } from "@/types";
import { loadRoutes } from "@/utils/router-utils";
import { jwtDecode } from "jwt-decode";
import { defineStore } from "pinia";

export type ManagerInfo = {
  /**token过期时间 */
  tokenExpTime: number;
  /**账号主键 */
  uid: string;
  /**账号key(账号名字、用户名) */
  account: string;
  /**是否为管理员 */
  isAdmin: boolean;
};

export interface UseUserStoreState {
  /**图片token */
  captchaimagetoken: string | null;
  /**用户登录信息 */
  userInfo: LoginOutput;
  /**账号信息 */
  managerInfo: ManagerInfo | null;
  /**用户权限路由 */
  userRouter: GetNavigationMenuTreeListOutput | null;
}

/**登录用户默认信息 */
const defaultUserInfo = (): UseUserStoreState["userInfo"] => {
  return {
    authToken: "",
    refreshAuthToken: "",
    expireTip: "",
  };
};

export const useUserStore = defineStore("userStore", {
  state: (): UseUserStoreState => {
    return {
      captchaimagetoken: null,
      userInfo: defaultUserInfo(),
      managerInfo: null,
      userRouter: null,
    };
  },

  actions: {
    /**初始化登录用户信息 */
    initUserInfo() {
      this.userInfo = defaultUserInfo();
      this.managerInfo = null;
    },

    /**初始化登录用户信息 */
    initUserRouter() {
      this.userRouter = null;
    },

    /**获取动态路由 */
    async getAsyncRouter() {
      const routerData = await getRouter_Java({ showNoData: false });
      this.userRouter = routerData;
      loadRoutes();
    },

    /**刷新token */
    async resetToken() {
      try {
        const data = await refreshTokenApi(
          {
            managerId: this.managerInfo!.uid,
            refreshAuthToken: this.userInfo.refreshAuthToken,
          },
          { loading: false, mute: true }
        );

        this.$patch((state) => {
          state.userInfo.authToken = data.authToken;
          state.userInfo.refreshAuthToken = data.refreshAuthToken;

          const managerInfo = jwtDecode<PangeJavaJwtData>(data.authToken);
          state.managerInfo = {
            tokenExpTime: managerInfo.exp * 1000,
            uid: managerInfo.k1,
            account: managerInfo.k2,
            isAdmin: managerInfo.k3 === JWT_K3.ADMIN ? true : false,
          };
        });
      } catch (error: any) {
        this.initUserInfo();
        this.initUserRouter();

        const errorMessage = error.resultMsg || '未知错误，请重新登录'

        appMessage.error(errorMessage);

        return Promise.reject(errorMessage);
      }
    },
  },

  persist: {
    // enabled: true,
    // strategies: [{ storage: sessionStorage, paths: ["userInfo", "managerInfo"] }],
    storage: sessionStorage,
    pick: ['userInfo', 'managerInfo']
  },
});
