<script setup lang="ts">
import PatientFeePanel from './patient-fee-panel.vue'
import DepositPaymentInfoPanel from './deposit-payment-info-panel.vue'
import type { ResExpenseInfoDTO, ResDepositPaymentInfoDTO } from '@/api/dto/discharge-settlement/patient-accounts.dto'
import { requestPatientExpenseInfo, requestDepositPaymentInfo } from '@/api/discharge-settlement.api'

/* 病人费用信息 & 押金缴交信息 */

const props = defineProps<{
  // 住院号
  inpno: string | undefined
}>()

enum PanelType {
  PanelPatientFee = 'panel-patient-fee',
  PanelDepositPaymentInfo = 'panel-deposit-payment-info'
}

const panelType = ref<PanelType>(PanelType.PanelPatientFee)

// 病人费用信息
const patientFee = ref<ResExpenseInfoDTO>({} as ResExpenseInfoDTO)

// 押金缴交信息
const depositPaymentInfoList = ref<ResDepositPaymentInfoDTO[]>([] as ResDepositPaymentInfoDTO[])

watch(
  () => props.inpno,
  (newVal) => {
    if (!newVal) {
      console.error('住院号不能为空')
      return
    }
    patientFee.value = {} as ResExpenseInfoDTO
    depositPaymentInfoList.value = []
    getPatientFee(newVal)
    getDepositPaymentInfoList(newVal)
  },
  { immediate: true }
)

// 获取病人费用信息
async function getPatientFee(inpno: string) {
  if (!inpno) {
    console.error('住院号不能为空')
    return
  }
  const res = await requestPatientExpenseInfo({
    inpno
  })
  patientFee.value = res
}

// 获取押金缴交信息
async function getDepositPaymentInfoList(inpno: string) {
  if (!inpno) {
    console.error('住院号不能为空')
    return
  }
  const res = await requestDepositPaymentInfo({
    inpno
  })
  depositPaymentInfoList.value = res
}
</script>

<template>
  <el-radio-group v-model="panelType" class="panel-radio-group">
    <el-radio-button :value="PanelType.PanelPatientFee">病人费用信息</el-radio-button>
    <el-radio-button :value="PanelType.PanelDepositPaymentInfo">押金缴交信息</el-radio-button>
  </el-radio-group>

  <div class="panel-container">
    <PatientFeePanel
      v-show="panelType === PanelType.PanelPatientFee"
      :prevent-shortcut="panelType === PanelType.PanelDepositPaymentInfo"
      :inpno="props.inpno"
      :patient-fee="patientFee"
    />
    <DepositPaymentInfoPanel
      v-show="panelType === PanelType.PanelDepositPaymentInfo"
      :deposit-payment-info-list="depositPaymentInfoList"
    />
  </div>
</template>

<style scoped lang="scss">
.panel-radio-group {
  margin: 30px 0 20px;
}
</style>
