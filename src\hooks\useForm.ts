import type { FormInstance, FormValidateCallback } from 'element-plus'
/**el表单Hook */
export const useFormHook = () => {
  const formRef = ref<FormInstance | null>(null)

  const validate = (callback?: FormValidateCallback) => {
    if (!formRef.value) return
    return formRef.value.validate(callback)
  }

  const resetForm = () => {
    if (!formRef.value) return
    formRef.value.resetFields()
  }

  return { formRef, validate, resetForm }
}
