import {
  ReqDeclarationApplyFileByPage,
  ReqDeclarationApplyFileDetail,
  ResDeclarationApplyFileByPage
} from '@/api/dto/declaration-data-management.dto.ts'
import { request } from '@/utils/axios-utils'
import type { ApiFunc } from 'axios'

/**
 * 申报资料管理 模块
 */

/** 申报资料 列表 */
export const requestDeclarationApplyFileByPage: ApiFunc<
  ReqDeclarationApplyFileByPage,
  ResDeclarationApplyFileByPage
> = (data) => {
  return request({
    url: '/foying/web/ApplyFile/getApplyFileByPage',
    method: 'post',
    data,
    retonly: false
  })
}

/** 申报资料 详情 */
export const requestDeclarationApplyFileDetail: ApiFunc<ReqDeclarationApplyFileDetail, string[]> = (data) => {
  return request({
    url: '/foying/web/ApplyFile/checkApplyFileInfo',
    method: 'post',
    data
  })
}
