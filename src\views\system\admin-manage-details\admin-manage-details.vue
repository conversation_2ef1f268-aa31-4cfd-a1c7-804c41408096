<script lang="ts" setup>
import ChooseUserPicker from '@/components/choose-user-picker'
import { useFormHook } from '@/hooks/useForm'
import { appMessage } from '@/hooks/useNaiveApi'
import { ROUTER_PATH } from '@/router/router-path'
import { validatorPassword } from '@/utils'
import {
  addSysManagerApi,
  getManagerGroupsForAddOrUpdateApi,
  getOrgsForAddOrUpdateApi,
  getRolesForAddOrUpdateApi,
  getSysManagerForEditApi,
  updateSysManagerApi,
} from '@/api/system/admin-manage.api'
import type { FormRules } from 'element-plus'
import type { AddSysManagerInput, ManagerGroupsModel, OrgsModel, RolesModel } from '@/api/dto/system/admin-manage.dto'
import type { RuleItem } from 'async-validator'
import { sha256 } from 'js-sha256'

type FormData = Omit<
  AddSysManagerInput,
  | 'forbiddenStartDate'
  | 'forbiddenEndDate'
  | 'forbiddenStartTime'
  | 'forbiddenEndTime'
  | 'managergroupIds'
  | 'orgIds'
  | 'roleIds'
> & {
  checkPassword: string
  userGroupList: ManagerGroupsModel[]
  organizationList: OrgsModel[]
  roleList: RolesModel[]
  forbiddenDate: [string, string]
  forbiddenTime: [string, string]
}

const route = useRoute()
const router = useRouter()

const managerId = computed(() => route.query.managerId as string | undefined)

const { formRef, validate } = useFormHook()

const formData = reactive<FormData>({
  account: '',
  password: '',
  checkPassword: '',
  encode: '',
  userGroupList: [],
  organizationList: [],
  roleList: [],
  forbiddenDate: ['', ''],
  forbiddenTime: ['', ''],
  enabledMark: true,
  isSys: false,
  sortCode: 0,
  realName: '',
  telephone: '',
  mobile: '',
  email: '',
  qicqno: '',
  weChat: '',
  gender: '',
  description: '',
})
const validatorCheckPassword: RuleItem['validator'] = (_, value, callback) => {
  if (value === '') {
    callback(new Error('请再次输入密码'))
  } else if (value !== formData.password) {
    callback(new Error('两次输入密码不一致!'))
  } else {
    callback()
  }
}
/**表单验证规则 */
const rules = reactive<FormRules<FormData>>({
  account: [{ required: true, message: '请输入账号', trigger: 'blur' }],
  password: [{ required: !managerId.value, validator: validatorPassword, trigger: 'blur' }],
  checkPassword: [{ required: !managerId.value, validator: validatorCheckPassword, trigger: 'blur' }],
  encode: [{ required: true, message: '请输入编码', trigger: 'blur' }],
  roleList: [{ required: true, message: '请选择角色', trigger: 'blur' }],
  realName: [{ required: true, message: '请输入用户名', trigger: 'blur' }],
})

/**选择器数据 */
const pickerData = reactive({
  userGroupList: [] as FormData['userGroupList'],
  organizationList: [] as FormData['organizationList'],
  roleList: [] as FormData['roleList'],
})

const getManagerGroupsForAddOrUpdate = async () => {
  try {
    pickerData.userGroupList = await getManagerGroupsForAddOrUpdateApi({ showNoData: true })
  } catch (error) {
    pickerData.userGroupList = []
  }
}

const getOrgsForAddOrUpdate = async () => {
  try {
    pickerData.organizationList = await getOrgsForAddOrUpdateApi()
  } catch (error) {
    pickerData.organizationList = []
  }
}

const getRolesForAddOrUpdate = async () => {
  try {
    pickerData.roleList = await getRolesForAddOrUpdateApi()
  } catch (error) {
    pickerData.roleList = []
  }
}

onMounted(() => {
  getManagerGroupsForAddOrUpdate()
  getOrgsForAddOrUpdate()
  getRolesForAddOrUpdate()
})

/**点击返回 */
const handleBack = () => {
  router.push(ROUTER_PATH.ADMIN_MANAGE)
}

const getSysManagerForEdit = async () => {
  try {
    const res = await getSysManagerForEditApi({ id: managerId.value! })
    formData.account = res.account
    formData.password = res.password
    formData.encode = res.encode

    formData.userGroupList = res.managergroups || []
    formData.organizationList = res.orgs || []
    formData.roleList = res.roles || []

    if (res.forbiddenStartDate && res.forbiddenEndDate) {
      formData.forbiddenDate = [res.forbiddenStartDate, res.forbiddenEndDate]
    }
    if (res.forbiddenStartTime && res.forbiddenEndTime) {
      formData.forbiddenTime = [res.forbiddenStartTime, res.forbiddenEndTime]
    }

    formData.enabledMark = res.enabledMark
    formData.isSys = res.isSys

    formData.sortCode = res.sortCode

    formData.realName = res.realName
    formData.telephone = res.telephone
    formData.mobile = res.mobile

    formData.email = res.email
    formData.qicqno = res.qicqno
    formData.weChat = res.weChat

    formData.gender = res.gender
    formData.description = res.description
  } catch (error) {
    handleBack()
  }
}

onMounted(() => {
  managerId.value && getSysManagerForEdit()
})

/**点击保存 */
const handleSave = async () => {
  try {
    await validate()

    const payload = {
      account: formData.account,
      password: sha256(formData.password),
      encode: formData.encode,

      managergroupIds: formData.userGroupList.map((item) => item.managerGroupId).join(','),
      orgIds: formData.organizationList.map((item) => item.organizationId).join(','),
      roleIds: formData.roleList.map((item) => item.roleId).join(','),

      forbiddenStartDate: formData.forbiddenDate?.[0] || '',
      forbiddenEndDate: formData.forbiddenDate?.[1] || '',
      forbiddenStartTime: formData.forbiddenTime?.[0] || '',
      forbiddenEndTime: formData.forbiddenTime?.[1] || '',

      enabledMark: formData.enabledMark,
      isSys: formData.isSys,

      sortCode: formData.sortCode,

      realName: formData.realName,
      telephone: formData.telephone,
      mobile: formData.mobile,

      email: formData.email,
      qicqno: formData.qicqno,
      weChat: formData.weChat,

      gender: formData.gender,
      description: formData.description,
    }

    if (managerId.value) {
      // 修改
      await updateSysManagerApi({ ...payload, managerId: managerId.value })
      appMessage.success('编辑成功')
    } else {
      // 新增
      await addSysManagerApi(payload)
      appMessage.success('新增成功')
    }

    handleBack()
  } catch (error) {}
}
</script>

<template>
  <div class="admin_manage_details-container">
    <div class="back-bar flex">
      <el-button type="primary" @click="handleSave">确定{{ managerId ? '保存' : '新增' }}</el-button>
      <el-button type="warning" @click="handleBack">返回</el-button>
    </div>

    <el-form ref="formRef" :model="formData" :rules="rules" label-width="110" class="layout-page-pd">
      <el-form-item label="账号:" prop="account">
        <el-input
          v-model="formData.account"
          clearable
          class="inputClass"
          placeholder="请输入账号名称"
          :disabled="!!managerId"
        ></el-input>
      </el-form-item>

      <el-form-item v-if="!managerId" label="登录密码:" prop="password">
        <el-input
          v-model="formData.password"
          type="password"
          clearable
          class="inputClass"
          placeholder="请输入登录密码"
          show-password
          :disabled="!!managerId"
        ></el-input>
      </el-form-item>

      <el-form-item v-if="!managerId" label="确定密码:" prop="checkPassword">
        <el-input
          v-model="formData.checkPassword"
          type="password"
          clearable
          class="inputClass"
          placeholder="请再次输入密码"
          show-password
        ></el-input>
      </el-form-item>

      <el-form-item label="内部编码:" prop="encode">
        <el-input v-model="formData.encode" clearable class="inputClass" placeholder="请输入内部编码"></el-input>
      </el-form-item>

      <el-form-item label="所属用户组:" prop="userGroupList">
        <ChooseUserPicker
          v-model="formData.userGroupList"
          :data="pickerData.userGroupList"
          selection-key="managerGroupId"
          :async-search="false"
        >
          <template #tag="{ item }">
            <span>{{ item.managerGroupName }} </span>
          </template>

          <el-table-column type="selection" />
          <el-table-column label="用户名" prop="managerGroupName" />
        </ChooseUserPicker>
      </el-form-item>

      <el-form-item label="所属部门:" prop="organizationList">
        <ChooseUserPicker
          v-model="formData.organizationList"
          :data="pickerData.organizationList"
          selection-key="organizationId"
          :async-search="false"
        >
          <template #tag="{ item }">
            <span>{{ item.organizationName }} </span>
          </template>

          <el-table-column type="selection" />
          <el-table-column label="用户名" prop="organizationName" />
        </ChooseUserPicker>
      </el-form-item>

      <el-form-item label="关联角色:" prop="roleList">
        <ChooseUserPicker
          v-model="formData.roleList"
          :data="pickerData.roleList"
          selection-key="roleId"
          :async-search="false"
        >
          <template #tag="{ item }">
            <span>{{ item.roleName }} </span>
          </template>

          <el-table-column type="selection" />
          <el-table-column label="用户名" prop="roleName" />
        </ChooseUserPicker>
      </el-form-item>

      <el-form-item label="禁止登陆日期:" prop="forbiddenDate">
        <el-date-picker
          v-model="formData.forbiddenDate"
          value-format="YYYY-MM-DD"
          type="daterange"
          align="right"
          unlink-panels
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        >
        </el-date-picker>
      </el-form-item>

      <el-form-item label="禁止登陆时间:" prop="forbiddenTime">
        <el-time-picker
          v-model="formData.forbiddenTime"
          value-format="HH:mm:ss"
          is-range
          range-separator="至"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
          placeholder="选择时间范围"
        >
        </el-time-picker>
      </el-form-item>

      <el-form-item label="是否启用：" prop="enabledMark">
        <el-switch v-model="formData.enabledMark" active-text="启用后账号才能正常登录"> </el-switch>
      </el-form-item>

      <el-form-item label="系统管理：" prop="isSys">
        <el-switch v-model="formData.isSys"> </el-switch>
      </el-form-item>

      <el-form-item label="排序：" prop="sortCode">
        <el-input-number v-model="formData.sortCode" controls-position="right" :min="0"></el-input-number>
      </el-form-item>

      <el-row>
        <el-col :sm="24" :md="24" :lg="8">
          <el-form-item label="用户名:" prop="realName">
            <el-input
              v-model="formData.realName"
              clearable
              class="inputClass-small"
              placeholder="请输入用户名"
            ></el-input>
          </el-form-item>
        </el-col>

        <el-col :sm="24" :md="24" :lg="8">
          <el-form-item label="手机号码:" prop="telephone">
            <el-input
              v-model="formData.telephone"
              clearable
              class="inputClass-small"
              placeholder="请输入手机号码"
            ></el-input>
          </el-form-item>
        </el-col>

        <el-col :sm="24" :md="24" :lg="8">
          <el-form-item label="座机号码:" prop="mobile">
            <el-input
              v-model="formData.mobile"
              clearable
              class="inputClass-small"
              placeholder="请输入手机号码"
            ></el-input>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row>
        <el-col :sm="24" :md="24" :lg="8">
          <el-form-item label="邮箱:">
            <el-input v-model="formData.email" clearable class="inputClass-small" placeholder="请输入邮箱"></el-input>
          </el-form-item>
        </el-col>

        <el-col :sm="24" :md="24" :lg="8">
          <el-form-item label="QQ号码:">
            <el-input
              v-model="formData.qicqno"
              clearable
              class="inputClass-small"
              placeholder="请输入QQ号码"
            ></el-input>
          </el-form-item>
        </el-col>

        <el-col :sm="24" :md="24" :lg="8">
          <el-form-item label="微信:">
            <el-input
              v-model="formData.weChat"
              clearable
              class="inputClass-small"
              placeholder="请输入微信号"
            ></el-input>
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item label="性别:">
        <el-radio v-model="formData.gender" label="1">男</el-radio>
        <el-radio v-model="formData.gender" label="2">女</el-radio>
      </el-form-item>

      <el-form-item label="备注:">
        <el-input
          v-model="formData.description"
          :autosize="{ minRows: 3, maxRows: 4 }"
          type="textarea"
          class="textareaClass"
          placeholder="请输入"
        ></el-input>
      </el-form-item>
    </el-form>
  </div>
</template>

<style lang="scss" scoped></style>
