/* Element Plus 自定义变量
 * 在此文件中覆盖 Element Plus 的默认变量
 * 参考文档: https://element-plus.org/zh-CN/component/button.html#自定义主题
 */

:root {
  // 主题色
  --el-color-primary: #1890ff;
  --el-color-primary-light-1: #42a5ff;
  --el-color-primary-light-2: #66b1ff;
  --el-color-primary-light-3: #85c1ff;
  --el-color-primary-light-4: #a3d0ff;
  --el-color-primary-light-5: #c2e0ff;
  --el-color-primary-light-6: #d9ecff;
  --el-color-primary-light-7: #ecf5ff;
  --el-color-primary-light-8: #f5f9ff;
  --el-color-primary-light-9: #f8faff;
  --el-color-primary-dark-1: #1670cc;

  // 成功色
  --el-color-success: #67c23a;
  // 警告色
  --el-color-warning: #e6a23c;
  // 危险色
  --el-color-danger: #f56c6c;
  // 信息色
  --el-color-info: #909399;

  // 文字颜色
  --el-text-color-primary: #303133;
  --el-text-color-regular: #606266;
  --el-text-color-secondary: #909399;
  --el-text-color-placeholder: #a8abb2;
  --el-text-color-disabled: #c0c4cc;

  // 边框颜色
  --el-border-color: #dcdfe6;
  --el-border-color-light: #e4e7ed;
  --el-border-color-lighter: #ebeef5;
  --el-border-color-extra-light: #f2f6fc;
  --el-border-color-primary: #1890ef;

  // 背景颜色
  --el-bg-color: #ffffff;
  --el-bg-color-page: #f2f3f5;
  --el-bg-color-overlay: #ffffff;

  // 禁用状态
  --el-disabled-bg-color: #f5f7fa;
  --el-disabled-text-color: #c0c4cc;
  --el-disabled-border-color: #e4e7ed;

  // 圆角
  --el-border-radius-base: 4px;
  --el-border-radius-small: 2px;
  --el-border-radius-round: 20px;
  --el-border-radius-circle: 100%;

  // 字体大小
  --el-font-size-extra-large: 20px;
  --el-font-size-large: 18px;
  --el-font-size-medium: 16px;
  --el-font-size-base: 14px;
  --el-font-size-small: 13px;
  --el-font-size-extra-small: 12px;
}

// 暗黑模式
.dark {
  // 暗黑模式的变量覆盖
  --el-color-primary: #1890ff;
  --el-bg-color: #141414;
  --el-bg-color-page: #0a0a0a;
  --el-text-color-primary: #ffffff;
  // ... 其他暗黑模式变量
}
