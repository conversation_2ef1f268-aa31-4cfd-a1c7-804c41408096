<script setup lang="ts">
/**
 * 医保登记管理 - 医保登记详情
 */
import { ResInsuranceRegistrationInfo } from '@/api/dto/insurance-registration.dto.ts'
import {
  requestInsuranceRegistrationFailed,
  requestInsuranceRegistrationInfo,
  requestInsuranceRegistrationSuccessful
} from '@/api/insurance-registration.api.ts'
import { appMessage } from '@/hooks/useNaiveApi.ts'
import { ROUTER_PATH } from '@/router/router-path.ts'
import FailureReasonDialog from '@/views/insurance-registration/detail/components/failure-reason-dialog.vue'
import RegistrationInfo from '@/views/insurance-registration/detail/components/registration-info.vue'

const router = useRouter()
const route = useRoute()

// 已登记表格数据
const detailInfo = ref<ResInsuranceRegistrationInfo>({} as ResInsuranceRegistrationInfo)

const failureReasonDialogVisible = ref(false)

let _insuranceregistrationId = ''

onMounted(() => {
  console.log('医保登记列表传入 -> 详情：', route.query)
  if (!route.query.insuranceregistrationId) {
    console.error('获取医保登记详情失败：未获取到医保登记 ID')
    appMessage.error('获取医保登记详情失败：医保登记 ID 为空')
    return
  }
  _insuranceregistrationId = route.query.insuranceregistrationId as string
  requestDetailData()
})

async function requestDetailData() {
  try {
    detailInfo.value = await requestInsuranceRegistrationInfo({
      insuranceregistrationId: _insuranceregistrationId
    })
    console.log('医保登记详情：', detailInfo.value)
  } catch (e) {
    console.error('获取医保登记详情失败：', e)
  }
}

function handleBack() {
  router.push({
    path: ROUTER_PATH.INSURANCE_REGISTRATION_LIST
  })
}

// 登记成功
async function handleRegistrationSuccessful() {
  try {
    await requestInsuranceRegistrationSuccessful({
      insuranceregistrationId: _insuranceregistrationId
    })
    console.log('医保登记 成功')
    await ElMessageBox({
      title: '提示',
      type: 'success',
      message: '状态已变更为 登记成功',
      callback: () => {
        requestDetailData()
      }
    })
  } catch (e) {
    console.error('医保登记 失败：', e)
  }
}

// 登记失败
async function handleRegistrationFailed() {
  console.log('点击 登记失败')
  failureReasonDialogVisible.value = true
}

// 登记失败原因弹窗 - 点击确认
async function handleConfirmFailureReason({ reason, done }: { reason: string; done: (flag?: boolean) => void }) {
  console.log('登记失败原因', reason)

  try {
    await requestInsuranceRegistrationFailed({
      insuranceregistrationId: _insuranceregistrationId,
      failureReason: reason
    })
    console.log('已登记失败')
    // 关闭弹窗，并且停止确认按钮的 loading
    done()
    await ElMessageBox({
      title: '提示',
      type: 'success',
      message: '状态已变更为 登记失败',
      callback: () => {
        requestDetailData()
      }
    })
  } catch (e) {
    console.error('已登记失败 报错：', e)
    done(true)
  }
}
</script>

<template>
  <div class="page-container">
    <div class="header">
      <el-button class="header-back-btn" type="warning" @click="handleBack">返回</el-button>
    </div>

    <RegistrationInfo
      :detail-info="detailInfo"
      @confirm="handleRegistrationSuccessful"
      @reject="handleRegistrationFailed"
    />

    <FailureReasonDialog
      v-model="failureReasonDialogVisible"
      :name="detailInfo.admissionName ?? ''"
      :hospitalization-number="detailInfo.admissionNo ?? ''"
      @confirm="handleConfirmFailureReason"
    />
  </div>
</template>

<style scoped lang="scss">
.page-container {
  height: calc(100vh - 50px - 34px);
  display: flex;
  flex-direction: column;
}

.header {
  height: 50px;
  background: #f4f4f5;
  display: flex;
  justify-content: right;
  align-items: center;
  padding: 0 20px;
  position: sticky;
  flex: none;
  z-index: 2;

  & .header-back-btn {
    width: 68px;
    height: 36px;
  }
}
</style>
