import type {
  GetSysManagerPageByRoleIdInput,
  GetSysManagerPageByRoleIdOutput,
  SysManagerPageByRoleIdModel,
  SysRoleByPageItemModel,
} from '@/api/dto/system/roles-manage.dto'
import { getSysManagerPageByRoleIdApi } from '@/api/system/roles-manage.api'
import { usePaginationHook } from '@/hooks/usePagination'
import { ElInput, ElButton, ElTable, ElTableColumn, ElTag, ElLoading } from 'element-plus'
import BasePagination from '@/components/base-pagination'
import { useLoadingHook } from '@/hooks/useLoading'

export type OpenParam = SysRoleByPageItemModel & {}

export type DialogData = Omit<GetSysManagerPageByRoleIdInput, 'page' | 'rows'> & {
  tableData: GetSysManagerPageByRoleIdOutput
}

const dialogData = reactive<DialogData>({
  roleId: '',
  keyword: '',
  tableData: [],
})

const { currentPage, pageSize, total } = usePaginationHook(5)

const { loading, loadingFunc } = useLoadingHook()

/**获取已经绑定账号列表 */
const getSysManagerPageByRoleId = async () => {
  try {
    const { data, recordCount } = await getSysManagerPageByRoleIdApi(
      {
        roleId: dialogData.roleId,
        keyword: dialogData.keyword,
        page: currentPage.value,
        rows: pageSize.value,
      },
      { retonly: false, loading: loadingFunc, showNoData: false }
    )
    dialogData.tableData = data
    total.value = recordCount!
  } catch (error) {
    dialogData.tableData = []
    total.value = 1
  }
}

/**点击搜索 */
const handleSearch = () => {
  currentPage.value = 1
  getSysManagerPageByRoleId()
}

watch([currentPage, pageSize], () => {
  getSysManagerPageByRoleId()
})

const BodyCompoment = defineComponent({
  directives: { loading: ElLoading.directive },

  setup() {
    return () => (
      <>
        <div class="search-bar flex">
          <ElInput v-model={dialogData.keyword} placeholder="关键字搜索" style={{ width: '240px' }}></ElInput>
          <ElButton type="primary" class="ml-6" onClick={handleSearch}>
            搜索
          </ElButton>
        </div>

        <ElTable v-loading={loading.value} data={dialogData.tableData} border height="300" class="mt-10">
          <ElTableColumn prop="account" label="账号"></ElTableColumn>
          <ElTableColumn prop="realName" label="用户名"></ElTableColumn>
          <ElTableColumn label="是否启用">
            {{
              default: ({ row }: { row: SysManagerPageByRoleIdModel }) => (
                <ElTag type={row.enabledMark ? 'success' : 'danger'}>{row.enabledMark ? '启用' : '停用'}</ElTag>
              ),
            }}
          </ElTableColumn>
        </ElTable>

        <BasePagination
          v-model:current-page={currentPage.value}
          v-model:page-size={pageSize.value}
          total={total.value}
        />
      </>
    )
  },
})

export const useVisitBindUserDialog = async (openParam: OpenParam) => {
  dialogData.roleId = openParam.roleId

  await getSysManagerPageByRoleId()

  await ElMessageBox({
    title: `${openParam.roleName} - 已关联的账号`,
    message: () => <BodyCompoment></BodyCompoment>,
    customClass: 'visit_bind_user_dialog',
    customStyle: { width: '600px', maxWidth: 'none', maxHeight: '560px', overflow: 'auto' },
    closeOnHashChange: true,
    showCancelButton: false,
    showConfirmButton: false,
  })

  dialogData.keyword = ''
}
