import { useAxiosStore } from '@/stores/axios.store'
import axios, { type InternalAxiosRequestConfig } from 'axios'
import { isFunction } from 'lodash-es'
import { serviceLoading } from './axios-loading'
import { useUserStore } from '@/stores/user.store'
import router from '@/router'
import { ROUTER_PATH } from '@/router/router-path'
import { appMessage } from '@/hooks/useNaiveApi'

let resetTokenLock = false

/**添加AuthToken */
export const addAuthToken = (config: InternalAxiosRequestConfig, userStore: ReturnType<typeof useUserStore>) => {
  config['headers']['authenticationToken'] = `${userStore.userInfo.authToken}`
}

/**请求拦截器成功回调 */
export async function successRequest(config: InternalAxiosRequestConfig) {
  // 优先调用接口定义的设置请求头回调函数
  config.setHeadersDataCallback?.(config)

  const axiosStore = useAxiosStore()

  /**用于判断是否为重复的请求 */
  let hasQueue = false
  config.cancelToken = new axios.CancelToken(function executor(callback) {
    //本次axios请求的配置添加cancelToken
    hasQueue = axiosStore.addAxiosQueue({ config, callback })
  })

  /**相同请求不再进行后续处理 */
  if (hasQueue) return config

  //设置请求loading.
  if (isFunction(config.loading)) {
    config.loading(true)
  } else if (config.loading) {
    serviceLoading(config.loadingOptions)
  }

  const userStore = useUserStore()
  const { tokenExpTime } = userStore.managerInfo || {}

  if (tokenExpTime) {
    const now = Date.now()
    // 判断token是否过期
    const isTokenExpired = tokenExpTime < now

    if (isTokenExpired) {
      console.warn('token过期')
      userStore.initUserInfo()
      userStore.initUserRouter()
      axiosStore.removeAxiosPending(config)
      router.push({ path: ROUTER_PATH.LOGIN, query: { redirect: router.currentRoute.value.fullPath } })
      appMessage.error('登录已过期，请重新登录')
    }
  }

  // 如果接口不需要校验token可以直接返回
  if (!config.checkAuthToken) return config

  addAuthToken(config, userStore)

  return config
}

/**请求拦截器错误回调 */
export function errorRequest(error: any) {
  return error
}
