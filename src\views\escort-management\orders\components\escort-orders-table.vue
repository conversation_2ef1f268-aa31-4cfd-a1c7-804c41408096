<script setup lang="ts">
/**
 * 留人陪护申请管理 - 服务订单管理
 */
import {
  HospitalSitterItem,
  ReqGetHospitalSitterByPage,
  EscortOrderStatusOptions,
  EscortOrderStatus,
  EscortOrderStatusConfig
} from '@/api/dto/escort-management.dto.ts'
import { useMountDialog } from '@/hooks/useMountDialog.ts'
import EscortConfirmServiceDialog from '@/views/escort-management/orders/components/escort-confirm-service-dialog.vue'
import { toRaw } from 'vue'
import { formatPrice, maskIdCard } from '@/utils/index.ts'

enum ItemActionType {
  /* 详情 */
  DETAIL = 'detail',
  /* 确定服务 */
  SERVICE = 'service',
  /* 确定归还 */
  RETURN = 'return'
}

const emits = defineEmits<{
  (e: 'search', data: { currentPage: number; pageSize: number; searchData: ReqGetHospitalSitterByPage }): void
  (e: 'toDetail', data: HospitalSitterItem): void
  (
    e: 'confirmService',
    data: {
      orderItem: HospitalSitterItem
      selfLiftingCode: string
    },
    resolve: () => void,
    reject: () => void
  ): void
  (e: 'confirmReturn', data: HospitalSitterItem): void
}>()

defineProps<{
  listData: HospitalSitterItem[]
  total: number
}>()

const { open: openConfirmServiceDialog } = useMountDialog(EscortConfirmServiceDialog)

// 搜索表单
const searchFormData = reactive<ReqGetHospitalSitterByPage>({} as ReqGetHospitalSitterByPage)

// 分页
const paginationData = reactive({
  currentPage: 1,
  pageSize: 10
})

onMounted(() => {
  // 默认加载页面时搜索
  emitSearch()
})

// 搜索
function handleSearch() {
  paginationData.currentPage = 1
  emitSearch()
}

// 重置
function handleReset() {
  searchFormData.platformOrderNo = ''
  searchFormData.patientName = ''
  searchFormData.patientIdNo = ''
  searchFormData.orderStatus = undefined
  emitSearch()
}

function emitSearch() {
  emits('search', {
    currentPage: paginationData.currentPage,
    pageSize: paginationData.pageSize,
    searchData: toRaw(searchFormData)
  })
}

function handleItemAction(itemAction: ItemActionType, itemData: HospitalSitterItem) {
  switch (itemAction) {
    case ItemActionType.SERVICE:
      openConfirmServiceDialog({
        name: itemData.patientName,
        hospitalizationNumber: itemData.patno ?? '-',
        confirmCallback: (selfLiftingCode: string) => {
          return new Promise<void>((resolve, reject) => {
            // 父组件可能是异步操作，把 resolve 和 reject 传给子组件，子组件可以调用
            emits('confirmService', { orderItem: itemData, selfLiftingCode }, resolve, reject)
          })
        }
      })
      break
    case ItemActionType.RETURN:
      emits('confirmReturn', itemData)
      break
    case ItemActionType.DETAIL:
      emits('toDetail', itemData)
      break
    default:
      break
  }
}

function handleSizeChange() {
  // 切换每页条数时把当前页重置回 1
  paginationData.currentPage = 1
  emitSearch()
}

function handleCurrentChange() {
  emitSearch()
}

defineExpose({
  handleSearch
})
</script>

<template>
  <div class="page-container">
    <el-form inline :model="searchFormData">
      <el-form-item label="平台单号：">
        <el-input
          v-model="searchFormData.platformOrderNo"
          class="input-container"
          placeholder="请输入"
          size="large"
          clearable
        />
      </el-form-item>
      <el-form-item label="住院人姓名：">
        <el-input
          v-model="searchFormData.patientName"
          class="input-container"
          placeholder="请输入"
          size="large"
          clearable
        />
      </el-form-item>
      <el-form-item label="证件号：">
        <el-input
          v-model="searchFormData.patientIdNo"
          class="input-container"
          placeholder="请输入"
          size="large"
          clearable
        />
      </el-form-item>
      <el-form-item label="状态：">
        <el-select
          v-model="searchFormData.orderStatus"
          class="input-container"
          placeholder="请选择"
          size="large"
          clearable
        >
          <el-option
            v-for="item in EscortOrderStatusOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button v-auth.disabled="'HospitalSitterByPage'" size="large" type="primary" @click="handleSearch">
          搜索
        </el-button>
        <el-button v-auth.disabled="'HospitalSitterByPage'" size="large" @click="handleReset">重置</el-button>
      </el-form-item>
    </el-form>

    <BaseTable class="table-container" :data="listData" border width="100%" height="550">
      <el-table-column prop="platformOrderNo" label="平台单号" width="220" fixed="left" show-overflow-tooltip />
      <el-table-column prop="patientName" label="住院人姓名" width="200" />
      <el-table-column prop="patientIdNo" label="证件号" width="200">
        <template #default="scope">
          {{ maskIdCard(scope.row.patientIdNo) }}
        </template>
      </el-table-column>
      <el-table-column prop="department" label="住院科室" width="200" />
      <el-table-column prop="serverTime" label="服务时间" width="200" />
      <el-table-column prop="orderStatus" label="状态" width="200">
        <template #default="scope">
          <el-tag
            :type="EscortOrderStatusConfig[scope.row.orderStatus]?.tagType || EscortOrderStatusConfig.default.tagType"
          >
            {{ EscortOrderStatusConfig[scope.row.orderStatus]?.label || EscortOrderStatusConfig.default.label }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="payFee" label="订单金额（元）" width="200">
        <template #default="scope">
          {{ formatPrice(scope.row.payFee) }}
        </template>
      </el-table-column>
      <el-table-column prop="createTime" label="下单时间" width="200" />

      <el-table-column label="操作" fixed="right" min-width="280">
        <template #default="scope">
          <el-button
            v-auth.disabled="'HospitalSitterInfo'"
            size="small"
            @click="handleItemAction(ItemActionType.DETAIL, scope.row)"
            >详情
          </el-button>
          <el-button
            v-if="scope.row.orderStatus === EscortOrderStatus.RESERVE"
            v-auth.disabled="'HospitalSitterConfirmService'"
            size="small"
            type="success"
            @click="handleItemAction(ItemActionType.SERVICE, scope.row)"
            >确定租赁
          </el-button>
          <el-button
            v-if="scope.row.orderStatus === EscortOrderStatus.RETURN"
            v-auth.disabled="'HospitalSitterConfirmReturn'"
            size="small"
            type="primary"
            @click="handleItemAction(ItemActionType.RETURN, scope.row)"
            >确定归还
          </el-button>
        </template>
      </el-table-column>
    </BaseTable>

    <base-pagination
      v-model:current-page="paginationData.currentPage"
      v-model:page-size="paginationData.pageSize"
      :total="total"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />
  </div>
</template>

<style scoped lang="scss">
:deep(.el-form--inline .el-form-item) {
  margin-right: 24px;
}

.page-container {
  padding: 20px 0;
}

.input-container {
  width: 190px;
}

.table-container {
  margin: 12px 0 30px;
}
</style>
