<template>
  <component :is="type" v-bind="linkProps()">
    <slot></slot>
  </component>
</template>

<script lang="ts" setup>
import { isExternal } from '@/utils'

const props = defineProps<{
  to: string
  item: any
}>()

const type = computed(() => {
  if (isExternal(props.to) && !props.item.meta.isAppView) {
    return 'a'
  } else {
    return 'router-link'
  }
})

const linkProps = () => {
  if (type.value === 'a') {
    return {
      href: props.to,
      target: '_blank',
      rel: 'noopener',
    }
  } else {
    return {
      to: props.to,
    }
  }
}
</script>

<style lang="scss" scoped>
a {
  text-decoration: none;
}
</style>
