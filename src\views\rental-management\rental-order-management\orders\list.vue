<script setup lang="ts">
/**
 * 租赁订单管理 - 列表
 */
import {
  CreateRentalOrderInfo,
  RentalOrderItem,
  RentalOrderStatus,
  ReqRentalOrdersByPage
} from '@/api/dto/rental-management/rental-order-management.dto.ts'
import {
  requestCreateInpatientCaregiverOrder,
  requestGetInpatientCaregiverByPage,
  requestServiceCompleted,
  requestConfirmLease,
  requestModifyReview,
  requestPersonnelReplacementReview,
  requestInpatientCaregiverCancelOrder
} from '@/api/rental-management.api.ts'
import { ROUTER_PATH } from '@/router/router-path.ts'
import RentalOrdersTable from '@/views/rental-management/rental-order-management/orders/components/rental-orders-table.vue'

// Tab 枚举
enum TabEnum {
  ALL = 'all',
  MODIFY_REQUESTS = 'modifyRequests',
  CHANGE_ESCORT_REQUESTS = 'changeEscortRequests'
}

// ref
const allOrdersTableRef = ref<InstanceType<typeof RentalOrdersTable>>()
const modifyRequestsTableRef = ref<InstanceType<typeof RentalOrdersTable>>()
const changeEscortRequestsTableRef = ref<InstanceType<typeof RentalOrdersTable>>()

const router = useRouter()

// 为每个标签页创建独立的数据源
const allOrdersData = reactive({
  listData: [] as RentalOrderItem[],
  total: 0
})

const modifyRequestsData = reactive({
  listData: [] as RentalOrderItem[],
  total: 0
})

const changeEscortRequestsData = reactive({
  listData: [] as RentalOrderItem[],
  total: 0
})

// 当前激活的标签页
const activeTab = ref(TabEnum.ALL)

// 根据标签页加载对应的数据
const fetchTabData = async (
  e: { currentPage: number; pageSize: number; searchData: ReqRentalOrdersByPage },
  tabName = activeTab.value
) => {
  switch (tabName) {
    case TabEnum.ALL:
      await fetchAllOrdersList(e)
      break
    case TabEnum.MODIFY_REQUESTS:
      await fetchModifyRequestsList(e)
      break
    case TabEnum.CHANGE_ESCORT_REQUESTS:
      await fetchChangeEscortRequestsList(e)
      break
  }
}

// 各个标签页的数据获取函数
const fetchAllOrdersList = async (e: { currentPage: number; pageSize: number; searchData: ReqRentalOrdersByPage }) => {
  try {
    const params = { ...e.searchData, page: e.currentPage, rows: e.pageSize }
    const response = await requestGetInpatientCaregiverByPage(params)
    allOrdersData.listData = response.data
    allOrdersData.total = response.recordCount
  } catch (error) {
    console.error('获取全部订单列表失败：', error)
    ElMessage.error('获取全部订单列表失败')
  }
}

const fetchModifyRequestsList = async (e: {
  currentPage: number
  pageSize: number
  searchData: ReqRentalOrdersByPage
}) => {
  try {
    const params = {
      ...e.searchData,
      orderStatus: RentalOrderStatus.RESERVED,
      tabNo: 0, // 订单修改 tab 时传入 0
      page: e.currentPage,
      rows: e.pageSize
    }
    const response = await requestGetInpatientCaregiverByPage(params)
    modifyRequestsData.listData = response.data
    modifyRequestsData.total = response.recordCount
  } catch (error) {
    console.error('获取订单修改申请列表失败：', error)
    ElMessage.error('获取订单修改申请列表失败')
  }
}

const fetchChangeEscortRequestsList = async (e: {
  currentPage: number
  pageSize: number
  searchData: ReqRentalOrdersByPage
}) => {
  try {
    const params = {
      ...e.searchData,
      orderStatus: RentalOrderStatus.ESCORTING,
      page: e.currentPage,
      rows: e.pageSize
    }
    const response = await requestGetInpatientCaregiverByPage(params)
    changeEscortRequestsData.listData = response.data
    changeEscortRequestsData.total = response.recordCount
  } catch (error) {
    console.error('获取换人申请列表失败：', error)
    ElMessage.error('获取换人申请列表失败')
  }
}

// 因为每个 tab 的搜索方法不一样，所以需要根据 tab 类型触发不同的搜索方法，同时使用其内部的搜索变量值
function fetchTabDataByTabRef(tabName: TabEnum = activeTab.value) {
  switch (tabName) {
    case TabEnum.ALL:
      allOrdersTableRef.value?.emitSearch()
      break
    case TabEnum.MODIFY_REQUESTS:
      modifyRequestsTableRef.value?.emitSearch()
      break
    case TabEnum.CHANGE_ESCORT_REQUESTS:
      changeEscortRequestsTableRef.value?.emitSearch()
      break
  }
}

// 确定租赁
async function handleConfirmService(
  data: {
    /* ID */
    inpatientCaregiverId: string
    /* 陪护人ID */
    inpatientCaregiverPersonId: string
  },
  resolve: () => void,
  reject: () => void
) {
  console.log('确定租赁 弹窗，点击确认：', data)
  try {
    await requestConfirmLease({
      inpatientCaregiverId: data.inpatientCaregiverId,
      inpatientCaregiverPersonId: data.inpatientCaregiverPersonId
    })

    console.log('确定租赁成功')

    // 调用 resolve 来关闭弹窗
    resolve()

    await ElMessageBox({
      title: '提示',
      type: 'success',
      message: '确定租赁成功',
      callback: () => {
        fetchTabDataByTabRef()
      }
    })
  } catch (e) {
    console.error('确定租赁失败：', e)
    reject()
  }
}

// 换人审核
async function handleReplacement(
  data: {
    inpatientCaregiverId: string
    inpatientCaregiverPersonId: string
    reviewOperate: boolean
  },
  resolve: () => void,
  reject: () => void
) {
  console.log('换人审核 弹窗，点击确认：', data)
  try {
    await requestPersonnelReplacementReview({
      inpatientCaregiverId: data.inpatientCaregiverId,
      inpatientCaregiverPersonId: data.inpatientCaregiverPersonId,
      reviewOperate: data.reviewOperate
    })

    // 调用 resolve 来关闭弹窗
    resolve()

    await ElMessageBox({
      title: '提示',
      type: 'success',
      message: '换人审核成功',
      callback: () => {
        fetchTabDataByTabRef()
      }
    })
  } catch (e) {
    console.error('换人审核失败：', e)
    reject()
  }
}

// 修改审核
async function handleModifyAudit(
  data: {
    inpatientCaregiverUpdateId: string
    inpatientCaregiverId: string
    reviewOperate: boolean
  },
  resolve: () => void,
  reject: () => void
) {
  console.log('修改审核 弹窗，点击确认：', data)
  try {
    await requestModifyReview({
      inpatientCaregiverUpdateId: data.inpatientCaregiverUpdateId,
      inpatientCaregiverId: data.inpatientCaregiverId,
      reviewOperate: data.reviewOperate
    })

    // 调用 resolve 来关闭弹窗
    resolve()

    await ElMessageBox({
      title: '提示',
      type: 'success',
      message: '修改审核成功',
      callback: () => {
        fetchTabDataByTabRef()
      }
    })
  } catch (e) {
    console.error('修改审核失败：', e)
    reject()
  }
}

// 取消订单
async function handleCancelOrder(data: { inpatientCaregiverId: string }) {
  ElMessageBox.confirm('确定取消订单吗？')
    .then(async () => {
      console.log('取消订单：', data)
      try {
        await requestInpatientCaregiverCancelOrder({ inpatientCaregiverId: data.inpatientCaregiverId })
        ElMessage.success('取消订单成功')
        fetchTabDataByTabRef()
      } catch (error) {
        console.error('取消订单失败：', error)
        ElMessage.error('取消订单失败')
      }
    })
    .catch(() => {
      // catch error
    })
}

// 服务完成
function handleFinishedService(data: { inpatientCaregiverId: string }) {
  ElMessageBox.confirm('确定服务完成吗？')
    .then(async () => {
      try {
        await requestServiceCompleted({ inpatientCaregiverId: data.inpatientCaregiverId })
        ElMessage.success('服务完成操作成功')
        fetchTabDataByTabRef()
      } catch (error) {
        console.error('服务完成操作失败：', error)
        ElMessage.error('服务完成操作失败')
      }
    })
    .catch(() => {
      // catch error
    })
}

// 详情
function handleToDetailPage(orderItem: RentalOrderItem) {
  console.log('详情：', orderItem)
  router.push({
    path: ROUTER_PATH.RENTAL_ORDER_DETAIL,
    query: { id: orderItem.inpatientCaregiverId }
  })
}

// 创建订单
async function handleCreateOrder(
  data: {
    createData: CreateRentalOrderInfo
  },
  resolve: () => void,
  reject: () => void
) {
  console.log('创建订单 弹窗，点击确认：', data)
  try {
    await requestCreateInpatientCaregiverOrder(data.createData)
    // 调用 resolve 来关闭弹窗
    resolve()

    await ElMessageBox({
      title: '提示',
      type: 'success',
      message: '创建订单成功',
      callback: () => {
        fetchTabDataByTabRef()
      }
    })
  } catch (e) {
    console.error('创建订单失败：', e)
    reject()
  }
}
</script>

<template>
  <el-tabs class="tab-container" v-model="activeTab">
    <el-tab-pane :name="TabEnum.ALL" label="全部订单">
      <RentalOrdersTable
        ref="allOrdersTableRef"
        :list-data="allOrdersData.listData"
        :total="allOrdersData.total"
        @search="fetchTabData($event, TabEnum.ALL)"
        @toDetail="handleToDetailPage"
        @confirm="handleConfirmService"
        @replacement="handleReplacement"
        @finished="handleFinishedService"
        @modify="handleModifyAudit"
        @cancel="handleCancelOrder"
        @create="handleCreateOrder"
      />
    </el-tab-pane>
    <!-- 下面分出来的 tab 只是方便管理员操作 -->
    <!-- 订单修改申请 tab 相当于全部订单里的已预约 + 小程序提交了修改 的状态 -->
    <el-tab-pane :name="TabEnum.MODIFY_REQUESTS" label="订单修改申请">
      <RentalOrdersTable
        ref="modifyRequestsTableRef"
        :list-data="modifyRequestsData.listData"
        :total="modifyRequestsData.total"
        :fixed-status-value="RentalOrderStatus.RESERVED"
        @search="fetchTabData($event, TabEnum.MODIFY_REQUESTS)"
        @toDetail="handleToDetailPage"
        @confirm="handleConfirmService"
        @replacement="handleReplacement"
        @finished="handleFinishedService"
        @modify="handleModifyAudit"
        @cancel="handleCancelOrder"
        @create="handleCreateOrder"
      />
    </el-tab-pane>
    <!-- 换人申请 tab 相当于全部订单里面的陪护中的状态 -->
    <el-tab-pane :name="TabEnum.CHANGE_ESCORT_REQUESTS" label="换人申请">
      <RentalOrdersTable
        ref="changeEscortRequestsTableRef"
        :list-data="changeEscortRequestsData.listData"
        :total="changeEscortRequestsData.total"
        :fixed-status-value="RentalOrderStatus.ESCORTING"
        @search="fetchTabData($event, TabEnum.CHANGE_ESCORT_REQUESTS)"
        @toDetail="handleToDetailPage"
        @confirm="handleConfirmService"
        @replacement="handleReplacement"
        @finished="handleFinishedService"
        @modify="handleModifyAudit"
        @cancel="handleCancelOrder"
        @create="handleCreateOrder"
      />
    </el-tab-pane>
  </el-tabs>
</template>

<style scoped lang="scss">
.tab-container {
  margin-top: 20px;
}
</style>
