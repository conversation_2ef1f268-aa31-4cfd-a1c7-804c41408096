<script setup lang="ts">
import { requestHospitalizationDepositOrderByPage } from '@/api/deposit-management.api.ts'
import { HospitalizationDepositDataItem } from '@/api/dto/deposit-management.dto.ts'

/**
 * 住院预交金管理 / 住院预交金信息 列表
 */
import { ROUTER_PATH } from '@/router/router-path.ts'
import DepositList from '@/views/deposit-management/list/components/deposit-list.vue'

const router = useRouter()

// 表格数据
const depositListData = reactive({
  listData: [] as HospitalizationDepositDataItem[],
  total: 0
})

async function handleSearch({ currentPage, pageSize, searchData }) {
  console.log('搜索：', currentPage, pageSize, searchData)
  try {
    const { data, recordCount } = await requestHospitalizationDepositOrderByPage({
      patientName: searchData.patientName,
      admissionNo: searchData.admissionNo,
      hisOrderNo: searchData.hisOrderNo,
      platformOrderNo: searchData.platformOrderNo,
      orderStatus: searchData.orderStatus ?? '',
      page: currentPage,
      rows: pageSize
    })
    console.log('获取住院预交金列表：', data)
    depositListData.listData = data
    depositListData.total = recordCount
  } catch (e) {
    console.error('搜索住院登记列表失败：', e)
    depositListData.listData = []
    depositListData.total = 0
  }
}

// 查看详情
function handleViewDetail(orderItem: HospitalizationDepositDataItem) {
  console.log('查看详情：', orderItem)
  router.push({
    path: ROUTER_PATH.DEPOSIT_MANAGEMENT_DETAIL,
    query: { hospitalizationDepositOrderId: orderItem.hospitalizationDepositOrderId }
  })
}
</script>

<template>
  <div class="deposit-container">
    <DepositList
      :list-data="depositListData.listData"
      :total="depositListData.total"
      @search="handleSearch"
      @view-detail="handleViewDetail"
    />
  </div>
</template>

<style scoped lang="scss">
.deposit-container {
  padding: 30px 20px;
}
</style>
