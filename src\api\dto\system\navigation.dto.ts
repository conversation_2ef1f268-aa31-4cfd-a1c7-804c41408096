import type { NAVIGATION_TYPE_ENUM } from '../navigation.dto'

export interface SysNavigationModel {
  checkMark: boolean
  classLayer: number
  expanded: 'true' | 'false'
  iconUrl: string
  isLeaf: 'true' | 'false'
  linkUrl: string
  mainId: number
  navigationId: string
  navigationName: string
  navigationType: NAVIGATION_TYPE_ENUM
  parentId: number
}
export type GetRightSysNavigationForShowOutput = SysNavigationModel[]

export interface GetSysNavigationForPageInput {
  parentId: number
  navigationName: string
  enabledMark: boolean | ''
  page: number
  rows: number
}
export interface SysNavigationMenuItem {
  createTime: string
  description: string
  enabledMark: boolean
  isSys: boolean
  layer: number
  modifyTime: string
  navigationId: string
  navigationName: string
  outLinkMark: boolean
  sortCode: number
}
export type GetSysNavigationForPageOutput = SysNavigationMenuItem[]

export interface GetSysNavigationForUpdateInput {
  navigationId: string
}
export interface GetSysNavigationForUpdateOutput {
  description: string
  enabledMark: boolean
  iconUrl: string
  isSys: boolean
  layer: number
  linkUrl: string
  navigationId: string
  navigationName: string
  navigationType: NAVIGATION_TYPE_ENUM
  outLinkMark: boolean
  parentId: number
  sortCode: number
  title: string
}

export interface AddSysNavigationInput extends Omit<GetSysNavigationForUpdateOutput, 'parentId'> {
  description: string
  enabledMark: boolean
  iconUrl: string
  isSys: boolean
  layer: number
  linkUrl: string
  navigationName: string
  navigationType: NAVIGATION_TYPE_ENUM
  outLinkMark: boolean
  /**新增子级必传 */
  parentId?: number
  sortCode: number
  title: string
}

export interface UpdateSysNavigationInput extends AddSysNavigationInput {
  navigationId: string
}

export interface DeleteSysNavigationInput {
  navigationId: string
}

export interface GetSysFunctionauthForPageInput {
  functionAuthName: string
  navigationId: string
  page: number
  rows: number
}
export interface SysFunctionauthModel {
  action: string
  area: string
  controller: string
  enabledMark: boolean
  encode: string
  functionAuthId: string
  functionAuthName: string
}
export type GetSysFunctionauthForPageOutput = SysFunctionauthModel[]

export interface AddSysFunctionauthInput {
  navigationId: string
  functionAuthName: string
  encode: string
  area: string
  controller: string
  action: string
  enabledMark: boolean
  isSys: boolean
  elementscript: string
  // sortCode: number
  // description: string
}

export interface GetSysFunctionauthForUpdateInput {
  functionAuthId: string
}
export interface GetSysFunctionauthForUpdateOutput {
  action: string
  area: string
  controller: string
  enabledMark: boolean
  encode: string
  functionAuthId: string
  functionAuthName: string
  isSys: boolean
  navigationId: string
}

export interface UpdateSysFunctionauthInput extends AddSysFunctionauthInput {
  functionAuthId: string
}

export interface DeleteSysFunctionauthInput {
  functionAuthId: string
}
