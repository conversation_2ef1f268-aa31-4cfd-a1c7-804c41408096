<script setup lang="ts">
import { AdmissionRegistrationStatus, admissionWayOptions, nationOptions } from '@/api/dto/emun.dto.ts'
import {
  AdmissionRegistrationExtra,
  EnumDictionaryItem,
  ResPrintingOfApplicationForm,
  ResPrintingOfWristStrap
} from '@/api/dto/inpatient-registration.dto.ts'
import { ResRentalQuerydeptDTO } from '@/api/dto/rental-management/rental-order-management.dto.ts'
import {
  requestAcceptInpatientRegistration,
  requestAdmissionEnums,
  requestInpatientRegistrationForDetail,
  requestPrintingOfApplicationForm,
  requestPrintingOfWristStrap,
  requestUpdateInpatientRegistration
} from '@/api/inpatient-registration.api.ts'
import { requestRentalSelectDepartmentList } from '@/api/rental-management.api.ts'
import { useMountDialog } from '@/hooks/useMountDialog'
import { appMessage } from '@/hooks/useNaiveApi'
import { getCityCascaderOptions } from '@/utils/city-list-utils.ts'
import { getNationCascaderOptions } from '@/utils/nation-list-utils.ts'
import ReceiveFormDialog from '@/views/inpatient-registration/components/receive-form-dialog.vue'
import {
  useInpatientFormPrinter,
  useWristbandPrinter,
  useIdCardPrinter
} from '@/views/inpatient-registration/utils/inpatient-printer-helper.ts'
import { WarningFilled } from '@element-plus/icons-vue'
import { CascaderValue, ElForm, type UploadUserFile } from 'element-plus'
import { insuranceTypeCascaderOptions } from '@/api/dto/emun.dto.ts'
import { useInpatientNotificationStore } from '@/stores/inpatient-notification.store'

const { printWristband, wristbandPrintError } = useWristbandPrinter()
const { printInpatientForm, inpatientFormPrintError } = useInpatientFormPrinter()
const { printIdCard, idCardPrintError } = useIdCardPrinter()

const { open: openReceiveDialog } = useMountDialog(ReceiveFormDialog)
const router = useRouter()
const route = useRoute()
const inpatientNotificationStore = useInpatientNotificationStore()

let _admissionRegistrationId = ''

// 点击打印手腕带按钮 loading
const isWristbandPrintingLoading = ref(false)

// 点击打印申请单按钮 loading
const isInpatientFormPrintingLoading = ref(false)

// 点击打印身份证按钮 loading
const isIdCardPrintingLoading = ref(false)

// 详情数据
const detailInfo = ref<AdmissionRegistrationExtra>({} as AdmissionRegistrationExtra)

// 是否为编辑模式
const isEditMode = ref(false)
// 编辑模式下的数据备份
let _editModeDetailInfoBackup: any = null
// 编辑模式下 placeholder 显示
const selectorPlaceholder = computed(() => {
  return isEditMode.value ? '请选择' : ' '
})
// 编辑模式下详细地址 placeholder 显示
const addressDetailPlaceholder = computed(() => {
  return isEditMode.value ? '请输入详细地址' : ' '
})

const uploadIdCardImgFrontFileList = ref<UploadUserFile[]>([])
const uploadIdCardImgBackFileList = ref<UploadUserFile[]>([])

watch(uploadIdCardImgFrontFileList, (val) => {
  detailInfo.value.idCardImgurlFront = val[0]?.url ?? ''
})

watch(uploadIdCardImgBackFileList, (val) => {
  detailInfo.value.idCardImgurlBack = val[0]?.url ?? ''
})

const stateOptions = [
  {
    label: '未办理入院',
    value: '0'
  },
  {
    label: '已提交登记信息',
    value: '1'
  },
  {
    label: '已办理入院',
    value: '2'
  }
]

const cityCascaderOptions = ref<{ value: any; label: any; children: any }[]>([])
const nationCascaderOptions = ref<{ value: any; label: any }[]>([])

// 处理出生地选择
const birthPlaceValue = ref<string[]>([])
const handleBirthPlaceChange = (value: CascaderValue) => {
  console.log('出生地选择', value)
  if (Array.isArray(value)) {
    detailInfo.value.birthPlace = value.join('-')
  } else {
    detailInfo.value.birthPlace = String(value || '')
  }
}
const domicileAddressValue = ref<string[]>([])
const handleDomicileAddressChange = (value: CascaderValue) => {
  console.log('户口地址选择', value)
  if (Array.isArray(value)) {
    detailInfo.value.domicileAddress = value.join('-')
  } else {
    detailInfo.value.domicileAddress = String(value || '')
  }
}
const currentResidentialAddressValue = ref<string[]>([])
const handleCurrentResidentialAddressChange = (value: CascaderValue) => {
  console.log('现居地址选择', value)
  if (Array.isArray(value)) {
    detailInfo.value.currentResidentialAddress = value.join('-')
  } else {
    detailInfo.value.currentResidentialAddress = String(value || '')
  }
}
const contactAddressValue = ref<string[]>([])
const handleContactAddressChange = (value: CascaderValue) => {
  console.log('联系人地址选择', value)
  if (Array.isArray(value)) {
    detailInfo.value.contactAddress = value.join('-')
  } else {
    detailInfo.value.contactAddress = String(value || '')
  }
}
const insuranceTypeValue = ref<string[]>([])
const handleInsuranceTypeChange = (value: CascaderValue) => {
  console.log('医保类型选择', value)
  if (Array.isArray(value)) {
    detailInfo.value.insuranceType = value.join('-')
  } else {
    detailInfo.value.insuranceType = String(value || '')
  }
}

// 表单校验规则
const formRules = reactive({
  patientName: [{ required: true, message: '请输入患者姓名', trigger: 'blur' }],
  genderType: [{ required: true, message: '请选择性别', trigger: 'blur' }],
  patientIdNo: [{ required: true, message: '请输入身份证号', trigger: 'blur' }],
  birthday: [{ required: true, message: '请选择出生日期', trigger: 'blur' }],
  mobilephone: [{ required: true, message: '请输入联系电话', trigger: 'blur' }],
  insuranceType: [{ required: true, message: '请选择医保付费类型', trigger: 'blur' }],
  maritalStatus: [{ required: true, message: '请选择婚姻状态', trigger: 'blur' }],
  nationality: [{ required: true, message: '请输入国籍', trigger: 'blur' }],
  nation: [{ required: true, message: '请输入民族', trigger: 'blur' }],
  birthPlace: [{ required: true, message: '请选择出生地址', trigger: 'blur' }],
  birthPlaceDetail: [{ required: true, message: '请输入详细出生地址', trigger: 'change' }],
  domicileAddress: [{ required: true, message: '请选择户籍地址', trigger: 'blur' }],
  domicileAddressDetail: [{ required: true, message: '请输入详细户籍地址', trigger: 'change' }],
  currentResidentialAddress: [{ required: true, message: '请选择当前居住地址', trigger: 'blur' }],
  currentResidentialAddressDetail: [{ required: true, message: '请输入详细当前居住地址', trigger: 'change' }],
  contactName: [{ required: true, message: '请输入联系人姓名', trigger: 'blur' }],
  contactRelationship: [{ required: true, message: '请选择联系人关系', trigger: 'blur' }],
  contactMobilephone: [{ required: true, message: '请输入联系人手机号', trigger: 'blur' }],
  contactAddress: [{ required: true, message: '请选择联系人地址', trigger: 'blur' }],
  contactAddressDetail: [{ required: true, message: '请输入联系人详细地址', trigger: 'change' }]
})

// 表单引用
const formRef = ref<InstanceType<typeof ElForm> | null>(null)

onMounted(async () => {
  console.log('住院登记列表 -> 详情：', route.query)
  if (!route.query.admissionRegistrationId) {
    console.error('获取住院登记详情失败：未获取到住院登记ID')
    appMessage.error('获取住院登记详情失败：未获取到住院登记ID')
    return
  }
  _admissionRegistrationId = route.query.admissionRegistrationId as string
  await fetchAllDictionaries()
  await requestDetailData()
})

async function requestDetailData() {
  try {
    const result = await requestInpatientRegistrationForDetail({
      admissionRegistrationId: _admissionRegistrationId
    })
    // 处理出生地、户口地址、现居住地，这三个字段使用 @ 分隔，左边是 省市区，右边是详细地址
    if (result.birthPlace) {
      const [birthPlace, birthPlaceDetail] = result.birthPlace.split('@')
      // 需要特殊赋值，因为层级输入需要数组形式
      result.birthPlace = birthPlace
      birthPlaceValue.value = birthPlace?.split('-') || []
      result.birthPlaceDetail = birthPlaceDetail
    }
    if (result.domicileAddress) {
      const [domicileAddress, domicileAddressDetail] = result.domicileAddress.split('@')
      result.domicileAddress = domicileAddress
      domicileAddressValue.value = domicileAddress?.split('-') || []
      result.domicileAddressDetail = domicileAddressDetail
    }
    if (result.currentResidentialAddress) {
      const [currentResidentialAddress, currentResidentialAddressDetail] = result.currentResidentialAddress.split('@')
      result.currentResidentialAddress = currentResidentialAddress
      currentResidentialAddressValue.value = currentResidentialAddress?.split('-') || []
      result.currentResidentialAddressDetail = currentResidentialAddressDetail
    }
    if (result.contactAddress) {
      const [contactAddress, contactAddressDetail] = result.contactAddress.split('@')
      result.contactAddress = contactAddress
      contactAddressValue.value = contactAddress?.split('-') || []
      result.contactAddressDetail = contactAddressDetail
    }
    if (result.insuranceType) {
      insuranceTypeValue.value = result.insuranceType.split('-') || []
    }

    if (result.idCardImgurlFront) {
      uploadIdCardImgFrontFileList.value = [
        {
          name: '身份证头像页',
          url: result.idCardImgurlFront
        }
      ]
    }

    if (result.idCardImgurlBack) {
      uploadIdCardImgBackFileList.value = [
        {
          name: '身份证国徽页',
          url: result.idCardImgurlBack
        }
      ]
    }

    result.state = stateOptions.find((item) => item.value === result.state)?.label || ''
    detailInfo.value = result
    console.log('住院登记详情：', detailInfo.value)
  } catch (e) {
    console.error('获取住院登记详情失败：', e)
  }
}

// 返回列表页
const handleBack = () => {
  // 返回路由栈的上一个页面
  router.back()
}

const handleOpenReceive = (registrationInfo?: AdmissionRegistrationExtra) => {
  if (!registrationInfo) {
    console.error('接受入院登记失败，入院信息为空')
    appMessage.error('接受入院登记失败，入院信息为空')
    return
  }
  openReceiveDialog({
    patientName: registrationInfo.patientName,
    hosNumber: registrationInfo.regNo,
    finished: async (selectedDate: string) => {
      console.log('选择的入院登记日期：', selectedDate)
      await acceptInpatientRegistration(selectedDate, registrationInfo.admissionRegistrationId)
    }
  })
}

async function acceptInpatientRegistration(admissionTime: string, admissionRegistrationId: string) {
  try {
    const { msg } = await requestAcceptInpatientRegistration({
      admissionRegistrationId,
      admissionTime
    })
    console.log('入院登记处理结果：', msg)
    ElMessageBox({
      title: '提示',
      type: 'success',
      message: '入院登记接受处理成功',
      callback: () => {
        requestDetailData()
        inpatientNotificationStore.triggerPendingAdmissionUpdate()
      }
    })
  } catch (e) {
    console.error('入院登记处理失败：', e)
    throw e
  }
}

const handleOpenEdit = () => {
  isEditMode.value = true
  _editModeDetailInfoBackup = JSON.parse(JSON.stringify(detailInfo.value))
  // 单独处理级联选择器的数据
  _editModeDetailInfoBackup.birthPlaceValue = JSON.parse(JSON.stringify(birthPlaceValue.value))
  _editModeDetailInfoBackup.domicileAddressValue = JSON.parse(JSON.stringify(domicileAddressValue.value))
  _editModeDetailInfoBackup.currentResidentialAddressValue = JSON.parse(
    JSON.stringify(currentResidentialAddressValue.value)
  )
  _editModeDetailInfoBackup.contactAddressValue = JSON.parse(JSON.stringify(contactAddressValue.value))
  _editModeDetailInfoBackup.insuranceTypeValue = JSON.parse(JSON.stringify(insuranceTypeValue.value))
  // 身份证头像页和身份证国徽页
  _editModeDetailInfoBackup.uploadIdCardImgFrontFileList = JSON.parse(
    JSON.stringify(uploadIdCardImgFrontFileList.value)
  )
  _editModeDetailInfoBackup.uploadIdCardImgBackFileList = JSON.parse(JSON.stringify(uploadIdCardImgBackFileList.value))
}

const handleCancelEdit = () => {
  isEditMode.value = false
  // 恢复备份数据
  detailInfo.value = JSON.parse(JSON.stringify(_editModeDetailInfoBackup))
  // 恢复级联选择器的数据
  birthPlaceValue.value = JSON.parse(JSON.stringify(_editModeDetailInfoBackup.birthPlaceValue))
  domicileAddressValue.value = JSON.parse(JSON.stringify(_editModeDetailInfoBackup.domicileAddressValue))
  currentResidentialAddressValue.value = JSON.parse(
    JSON.stringify(_editModeDetailInfoBackup.currentResidentialAddressValue)
  )
  contactAddressValue.value = JSON.parse(JSON.stringify(_editModeDetailInfoBackup.contactAddressValue))
  insuranceTypeValue.value = JSON.parse(JSON.stringify(_editModeDetailInfoBackup.insuranceTypeValue))
  // 身份证头像页和身份证国徽页
  uploadIdCardImgFrontFileList.value = JSON.parse(
    JSON.stringify(_editModeDetailInfoBackup.uploadIdCardImgFrontFileList)
  )
  uploadIdCardImgBackFileList.value = JSON.parse(JSON.stringify(_editModeDetailInfoBackup.uploadIdCardImgBackFileList))
}

const handleSaveEdit = async () => {
  await formRef.value!.validate(async (valid) => {
    if (valid) {
      try {
        await updateDetailData(detailInfo.value)
        isEditMode.value = false
        ElMessageBox({
          title: '提示',
          type: 'success',
          message: '住院登记信息更新成功',
          callback: () => {
            requestDetailData()
          }
        })
      } catch (e) {
        console.error('更新失败：', e)
        appMessage.error('更新失败，请重试')
      }
    }
  })
}

async function updateDetailData(modifyData: AdmissionRegistrationExtra) {
  try {
    const updateResult = await requestUpdateInpatientRegistration({
      admissionRegistrationId: modifyData.admissionRegistrationId,
      mobilephone: modifyData.mobilephone,
      insuranceType: modifyData.insuranceType,
      maritalStatus: modifyData.maritalStatus,
      nationality: modifyData.nationality,
      nation: modifyData.nation,
      birthPlace: modifyData.birthPlace + '@' + modifyData.birthPlaceDetail,
      domicileAddress: modifyData.domicileAddress + '@' + modifyData.domicileAddressDetail,
      currentResidentialAddress:
        modifyData.currentResidentialAddress + '@' + modifyData.currentResidentialAddressDetail,
      employerName: modifyData.employerName,
      career: modifyData.career,
      contactName: modifyData.contactName,
      contactRelationship: modifyData.contactRelationship,
      contactMobilephone: modifyData.contactMobilephone,
      contactAddress: modifyData.contactAddress + '@' + modifyData.contactAddressDetail,
      hospitalizationDepartmentName: modifyData.hospitalizationDepartmentName,
      patientName: modifyData.patientName,
      patientIdNo: modifyData.patientIdNo,
      genderType: modifyData.genderType,
      birthday: modifyData.birthday,
      indate: modifyData.indate || ''
    })
    console.log('更新住院登记信息：', updateResult)
  } catch (e) {
    console.error('更新住院登记信息失败：', e)
    // 把错误抛出，在编辑弹窗中会用 try catch 处理，让 loading 关闭
    throw e
  }
}

// 打印申请单模板
async function handlePrintInpatientForm() {
  console.log('打印申请单模板', detailInfo.value)

  let printFormData: ResPrintingOfApplicationForm

  try {
    printFormData = await requestPrintingOfApplicationForm({
      admissionRegistrationId: _admissionRegistrationId
    })
    console.log('获取到申请单模板数据：', printFormData)
  } catch (e) {
    console.error('获取申请单模板数据失败：', e)
    appMessage.error('获取申请单模板数据失败')
    return
  }

  isInpatientFormPrintingLoading.value = true
  await printInpatientForm(printFormData)
  isInpatientFormPrintingLoading.value = false

  if (inpatientFormPrintError.value) {
    console.error(inpatientFormPrintError.value)
    appMessage.error(inpatientFormPrintError.value)
    return
  }

  console.log('申请单打印成功')
  appMessage.success('申请单打印成功')
}

// 打印身份证
async function handlePrintIdCard() {
  if (!detailInfo.value.idCardImgurlFront && !detailInfo.value.idCardImgurlBack) {
    appMessage.error('未上传身份证图片，无法打印')
    return
  }

  isIdCardPrintingLoading.value = true
  await printIdCard({
    idCardImgurlFront: detailInfo.value.idCardImgurlFront || '',
    idCardImgurlBack: detailInfo.value.idCardImgurlBack || ''
  })
  isIdCardPrintingLoading.value = false

  if (idCardPrintError.value) {
    console.error(idCardPrintError.value)
    appMessage.error(idCardPrintError.value)
    return
  }

  console.log('身份证打印成功')
  appMessage.success('身份证打印成功')
}

// 打印手腕带模板
async function handlePrintWristband() {
  let printFormData: ResPrintingOfWristStrap
  try {
    printFormData = await requestPrintingOfWristStrap({
      admissionRegistrationId: _admissionRegistrationId
    })
    console.log('获取到手腕带模板数据：', printFormData)
  } catch (e) {
    console.error('获取手腕带模板数据失败：', e)
    return
  }

  // 增加记帐号判空，避免打印的手腕带二维码为默认值产生误导
  if (!printFormData.inpno) {
    appMessage.error('打印失败，记帐号为空')
    return
  }

  // 记帐号
  const billingAccountValue = printFormData.inpno

  isWristbandPrintingLoading.value = true

  await printWristband({
    // 患者名
    patientName: printFormData.name,
    // 年龄
    age: printFormData.age,
    // 性别
    gender: printFormData.sex,
    // 科别
    department: printFormData.curdptnm,
    // 住院号
    hospitalizationNumber: printFormData.patno,
    // 记帐号
    billingAccount: billingAccountValue,
    // 二维码值 - 对应记帐号
    billingAccountBar: billingAccountValue
  })

  isWristbandPrintingLoading.value = false
  if (wristbandPrintError.value) {
    console.error(wristbandPrintError.value)
    appMessage.error(wristbandPrintError.value)
    return
  }
  console.log('手腕带打印成功')
  appMessage.success('手腕带打印成功')
}

// 从接口获取字典数据
// 婚姻状态
const maritalStatusOptions = ref<EnumDictionaryItem[]>([])
// 职业
const careerOptions = ref<EnumDictionaryItem[]>([])
// 性别
const genderOptions = ref<EnumDictionaryItem[]>([])
// 科室列表
const departmentOptions = ref<ResRentalQuerydeptDTO[]>([] as ResRentalQuerydeptDTO[])
// 联系人关系
const contactRelationshipOptions = ref<EnumDictionaryItem[]>([])

// 获取所有字典数据
const fetchAllDictionaries = async () => {
  try {
    // 动态获取省市区数据
    cityCascaderOptions.value = await getCityCascaderOptions()
    nationCascaderOptions.value = await getNationCascaderOptions()

    // 获取其他枚举字典（婚姻状态、医保类型等）
    const enumsRes = await requestAdmissionEnums()
    console.log('其他枚举字典：', enumsRes)

    // 获取科室列表
    const departmentRes = await requestRentalSelectDepartmentList()
    departmentOptions.value = departmentRes || []
    console.log('科室列表：', departmentOptions.value)

    maritalStatusOptions.value = enumsRes.marital
    careerOptions.value = enumsRes.career
    genderOptions.value = enumsRes.gender
    contactRelationshipOptions.value = enumsRes.contactRelationship
  } catch (error) {
    console.error('获取字典数据失败:', error)
    appMessage.error('获取字典数据失败，请刷新页面重试')
  }
}
</script>

<template>
  <div class="inpatient-registration_details-container">
    <div class="back-bar flex">
      <el-button type="warning" @click="handleBack">返回</el-button>
    </div>

    <!-- 有数据时候才显示 -->
    <div v-if="detailInfo.admissionRegistrationId" class="content-container">
      <div class="tools">
        <template v-if="isEditMode">
          <el-button plain size="large" @click="handleCancelEdit">取消编辑</el-button>
          <el-button type="primary" size="large" @click="handleSaveEdit">确定保存</el-button>
        </template>
        <template v-else>
          <template v-if="detailInfo.status !== AdmissionRegistrationStatus.PROCESSED">
            <el-icon color="#E6A23C" size="16"><WarningFilled /></el-icon>
            <span style="color: #e6a23c; margin: 0 16px 0 8px"> 不支持直接编辑，如需编辑请点击编辑按钮 </span>
            <el-button v-auth="'admissionRegistration_update'" plain size="large" @click="handleOpenEdit"
              >编辑</el-button
            >
          </template>
          <el-button
            v-auth="'admissionRegistration_accept'"
            v-if="detailInfo.status === AdmissionRegistrationStatus.COMPLETED"
            type="success"
            size="large"
            @click="handleOpenReceive(detailInfo)"
          >
            接受处理
          </el-button>
          <el-button
            v-auth="'admissionRegistration_printingOfApplicationForm'"
            type="primary"
            :loading="isInpatientFormPrintingLoading"
            size="large"
            @click="handlePrintInpatientForm"
          >
            入院卡打印
          </el-button>
          <el-button
            v-auth="'admissionRegistration_printingOfWristStrap'"
            color="#00B798"
            :loading="isWristbandPrintingLoading"
            size="large"
            @click="handlePrintWristband"
          >
            手腕带打印
          </el-button>
          <el-button
            style="color: white"
            color="#2EC6C9"
            :loading="isIdCardPrintingLoading"
            size="large"
            @click="handlePrintIdCard"
          >
            打印身份证
          </el-button>
        </template>
      </div>

      <!-- 数据展示，在编辑模式下会将输入框改为可编辑状态，否则默认不许编辑 -->
      <el-form
        ref="formRef"
        :model="detailInfo"
        :rules="formRules"
        :disabled="!isEditMode"
        label-width="124px"
        class="detail-form"
      >
        <!-- 基本信息 -->
        <div class="form-section">
          <div class="section-title section-title__first">基本信息</div>
          <el-row>
            <el-col :style="{ flex: 1 }">
              <el-form-item label="姓名：" prop="patientName">
                <el-input v-model="detailInfo.patientName" :disabled="!isEditMode" />
              </el-form-item>
            </el-col>
            <el-col :style="{ flex: 1 }" prop="genderType">
              <el-form-item label="性别：" prop="genderType">
                <el-select v-model="detailInfo.genderType" :disabled="!isEditMode" :placeholder="selectorPlaceholder">
                  <el-option
                    v-for="item in genderOptions"
                    :key="item.ordinal"
                    :label="item.describe"
                    :value="item.ordinal"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :style="{ flex: 1 }">
              <el-form-item label="身份证号：" prop="patientIdNo">
                <el-input v-model="detailInfo.patientIdNo" disabled />
              </el-form-item>
            </el-col>
            <el-col :style="{ flex: 1 }">
              <el-form-item label="出生日期：" prop="birthday">
                <el-date-picker
                  v-model="detailInfo.birthday"
                  class="date-picker"
                  :disabled="!isEditMode"
                  value-format="YYYY-MM-DD"
                />
              </el-form-item>
            </el-col>
            <el-col :style="{ flex: 1 }">
              <el-form-item label="联系电话：" prop="mobilephone">
                <el-input v-model="detailInfo.mobilephone" :disabled="!isEditMode" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :style="{ flex: 2 }">
              <el-form-item label="医保付费类型：" prop="insuranceType" class="insurance-type-col">
                <el-cascader
                  v-model="insuranceTypeValue"
                  :options="insuranceTypeCascaderOptions"
                  :props="{ expandTrigger: 'hover', label: 'label', value: 'value' }"
                  :disabled="!isEditMode"
                  :placeholder="selectorPlaceholder"
                  @change="handleInsuranceTypeChange"
                />
              </el-form-item>
            </el-col>
            <el-col :style="{ flex: 1 }" />
            <el-col :style="{ flex: 1 }" />
            <el-col :style="{ flex: 1 }" />
          </el-row>
        </div>

        <div class="form-section">
          <div class="section-title">详细信息</div>
          <el-row>
            <el-col :style="{ flex: 1 }">
              <el-form-item label="婚姻状态：" prop="maritalStatus">
                <el-select
                  v-model="detailInfo.maritalStatus"
                  :disabled="!isEditMode"
                  :placeholder="selectorPlaceholder"
                >
                  <el-option
                    v-for="item in maritalStatusOptions"
                    :key="item.ordinal"
                    :label="item.describe"
                    :value="item.describe"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :style="{ flex: 1 }">
              <el-form-item label="国籍：" prop="nationality">
                <el-select v-model="detailInfo.nationality" :disabled="!isEditMode" :placeholder="selectorPlaceholder">
                  <el-option
                    v-for="item in nationCascaderOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.label"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :style="{ flex: 1 }">
              <el-form-item label="民族：" prop="nation">
                <el-select v-model="detailInfo.nation" :disabled="!isEditMode" :placeholder="selectorPlaceholder">
                  <el-option v-for="item in nationOptions" :key="item" :label="item" :value="item" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col class="address-col" :style="{ flex: 2 }">
              <el-form-item label="出生地：" prop="birthPlace">
                <el-cascader
                  v-model="birthPlaceValue"
                  :options="cityCascaderOptions"
                  :props="{ expandTrigger: 'hover', label: 'label', value: 'value' }"
                  clearable
                  :disabled="!isEditMode"
                  :placeholder="selectorPlaceholder"
                  @change="handleBirthPlaceChange"
                />
              </el-form-item>
              <el-form-item class="address-detail" label="" prop="birthPlaceDetail">
                <el-input
                  v-model="detailInfo.birthPlaceDetail"
                  :disabled="!isEditMode"
                  :placeholder="addressDetailPlaceholder"
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col class="address-col" :style="{ flex: 2 }">
              <el-form-item label="户口地址：" prop="domicileAddress">
                <el-cascader
                  v-model="domicileAddressValue"
                  :options="cityCascaderOptions"
                  :props="{ expandTrigger: 'hover', label: 'label', value: 'value' }"
                  clearable
                  :disabled="!isEditMode"
                  :placeholder="selectorPlaceholder"
                  @change="handleDomicileAddressChange"
                />
              </el-form-item>
              <el-form-item class="address-detail" label="" prop="domicileAddressDetail">
                <el-input
                  v-model="detailInfo.domicileAddressDetail"
                  :disabled="!isEditMode"
                  :placeholder="addressDetailPlaceholder"
                />
              </el-form-item>
            </el-col>

            <el-col class="address-col" :style="{ flex: 2 }">
              <el-form-item label="现居住地：" prop="currentResidentialAddress">
                <el-cascader
                  v-model="currentResidentialAddressValue"
                  :options="cityCascaderOptions"
                  :props="{ expandTrigger: 'hover', label: 'label', value: 'value' }"
                  clearable
                  :disabled="!isEditMode"
                  :placeholder="selectorPlaceholder"
                  @change="handleCurrentResidentialAddressChange"
                />
              </el-form-item>
              <el-form-item class="address-detail" label="" prop="currentResidentialAddressDetail">
                <el-input
                  v-model="detailInfo.currentResidentialAddressDetail"
                  :disabled="!isEditMode"
                  :placeholder="addressDetailPlaceholder"
                />
              </el-form-item>
            </el-col>
            <el-col :style="{ flex: 1 }">
              <el-form-item label="单位名称：" prop="employerName">
                <el-input v-model="detailInfo.employerName" :disabled="!isEditMode" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :style="{ flex: 1 }">
              <el-form-item label="职业：" prop="career">
                <el-select
                  v-model="detailInfo.career"
                  :disabled="!isEditMode"
                  :placeholder="selectorPlaceholder"
                  clearable
                >
                  <el-option
                    v-for="item in careerOptions"
                    :key="item.ordinal"
                    :label="item.describe"
                    :value="item.describe"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :style="{ flex: 1 }" class="img-upload-col">
              <el-form-item label="身份证头像页：" prop="idCardImgurlFront">
                <CommonImgUpload
                  v-model="uploadIdCardImgFrontFileList"
                  :limit="1"
                  :max-file-size="10240"
                  :file-type="['image/jpeg', 'image/png']"
                  use-base64
                  height="100px"
                  width="100px"
                  disabled
                />
              </el-form-item>
            </el-col>
            <el-col :style="{ flex: 1 }" class="img-upload-col">
              <el-form-item label="身份证国徽页：" prop="idCardImgurlBack">
                <CommonImgUpload
                  v-model="uploadIdCardImgBackFileList"
                  :limit="1"
                  :max-file-size="10240"
                  :file-type="['image/jpeg', 'image/png']"
                  use-base64
                  height="100px"
                  width="100px"
                  disabled
                />
              </el-form-item>
            </el-col>
            <el-col :style="{ flex: 1 }" />
            <el-col :style="{ flex: 1 }" />
          </el-row>
        </div>

        <div class="form-section">
          <div class="section-title">紧急联系人</div>
          <el-row>
            <el-col :style="{ flex: 1 }">
              <el-form-item label="关系：" prop="contactRelationship">
                <el-select
                  v-model="detailInfo.contactRelationship"
                  :disabled="!isEditMode"
                  :placeholder="selectorPlaceholder"
                >
                  <el-option
                    v-for="item in contactRelationshipOptions"
                    :key="item.ordinal"
                    :label="item.describe"
                    :value="item.describe"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :style="{ flex: 1 }">
              <el-form-item label="姓名：" prop="contactName">
                <el-input v-model="detailInfo.contactName" :disabled="!isEditMode" />
              </el-form-item>
            </el-col>
            <el-col :style="{ flex: 1 }">
              <el-form-item label="联系电话：" prop="contactMobilephone">
                <el-input v-model="detailInfo.contactMobilephone" :disabled="!isEditMode" />
              </el-form-item>
            </el-col>
            <el-col class="address-col" :style="{ flex: 2 }">
              <el-form-item label="联系地址：" prop="contactAddress">
                <el-cascader
                  v-model="contactAddressValue"
                  :options="cityCascaderOptions"
                  :props="{ expandTrigger: 'hover', label: 'label', value: 'value' }"
                  clearable
                  :disabled="!isEditMode"
                  :placeholder="selectorPlaceholder"
                  @change="handleContactAddressChange"
                />
              </el-form-item>
              <el-form-item class="address-detail" label="" prop="contactAddressDetail">
                <el-input
                  v-model="detailInfo.contactAddressDetail"
                  :disabled="!isEditMode"
                  :placeholder="addressDetailPlaceholder"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </div>

        <div class="form-section">
          <div class="section-title">入院情况</div>
          <el-row>
            <el-col :style="{ flex: 1 }">
              <el-form-item label="住院号：" prop="admissionNo">
                <el-input v-model="detailInfo.admissionNo" disabled />
              </el-form-item>
            </el-col>
            <el-col :style="{ flex: 1 }">
              <el-form-item label="记帐号：" prop="inpno">
                <el-input v-model="detailInfo.inpno" disabled />
              </el-form-item>
            </el-col>
            <el-col :style="{ flex: 1 }">
              <el-form-item label="科室：" prop="hospitalizationDepartmentName">
                <el-select
                  v-model="detailInfo.hospitalizationDepartmentName"
                  :disabled="!isEditMode"
                  :placeholder="selectorPlaceholder"
                  clearable
                >
                  <el-option
                    v-for="item in departmentOptions"
                    :key="item.deptcode"
                    :label="item.deptname"
                    :value="item.deptname"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :style="{ flex: 1 }">
              <el-form-item label="入院日期：" prop="indate">
                <el-date-picker
                  v-model="detailInfo.indate"
                  class="date-picker"
                  :disabled="!isEditMode"
                  value-format="YYYY-MM-DD"
                />
              </el-form-item>
            </el-col>
            <el-col :style="{ flex: 1 }">
              <el-form-item label="入院情况：" prop="state">
                <el-input v-model="detailInfo.state" disabled />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :style="{ flex: 1 }">
              <el-form-item label="入院途径：" prop="admway">
                <el-select v-model="detailInfo.admway" disabled :placeholder="selectorPlaceholder">
                  <el-option
                    v-for="item in admissionWayOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :style="{ flex: 4 }">
              <el-form-item label="门诊诊断：" prop="diag">
                <el-input v-model="detailInfo.diag" disabled />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :style="{ flex: 1 }">
              <el-form-item label="门诊医生：" prop="drname">
                <el-input v-model="detailInfo.drname" disabled />
              </el-form-item>
            </el-col>
            <el-col :style="{ flex: 1 }">
              <el-form-item label="结算方式：" prop="setmeth">
                <el-input v-model="detailInfo.setmeth" disabled />
              </el-form-item>
            </el-col>
            <el-col :style="{ flex: 1 }">
              <el-form-item label="价类：" prop="pricetype">
                <el-input v-model="detailInfo.pricetype" disabled />
              </el-form-item>
            </el-col>
            <el-col :style="{ flex: 1 }">
              <el-form-item label="自付%：" prop="rate">
                <el-input v-model.number="detailInfo.rate" disabled />
              </el-form-item>
            </el-col>
            <el-col :style="{ flex: 1 }">
              <el-form-item label="医疗证号：" prop="medicalCertificateNumber">
                <el-input v-model="detailInfo.medicalCertificateNumber" disabled />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :style="{ flex: 1 }">
              <el-form-item label="就医登记号：" prop="medicalRegistrationNumber">
                <el-input v-model="detailInfo.medicalRegistrationNumber" disabled />
              </el-form-item>
            </el-col>
            <el-col :style="{ flex: 4 }"></el-col>
          </el-row>
        </div>

        <div class="form-section">
          <div class="section-title">卡类情况</div>
          <el-row>
            <el-col :style="{ flex: 1 }">
              <el-form-item label="健康卡号：" prop="healthCardNumber">
                <el-input v-model="detailInfo.healthCardNumber" disabled />
              </el-form-item>
            </el-col>
            <el-col :style="{ flex: 1 }">
              <el-form-item label="参保地编码：" prop="codeOfInsuredPlace">
                <el-input v-model="detailInfo.codeOfInsuredPlace" disabled />
              </el-form-item>
            </el-col>
            <el-col :style="{ flex: 3 }">
              <el-form-item label="备注：" prop="notes">
                <el-input v-model="detailInfo.notes" class="full-width" disabled />
              </el-form-item>
            </el-col>
          </el-row>
        </div>
      </el-form>
    </div>
  </div>
</template>

<style scoped lang="scss">
.inpatient-registration_details-container {
  :deep(.el-form-item__content) {
    --el-form-inline-content-width: 194px !important;
  }

  :deep(.el-input.is-disabled .el-input__wrapper) {
    background-color: transparent !important;
  }

  :deep(.el-select__wrapper.is-disabled) {
    background-color: transparent !important;

    .el-select__suffix {
      display: none !important;
    }
  }

  :deep(.el-cascader .el-input.is-disabled) {
    cursor: not-allowed !important;

    .el-input__wrapper {
      cursor: not-allowed !important;
      pointer-events: none !important;
    }

    .el-input__inner {
      cursor: not-allowed !important;
      pointer-events: none !important;
    }

    .el-input__suffix {
      display: none !important;
    }
  }

  :deep(.el-input.is-disabled .el-input__inner) {
    color: #606266 !important;
    -webkit-text-fill-color: #606266 !important;
  }

  :deep(.el-select__wrapper.is-disabled .el-select__selected-item) {
    color: #606266 !important;
    -webkit-text-fill-color: #606266 !important;
  }

  :deep(.el-form-item) {
    align-items: flex-start !important;
  }

  .img-upload-col {
    display: flex;
    justify-content: flex-end;

    :deep(.el-form-item__content) {
      --el-form-inline-content-width: 0 !important;
    }
  }

  .content-container {
    padding: 20px 30px 20px 20px;
    position: relative;

    .tools {
      position: absolute;
      right: 30px;
      top: 20px;
      display: flex;
      align-items: center;
      z-index: 1;
    }

    .detail-form {
      .form-section {
        margin-bottom: 12px;

        .section-title {
          font-size: 16px;
          margin-bottom: 20px;
        }

        .section-title__first {
          margin-bottom: 30px;
        }
      }
    }
  }
}

.full-width {
  width: 100%;
}

:deep(.el-cascader) {
  width: 220px;
}

:deep(.insurance-type-col .el-cascader) {
  width: 100% !important;
}

:deep(.el-date-editor.el-input, .el-date-editor.el-input__wrapper) {
  width: 100% !important;
}

.address-detail {
  min-width: 310px;
  margin-left: 10px;
  flex: 1;
}

.address-col {
  display: flex;

  :deep(.el-form-item):nth-child(2) {
    .el-form-item__content {
      margin-left: 0 !important;
    }
  }
}

:deep(.el-upload-list.el-upload-list--picture-card.is-disabled) {
  .el-upload.el-upload--picture-card.is-disabled {
    cursor: not-allowed !important;
  }
}
</style>
