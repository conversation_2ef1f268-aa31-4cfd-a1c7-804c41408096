<template>
  <div class="exception-page">
    <div class="img">
      <img :src="Config[type].img + '?' + new Date()" />
      <slot></slot>
    </div>
    <div class="content">
      <h1>{{ Config[type].title }}</h1>
      <div class="desc" v-html="Config[type].desc"></div>
      <div class="action">
        <el-button type="primary" @click="backHome">返回首页</el-button>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { useRouter } from 'vue-router'
import Config from './type-config'
import { ROUTER_PATH } from '@/router/router-path'

defineOptions({
  name: 'Exception',
})

const props = defineProps<{
  type: keyof typeof Config
}>()

const router = useRouter()
const emit = defineEmits<{ 'back-home': [payload: keyof typeof Config] }>()

const backHome = () => {
  router.replace(ROUTER_PATH.HOME)
  emit('back-home', props.type)
}
</script>

<style lang="scss" scoped>
.exception-page {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: calc(100vh - 110px);
}

.content {
  margin-left: 30px;
}
.desc {
  margin-bottom: 20px;
  font-size: 18px;
  font-weight: 600;
}
</style>
./type-config
