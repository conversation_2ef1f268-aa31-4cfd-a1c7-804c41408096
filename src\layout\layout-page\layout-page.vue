<script lang="ts" setup>
import { useTabsStore } from '@/stores/tabs.store'
import type { ScrollbarInstance } from 'element-plus'
import { storeToRefs } from 'pinia'

const scrollbarRef = ref<ScrollbarInstance | null>(null)

const { cachedViews } = storeToRefs(useTabsStore())

const route = useRoute()

watch(route, () => {
  setTimeout(() => {
    scrollbarRef.value?.update()
  }, 600)
})
</script>

<template>
  <div class="layout_page-container">
    <el-scrollbar ref="scrollbarRef" always>
      <router-view v-slot="{ Component }">
        <transition name="fade-transform" mode="out-in">
          <keep-alive :include="cachedViews">
            <component :key="$route.fullPath" :is="Component"></component>
          </keep-alive>
        </transition>
      </router-view>
    </el-scrollbar>
  </div>
</template>

<style lang="scss" scoped>
.layout_page-container {
  height: calc(100vh - 50px - 34px);
}
</style>
