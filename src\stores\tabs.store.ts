import { getRouterName } from '@/utils/router-utils'
import { defineStore } from 'pinia'
import type { RouteLocationNormalized } from 'vue-router'

interface TabsStoreState {
  /**标签页列表 */
  visitedViews: RouteLocationNormalized[]
  /**缓存列表 */
  cachedViews: string[]
}

export const useTabsStore = defineStore('tabsStore', {
  state(): TabsStoreState {
    return {
      visitedViews: [],
      cachedViews: [],
    }
  },

  actions: {
    /**添加tab */
    addView(view: RouteLocationNormalized) {
      this.addVisitedView(view)
      this.addCachedView(view)
    },
    /**添加tab */
    addVisitedView(view: RouteLocationNormalized) {
      if (this.visitedViews.some((v) => v.path === view.path)) return
      this.visitedViews.push(view)
    },
    /**添加缓存 */
    addCachedView(view: RouteLocationNormalized) {
      if (!view.name) return
      const RouteName = getRouterName(view.name)
      if (this.cachedViews.includes(RouteName)) return
      if (!view.meta.noCache) {
        this.cachedViews.push(RouteName)
      }
    },

    /**删除当前 */
    delView(view: RouteLocationNormalized) {
      this.delVisitedView(view)
      this.delCachedView(view)
    },
    delVisitedView(view: RouteLocationNormalized) {
      for (const [i, v] of this.visitedViews.entries()) {
        if (v.path === view.path) {
          this.visitedViews.splice(i, 1)
          break
        }
      }
    },
    delCachedView(view: RouteLocationNormalized) {
      const index = this.getCachedViewIndex(view)
      index !== null && index > -1 && this.cachedViews.splice(index, 1)
    },

    /**删除其他 */
    delOthersViews(view: RouteLocationNormalized) {
      this.delOthersVisitedViews(view)
      this.delOthersCachedViews(view)
    },
    delOthersVisitedViews(view: RouteLocationNormalized) {
      this.visitedViews = this.visitedViews.filter((v) => {
        return v.meta.affix || v.path === view.path
      })
    },
    delOthersCachedViews(view: RouteLocationNormalized) {
      const index = this.getCachedViewIndex(view)
      if (index !== null && index > -1) {
        this.cachedViews = this.cachedViews.slice(index, index + 1)
      } else {
        this.cachedViews = []
      }
    },

    /**删除所有 */
    delAllViews() {
      this.delAllVisitedViews()
      this.delAllCachedViews()
    },
    delAllVisitedViews() {
      this.visitedViews = this.visitedViews.filter((v) => v.meta.affix)
    },
    delAllCachedViews() {
      this.cachedViews = []
    },

    /**获取某个tab在cachedViews的索引 */
    getCachedViewIndex(view: RouteLocationNormalized) {
      if (!view.name) return null
      const RouteName = getRouterName(view.name)
      return this.cachedViews.indexOf(RouteName)
    },
  },
})
