<script setup lang="ts">
/**
 * 租赁订单管理 - 表格
 */
import {
  ConfirmLeaseOfCaregiverPerson,
  RentalOrderItem,
  RentalOrderStatus,
  RentalOrderStatusConfig,
  RentalOrderStatusOptions,
  ReqRentalOrdersByPage
} from '@/api/dto/rental-management/rental-order-management.dto.ts'
import { useMountDialog } from '@/hooks/useMountDialog.ts'
import RentalConfirmDialog from '@/views/rental-management/rental-order-management/components/rental-confirm-dialog.vue'
import RentalModifyDialog from '@/views/rental-management/rental-order-management/components/rental-modify-dialog.vue'
import RentalReplacementDialog from '@/views/rental-management/rental-order-management/components/rental-replacement-dialog.vue'
import RentalCreateDialog from '@/views/rental-management/rental-order-management/orders/components/rental-create-dialog.vue'
import { maskIdCard } from '@/utils/index.ts'
import { toRaw } from 'vue'

enum ItemActionType {
  /* 详情 */
  DETAIL = 'detail',
  /* 确定租赁 */
  CONFIRM = 'confirm',
  /* 换人审核 */
  REPLACEMENT = 'replacement',
  /* 服务完成 */
  FINISHED = 'finished',
  /* 修改审核 */
  MODIFY = 'modify',
  /* 取消订单 */
  CANCEL = 'cancel'
}

const emits = defineEmits<{
  (e: 'search', data: { currentPage: number; pageSize: number; searchData: ReqRentalOrdersByPage }): void
  (e: 'toDetail', data: RentalOrderItem): void
  (
    e: 'confirm',
    data: {
      /* ID */
      inpatientCaregiverId: string
      /* 陪护人ID */
      inpatientCaregiverPersonId: string
    },
    resolve: () => void,
    reject: () => void
  ): void
  (
    e: 'replacement',
    data: {
      inpatientCaregiverId: string
      inpatientCaregiverPersonId: string
      reviewOperate: boolean
    },
    resolve: () => void,
    reject: () => void
  ): void
  (
    e: 'finished',
    data: {
      inpatientCaregiverId: string
    }
  ): void
  (
    e: 'modify',
    data: {
      inpatientCaregiverUpdateId: string
      inpatientCaregiverId: string
      reviewOperate: boolean
    },
    resolve: () => void,
    reject: () => void
  ): void
  (e: 'cancel', data: { inpatientCaregiverId: string }): void
  (
    e: 'create',
    data: {
      createData: any
    },
    resolve: () => void,
    reject: () => void
  ): void
}>()

const props = withDefaults(
  defineProps<{
    listData: RentalOrderItem[]
    total: number
    // 是否固定状态值，如果是，则搜索时候传固定值且不显示状态选项
    fixedStatusValue?: number
  }>(),
  {
    listData: () => [],
    total: 0,
    fixedStatusValue: undefined
  }
)

const { open: openConfirmRentalDialog } = useMountDialog(RentalConfirmDialog)
const { open: openModifyRentalDialog } = useMountDialog(RentalModifyDialog)
const { open: openReplacementRentalDialog } = useMountDialog(RentalReplacementDialog)
const { open: openCreateRentalDialog } = useMountDialog(RentalCreateDialog)

// 搜索表单
const searchFormData = reactive<ReqRentalOrdersByPage>({
  platFormOrderNo: '',
  patientName: '',
  department: '',
  patientIdNo: '',
  orderStatus: props.fixedStatusValue,
  page: 1,
  rows: 10
})

// 分页
const paginationData = reactive({
  currentPage: 1,
  pageSize: 10
})

onMounted(() => {
  // 默认加载页面时搜索
  emitSearch()
})

// 搜索
function handleSearch() {
  paginationData.currentPage = 1
  emitSearch()
}

// 新建订单
function handleCreateOrder() {
  openCreateRentalDialog({
    confirmCallback: (data: any) => {
      return new Promise<void>((resolve, reject) => {
        // 父组件可能是异步操作，把 resolve 和 reject 传给子组件，子组件可以调用
        emits('create', { createData: data }, resolve, reject)
      })
    }
  })
}

// 重置
function handleReset() {
  searchFormData.platFormOrderNo = ''
  searchFormData.patientName = ''
  searchFormData.patientIdNo = ''
  searchFormData.department = ''
  searchFormData.orderStatus = undefined
  emitSearch()
}

function emitSearch() {
  emits('search', {
    currentPage: paginationData.currentPage,
    pageSize: paginationData.pageSize,
    searchData: toRaw(searchFormData)
  })
}

function handleItemAction(itemAction: ItemActionType, itemData: RentalOrderItem) {
  switch (itemAction) {
    /* 确定租赁 */
    case ItemActionType.CONFIRM:
      openConfirmRentalDialog({
        rentalData: itemData,
        confirmCallback: (selectedPerson: ConfirmLeaseOfCaregiverPerson) => {
          return new Promise<void>((resolve, reject) => {
            // 父组件可能是异步操作，把 resolve 和 reject 传给子组件，子组件可以调用
            emits(
              'confirm',
              {
                inpatientCaregiverId: itemData.inpatientCaregiverId,
                inpatientCaregiverPersonId: selectedPerson.inpatientCaregiverPersonId
              },
              resolve,
              reject
            )
          })
        }
      })
      break
    /* 换人审核 */
    case ItemActionType.REPLACEMENT:
      openReplacementRentalDialog({
        inpatientCaregiverId: itemData.inpatientCaregiverId,
        confirmCallback: (inpatientCaregiverId: string, inpatientCaregiverPersonId: string, reviewOperate: boolean) => {
          return new Promise<void>((resolve, reject) => {
            // 父组件可能是异步操作，把 resolve 和 reject 传给子组件，子组件可以调用
            emits(
              'replacement',
              {
                inpatientCaregiverId,
                inpatientCaregiverPersonId,
                reviewOperate
              },
              resolve,
              reject
            )
          })
        }
      })
      break
    /* 服务完成 */
    case ItemActionType.FINISHED:
      emits('finished', {
        inpatientCaregiverId: itemData.inpatientCaregiverId
      })
      break
    /* 修改审核 */
    case ItemActionType.MODIFY:
      openModifyRentalDialog({
        inpatientCaregiverId: itemData.inpatientCaregiverId,
        confirmCallback: (inpatientCaregiverUpdateId: string, inpatientCaregiverId: string, reviewOperate: boolean) => {
          return new Promise<void>((resolve, reject) => {
            // 父组件可能是异步操作，把 resolve 和 reject 传给子组件，子组件可以调用
            emits(
              'modify',
              {
                inpatientCaregiverUpdateId,
                inpatientCaregiverId,
                reviewOperate
              },
              resolve,
              reject
            )
          })
        }
      })
      break
    /* 取消订单 */
    case ItemActionType.CANCEL:
      emits('cancel', {
        inpatientCaregiverId: itemData.inpatientCaregiverId
      })
      break
    /* 详情 */
    case ItemActionType.DETAIL:
      emits('toDetail', itemData)
      break
    default:
      break
  }
}

function handleSizeChange() {
  // 切换每页条数时把当前页重置回 1
  paginationData.currentPage = 1
  emitSearch()
}

function handleCurrentChange() {
  emitSearch()
}

defineExpose({
  emitSearch
})
</script>

<template>
  <div class="page-container">
    <el-form inline :model="searchFormData">
      <el-form-item label="平台单号：">
        <el-input
          v-model="searchFormData.platFormOrderNo"
          class="input-container"
          placeholder="请输入"
          size="large"
          clearable
        />
      </el-form-item>
      <el-form-item label="住院人姓名：">
        <el-input
          v-model="searchFormData.patientName"
          class="input-container"
          placeholder="请输入"
          size="large"
          clearable
        />
      </el-form-item>
      <el-form-item label="住院科室：">
        <el-input
          v-model="searchFormData.department"
          class="input-container"
          placeholder="请输入"
          size="large"
          clearable
        />
      </el-form-item>
      <el-form-item label="证件号：">
        <el-input
          v-model="searchFormData.patientIdNo"
          class="input-container"
          placeholder="请输入"
          size="large"
          clearable
        />
      </el-form-item>
      <el-form-item label="状态：" v-if="fixedStatusValue === undefined">
        <el-select
          v-model="searchFormData.orderStatus"
          class="input-container"
          placeholder="请选择"
          size="large"
          clearable
        >
          <el-option
            v-for="item in RentalOrderStatusOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button v-auth.disabled="'InpatientCaregiverByPage'" size="large" type="primary" @click="handleSearch">
          搜索
        </el-button>
        <el-button v-auth.disabled="'InpatientCaregiverByPage'" size="large" @click="handleReset">重置</el-button>
        <el-button
          v-auth.disabled="'CreateInpatientCaregiverOrder'"
          size="large"
          type="success"
          @click="handleCreateOrder"
          >创建订单</el-button
        >
      </el-form-item>
    </el-form>

    <BaseTable
      class="table-container"
      :data="listData"
      border
      width="100%"
      height="520"
      :tooltip-options="{ placement: 'top-start' }"
    >
      <el-table-column prop="platformOrderNo" label="平台单号" width="200" fixed="left" show-overflow-tooltip />
      <el-table-column prop="patientName" label="住院人姓名" width="200" />
      <el-table-column prop="patientIdNo" label="证件号" width="200">
        <template #default="scope">
          {{ maskIdCard(scope.row.patientIdNo) }}
        </template>
      </el-table-column>
      <el-table-column prop="department" label="住院科室" width="200" />
      <el-table-column prop="serverType" label="陪护类型" width="200" />
      <el-table-column prop="leaseTime" label="租赁时间" width="240" />
      <el-table-column prop="orderStatus" label="状态" width="200">
        <template #default="scope">
          <el-tag
            :type="RentalOrderStatusConfig[scope.row.orderStatus]?.tagType || RentalOrderStatusConfig.default.tagType"
          >
            {{ RentalOrderStatusConfig[scope.row.orderStatus]?.label || RentalOrderStatusConfig.default.label }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="caregiverPersonName" label="陪护人名称" width="200">
        <template #default="scope">
          {{ scope.row.caregiverPersonName || '-' }}
        </template>
      </el-table-column>
      <el-table-column prop="payFee" label="订单金额（元）" width="200" />
      <el-table-column prop="createTime" label="下单时间" width="200" />

      <el-table-column label="操作" fixed="right" min-width="280">
        <template #default="scope">
          <el-button
            v-auth.disabled="'InpatientCaregiverOrderInfo'"
            size="small"
            @click="handleItemAction(ItemActionType.DETAIL, scope.row)"
            >详情</el-button
          >
          <el-button
            v-if="scope.row.orderStatus === RentalOrderStatus.RESERVED"
            v-auth.disabled="'InpatientCaregiverConfirmLease'"
            size="small"
            type="success"
            @click="handleItemAction(ItemActionType.CONFIRM, scope.row)"
            >确定租赁
          </el-button>
          <el-button
            v-if="scope.row.isReplacePersonReviewing"
            v-auth.disabled="'PersonnelReplacementReview'"
            size="small"
            type="warning"
            @click="handleItemAction(ItemActionType.REPLACEMENT, scope.row)"
            >换人审核
          </el-button>
          <el-button
            v-if="scope.row.orderStatus === RentalOrderStatus.ESCORTING"
            v-auth.disabled="'InpatientCaregiverServiceCompleted'"
            size="small"
            type="primary"
            @click="handleItemAction(ItemActionType.FINISHED, scope.row)"
            >服务完成
          </el-button>
          <el-button
            v-if="scope.row.isUpdateReviewing"
            v-auth.disabled="'InpatientCaregiverModifyReview'"
            size="small"
            type="warning"
            @click="handleItemAction(ItemActionType.MODIFY, scope.row)"
            >修改审核
          </el-button>
          <el-button
            v-if="scope.row.orderStatus === RentalOrderStatus.AWAITING_PAYMENT"
            v-auth.disabled="'InpatientCaregiverCancelOrder'"
            size="small"
            type="warning"
            @click="handleItemAction(ItemActionType.CANCEL, scope.row)"
            >取消订单
          </el-button>
        </template>
      </el-table-column>
    </BaseTable>

    <base-pagination
      v-model:current-page="paginationData.currentPage"
      v-model:page-size="paginationData.pageSize"
      :total="total"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />
  </div>
</template>

<style scoped lang="scss">
:deep(.el-form--inline .el-form-item) {
  margin-right: 20px;

  &:last-child {
    margin-right: 0;
  }
}

.page-container {
  padding: 20px 0;
}

.input-container {
  width: 180px;
}

.table-container {
  margin: 12px 0 30px;
}
</style>
