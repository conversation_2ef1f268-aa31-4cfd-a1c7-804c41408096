<script setup lang="ts">
import {
  requestDeclarationApplyFileByPage,
  requestDeclarationApplyFileDetail
} from '@/api/declaration-data-management.api.ts'
import { DeclarationApplyFileItem } from '@/api/dto/declaration-data-management.dto.ts'
import { OperaRow } from '@/api/dto/index.dto'
import { useFormHook } from '@/hooks/useForm'
import { useMountDialog } from '@/hooks/useMountDialog.ts'
import { usePaginationHook } from '@/hooks/usePagination'
import DeclarationDialog from '@/views/declaration-data-management/list/components/declaration-dialog.vue'

const { open: openDeclarationDialog } = useMountDialog(DeclarationDialog)

const { formRef, resetForm } = useFormHook()
const { currentPage, pageSize, total, resetPagination } = usePaginationHook()

const formData = reactive({
  fileName: '',
  admissionNo: '',
  admissionName: ''
})

const tableData = ref<DeclarationApplyFileItem[]>([])

watch([currentPage, pageSize], () => {
  handleSearch()
})

onMounted(() => {
  handleSearch()
})

/**
 * 搜索 申报资料
 */
async function handleSearch() {
  try {
    const {
      data,
      pageSize: resPageSize,
      pageIndex,
      recordCount
    } = await requestDeclarationApplyFileByPage({
      fileName: formData.fileName,
      admissionName: formData.admissionName,
      admissionNo: formData.admissionNo,
      page: currentPage.value,
      rows: pageSize.value
    })
    console.log('搜索 申报资料 列表：', data)
    tableData.value = data
    currentPage.value = pageIndex
    pageSize.value = resPageSize
    total.value = recordCount
  } catch (e) {
    console.error('搜索 申报资料 列表失败：', e)
    tableData.value = []
    resetPagination()
  }
}

function handleReset() {
  resetForm()
  handleSearch()
}

async function handleDetail(row: OperaRow<any>) {
  try {
    const declarationFiles = await requestDeclarationApplyFileDetail({
      applefileId: row.applefileId,
      fileName: row.fileName
    })
    if (declarationFiles) {
      openDeclarationDialog({
        fileItem: declarationFiles
      })
    }
  } catch (e) {
    console.error('获取申报资料详情失败：', e)
    return null
  }
}
</script>

<template>
  <div class="layout-page-pd">
    <el-form ref="formRef" :model="formData" inline>
      <el-form-item prop="fileName" label="申报资料名称：">
        <el-input class="input-container" size="large" v-model="formData.fileName" clearable />
      </el-form-item>
      <el-form-item prop="admissionName" label="住院人姓名：">
        <el-input class="input-container" size="large" v-model="formData.admissionName" clearable />
      </el-form-item>

      <el-form-item prop="admissionNo" label="住院号：">
        <el-input class="input-container" size="large" v-model="formData.admissionNo" clearable />
      </el-form-item>

      <el-form-item>
        <el-button v-auth.disabled="'DeclarationApplyFileByPage'" type="primary" size="large" @click="handleSearch">
          搜索
        </el-button>
        <el-button v-auth.disabled="'DeclarationApplyFileByPage'" size="large" @click="handleReset">重置</el-button>
      </el-form-item>
    </el-form>

    <BaseTable :data="tableData" border height="600">
      <el-table-column prop="fileName" label="申报资料名称" width="404" fixed="left"></el-table-column>
      <el-table-column prop="admissionName" label="住院人姓名"></el-table-column>
      <el-table-column prop="admissionNo" label="住院号"></el-table-column>
      <el-table-column prop="hospitalizationDepartmentName" label="住院科室"></el-table-column>
      <el-table-column prop="submissionTime" label="提交时间"></el-table-column>

      <el-table-column label="操作" min-width="80" fixed="right">
        <template #default="{ row }: { row: OperaRow<any> }">
          <el-button v-auth.disabled="'DeclarationApplyFileDetail'" plain @click="handleDetail(row)"> 查看</el-button>
        </template>
      </el-table-column>
    </BaseTable>

    <BasePagination v-model:current-page="currentPage" v-model:page-size="pageSize" :total="total" />
  </div>
</template>

<style scoped lang="scss">
.input-container {
  width: 200px;
}
</style>
