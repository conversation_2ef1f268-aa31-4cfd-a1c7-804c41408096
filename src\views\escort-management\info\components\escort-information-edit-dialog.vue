<script setup lang="ts">
import { EscortInfoStatus } from '@/api/dto/escort-management.dto.ts'
import { useFormHook } from '@/hooks/useForm.ts'
import { FormRules } from 'element-plus'

// 使用any类型，因为这里有两种不同的数据格式：从API获取的后端格式和UI组件使用的前端格式
const props = defineProps<{
  detailInfo: any
  title: string
  departmentOptions: {
    value: string
    label: string
  }[]
  confirmCallback: (data: any) => Promise<void>
}>()

const { formRef } = useFormHook()

const formData = ref<any>({})

const visible = defineModel({ default: false })

watch(
  visible,
  (v) => {
    if (v) {
      // 重置表单，避免在点击编辑后再点击新增时候，因为弹窗组件没有销毁导致数据不清空
      formRef.value?.resetFields()
      formData.value = JSON.parse(JSON.stringify(props.detailInfo))
    }
  },
  {
    immediate: true
  }
)

const rules = reactive<FormRules>({
  departmentName: [{ required: true, message: '请选择住院科室', trigger: 'change' }],
  serviceName: [{ required: true, message: '请输入服务名称', trigger: 'blur' }],
  price: [{ required: true, message: '请输入单价', trigger: 'blur' }],
  total: [{ required: true, message: '请输入总数量', trigger: 'blur' }],
  status: [{ required: true, message: '请选择状态', trigger: 'change' }],
  remark: [{ required: false, message: '请输入备注', trigger: 'blur' }]
})

const handleConfirm = async (done: (keepVisible: boolean) => void) => {
  await formRef.value!.validate(async (valid) => {
    if (valid) {
      try {
        await props.confirmCallback(JSON.parse(JSON.stringify(formData.value)))
        done(false)
        setTimeout(() => {
          formRef.value!.resetFields()
        })
      } catch (error) {
        // 结束按钮 loading，但是不关闭弹窗
        done(true)
      }
    } else {
      done(true)
    }
  })
}
</script>

<template>
  <CommonFormDialog
    v-model="visible"
    :title="title"
    width="732px"
    showLoading
    :showCancelTips="false"
    @confirm="handleConfirm"
  >
    <el-form ref="formRef" :model="formData" :rules="rules" label-width="auto" label-position="right" size="large">
      <el-row>
        <el-col :span="24">
          <el-form-item label="住院科室：" prop="departmentName">
            <el-select v-model="formData.departmentName" placeholder="请选择" clearable>
              <el-option
                v-for="department in departmentOptions"
                :key="department.value"
                :label="department.label"
                :value="department.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row>
        <el-col :span="24">
          <el-form-item label="服务名称：" prop="serviceName">
            <el-input v-model="formData.serviceName" clearable placeholder="请输入" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row>
        <el-col :span="12">
          <el-form-item label="单价：" prop="price">
            <el-input v-model="formData.price" clearable placeholder="请输入">
              <template #append>元/天/人</template>
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="总数量：" prop="total">
            <el-input v-model="formData.total" clearable placeholder="请输入" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row>
        <el-col :span="12">
          <el-form-item label="状态：" prop="status">
            <el-radio-group v-model="formData.status">
              <el-radio :value="EscortInfoStatus.ENABLE">启用</el-radio>
              <el-radio :value="EscortInfoStatus.DISABLE">禁用</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row>
        <el-col :span="24">
          <el-form-item label="备注：" prop="remark">
            <el-input v-model="formData.remark" clearable type="textarea" placeholder="请输入" />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </CommonFormDialog>
</template>

<style lang="scss" scoped></style>
