<script lang="ts" setup>
import { usePaginationHook } from '@/hooks/usePagination'
import SearchBar, { type SearchData } from './components/search-bar.vue'
import { useLoadingHook } from '@/hooks/useLoading'
import type { getSysDataAuditInfoLogForPageOutput } from '@/api/dto/system/audit-log.dto'
import { getSysDataAuditInfoLogForPageApi } from '@/api/system/audit-log.api'

const { currentPage, pageSize, total } = usePaginationHook(20)

const { loading, loadingFunc } = useLoadingHook()

const searchData = reactive<SearchData>({
  tableName: '',
  operationType: '',
  operateTime: ['', ''],
})

const tableData = ref<getSysDataAuditInfoLogForPageOutput>([])

const getSysDataAuditInfoLogForPage = async () => {
  try {
    const { data, recordCount } = await getSysDataAuditInfoLogForPageApi(
      {
        tableName: searchData.tableName,
        operationType: searchData.operationType,
        operTimeBegin: searchData.operateTime[0],
        operTimeEnd: searchData.operateTime[1],
        page: currentPage.value,
        rows: pageSize.value,
      },
      { retonly: false, loading: loadingFunc, showNoData: false }
    )

    tableData.value = data
    total.value = recordCount!
  } catch (error) {
    tableData.value = []
    total.value = 1
  }
}
getSysDataAuditInfoLogForPage()

const handleSearch = (data: SearchData) => {
  for (const [k, v] of Object.entries(data)) {
    const key = k as keyof SearchData
    const value = v as any
    searchData[key] = value
  }
  getSysDataAuditInfoLogForPage()
}

watch(currentPage, () => {
  getSysDataAuditInfoLogForPage()
})
</script>

<template>
  <div class="operation_log-container layout-page-pd">
    <SearchBar @search="handleSearch" />

    <BaseTable v-loading="loading" :data="tableData" border scrollbar-always-on height="680">
      <el-table-column prop="tableName" label="表名" width="200"></el-table-column>
      <el-table-column prop="operTime" label="操作时间" width="200"></el-table-column>
      <el-table-column prop="operationType" label="操作类型" show-overflow-tooltip width="100"></el-table-column>
      <el-table-column prop="sqlStatement" label="SQL语句" show-overflow-tooltip></el-table-column>
      <el-table-column prop="sqlParams" label="SQL参数" show-overflow-tooltip></el-table-column>
    </BaseTable>

    <BasePagination v-model:current-page="currentPage" v-model:page-size="pageSize" :total="total" />
  </div>
</template>
