<script setup lang="ts">
import { OperaRow } from '@/api/dto/index.dto'
import { useFormHook } from '@/hooks/useForm'
import { usePaginationHook } from '@/hooks/usePagination'
import { ROUTER_PATH } from '@/router/router-path'

const router = useRouter()

const { formRef, resetForm } = useFormHook()
const { currentPage, pageSize, total } = usePaginationHook()

const formData = reactive({
    title: '',
    department: '',
    status: '',
})

const loading = ref(false)
const tableData = ref([
  {
    materialName: '李小明',
    hospitalizationNumber: '440123440'
  }
])

function handleSearch() {}

function handleReset() {
  resetForm()
}
// 查看详情
function handleDetail() {
  router.push({
    path: ROUTER_PATH.PUBLIC_ED_ARTICLE_DETAIL
  })
}
// 下架
function handlePullOff() {

}
// 删除
function handleDelete() {

}
</script>

<template>
  <div class="role_manage-container layout-page-pd">
    <el-form ref="formRef" :model="formData" inline>
      <el-form-item prop="title" label="标题：">
        <el-input v-model="formData.title" @keydown.enter="handleSearch"></el-input>
      </el-form-item>
      <el-form-item prop="department" label="住院科室：">
        <el-input v-model="formData.department" @keydown.enter="handleSearch"></el-input>
      </el-form-item>

      <el-form-item prop="status" label="状态：">
        <el-select v-model="formData.status">
          <el-option label="全部" value=""></el-option>
          <el-option label="已启用" :value="true"></el-option>
          <el-option label="未启用" :value="false"></el-option>
        </el-select>
      </el-form-item>

      <el-form-item>
        <el-button v-auth.disabled="'getSysRoleByPage'" type="primary" @click="handleSearch">搜索</el-button>
        <el-button v-auth.disabled="'getSysRoleByPage'" @click="handleReset">重置</el-button>
      </el-form-item>
    </el-form>

    <BaseTable v-loading="loading" :data="tableData" border height="630">
      <!--          <el-table-column type="selection" width="56" align="center" />-->
      <el-table-column prop="patientName" label="标题" width="404" fixed="left"></el-table-column>
      <el-table-column prop="patientName" label="缩略图"></el-table-column>
      <el-table-column prop="hospitalizationNumber" label="住院科室"> </el-table-column>
      <el-table-column prop="departmentName" label="阅读数"></el-table-column>
      <el-table-column prop="modifyTime" label="状态"></el-table-column>
      <el-table-column prop="modifyTime" label="文章类型"></el-table-column>
      <el-table-column prop="modifyTime" label="创建时间" min-width="200"></el-table-column>

      <el-table-column label="操作" min-width="240" fixed="right">
        <template #default="{ row }: { row: OperaRow<any> }">
          <el-button plain @click="handleDetail"> 查看 </el-button>
          <el-popconfirm title="是否确认下架?" @confirm="handlePullOff">
            <template #reference>
              <el-button type="warning" @click=""> 下架 </el-button>
            </template>
          </el-popconfirm>
          <el-popconfirm title="是否确认删除?" @confirm="handleDelete">
            <template #reference>
              <el-button type="danger" @click=""> 删除 </el-button>
            </template>
          </el-popconfirm>
        </template>
      </el-table-column>
    </BaseTable>

    <BasePagination v-model:current-page="currentPage" v-model:page-size="pageSize" :total="total" />
  </div>
</template>
