import * as hiPrintUtils from '@/utils/hi-print-plugin-utils.ts'
import { defineStore } from 'pinia'

export enum PrintMode {
  SILENT = 'silent',
  PREVIEW = 'preview'
}

export interface SilentPrinterConfig {
  // 入院卡打印机
  inpatientFormPrinter: string
  // 手腕带打印机
  wristbandPrinter: string
}

/**
 * 打印机状态
 *
 * 对打印机进行一层封装，相当于接口层
 */
export const usePrinterStore = defineStore(
  'printer',
  () => {
    // 打印客户端状态
    const printerClientConnected = ref<boolean>(false)
    // 保存的静默打印机列表
    const silentPrinterConfig = ref<SilentPrinterConfig>({
      // 入院卡打印机
      inpatientFormPrinter: '',
      // 手腕带打印机
      wristbandPrinter: ''
    })
    // 打印方式，静默打印模式 | 打印预览模式
    const printMode = ref<PrintMode>()

    // 设置打印机客户端连接状态回调，在打印机客户端连接/断开时回调
    function setPrinterClientStatusCallback() {
      if (printerClientConnected.value) {
        console.log('打印机客户端已连接，跳过此次开启请求')
        return
      }
      console.log('设置打印机客户端连接状态回调')
      hiPrintUtils.setHiPrintClientConnectCallback((isConnected: boolean) => {
        console.log('打印客户端连接状态：', isConnected)
        printerClientConnected.value = isConnected
      })
    }

    // 获取打印机列表
    async function getPrinterList() {
      return await hiPrintUtils.getHiPrinterList()
    }

    // 设置静默打印机配置
    function setSilentPrinterConfig(config: SilentPrinterConfig) {
      silentPrinterConfig.value = config
      console.log('静默打印机配置：', silentPrinterConfig.value)
    }

    // 设置打印方式
    function setPrintMode(mode: PrintMode) {
      printMode.value = mode
      console.log('打印方式：', printMode.value)
    }

    // 关闭打印机连接
    function closePrinterClient() {
      return hiPrintUtils.closeHiPrinterClient()
    }

    return {
      printerClientConnected,
      silentPrinterConfig,
      printMode,
      setPrinterClientStatusCallback,
      getPrinterList,
      setSilentPrinterConfig,
      setPrintMode,
      closePrinterClient
    }
  },
  {
    persist: {
      pick: ['silentPrinterConfig', 'printMode']
    }
  }
)
