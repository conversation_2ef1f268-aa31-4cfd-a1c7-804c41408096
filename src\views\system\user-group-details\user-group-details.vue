<script lang="ts" setup>
import {
  getManagerForPageApi,
  getRolesForListApi,
  updateSysManagerGroupApi,
  addSysManagerGroupApi,
  getSysManagerGroupForUpdateApi
} from '@/api/system/user-group.api'
import type {
  GetRolesForListOutput,
  GetManagerForPageOutput,
  AddOrUpdateSysManagerGroupInput
} from '@/api/dto/system/user-group.dto'

import ChooseUserPicker from '@/components/choose-user-picker'
import type { SearchPayload } from '@/components/choose-user-picker/choose-user-picker.vue'
import type { LoadingFuncType } from '@/hooks/useLoading'
import { useFormHook } from '@/hooks/useForm'
import { appMessage } from '@/hooks/useNaiveApi'
import type { FormRules } from 'element-plus'

type RoleListType = { roleName: string; roleId: string }
type ManagerListType = { account: string; managerId: string }

const { formRef, validate } = useFormHook()

const route = useRoute()
const router = useRouter()

const managerGroupId = computed(() => route.query.managerGroupId as string)

const formData = reactive<
  AddOrUpdateSysManagerGroupInput & { roleList: RoleListType[]; managerList: ManagerListType[] }
>({
  managerGroupName: '',
  roleList: [],
  roleIds: '',
  enabledMark: false,
  isSys: false,
  sortCode: 0,
  managerList: [],
  managerIds: '',
  description: ''
})
/**表单验证规则 */
const rules = reactive<FormRules<AddOrUpdateSysManagerGroupInput>>({
  managerGroupName: [{ required: true, message: '请输入用户组名称', trigger: 'blur' }]
})

const getSysManagerGroupForUpdate = async () => {
  try {
    const data = await getSysManagerGroupForUpdateApi({ managerGroupId: managerGroupId.value })
    formData.managerGroupName = data.managerGroupName
    formData.roleList = data.roleList?.map((item) => ({ roleName: item.name, roleId: item.id })) || []
    formData.enabledMark = data.enabledMark
    formData.isSys = data.isSys
    formData.sortCode = data.sortCode
    formData.managerList = data.managerList?.map((item) => ({ account: item.name, managerId: item.id })) || []
    formData.description = data.description
  } catch (error) {
    handleBack()
    appMessage.error('获取失败,请稍后重试')
  }
}

onMounted(() => {
  if (managerGroupId.value) {
    getSysManagerGroupForUpdate()
  }
})

/**保存按钮权限控制 */
const saveButtonAuth = computed(() => {
  if (managerGroupId.value) {
    return 'updateSysManagerGroup'
  } else {
    return 'addSysManagerGroup'
  }
})

/**确认保存 */
const handleSave = async () => {
  try {
    await validate()
    const payload: AddOrUpdateSysManagerGroupInput = {
      managerGroupName: formData.managerGroupName,
      roleIds: formData.roleList.map((item) => item.roleId).join(','),
      enabledMark: formData.enabledMark,
      isSys: formData.isSys,
      sortCode: formData.sortCode,
      managerIds: formData.managerList.map((item) => item.managerId).join(','),
      description: formData.description
    }

    if (managerGroupId.value) {
      //修改
      payload.managerGroupId = managerGroupId.value
      await updateSysManagerGroupApi(payload)
      appMessage.success('修改成功')
    } else {
      // 新增
      await addSysManagerGroupApi(payload)
      appMessage.success('新增成功')
    }
    handleBack()
  } catch (error) {}
}

const handleBack = () => {
  router.go(-1)
}

const rolesList = ref<GetRolesForListOutput>([])
const getRolesForList = async (searchData: SearchPayload, loadingFucn: LoadingFuncType) => {
  try {
    rolesList.value = await getRolesForListApi({ roleName: searchData.keyword }, { loading: loadingFucn })
  } catch (error) {
    rolesList.value = []
  }
}

const managerTable = ref<GetManagerForPageOutput>([])
const total = ref(1)
const getManagerForPage = async (searchData: SearchPayload, loadingFucn: LoadingFuncType) => {
  try {
    const { data, recordCount } = await getManagerForPageApi(
      {
        managerKeyword: searchData.keyword,
        page: searchData.currentPage,
        rows: 5
      },
      { loading: loadingFucn, retonly: false }
    )
    managerTable.value = data
    total.value = recordCount!
  } catch (error) {
    managerTable.value = []
    total.value = 1
  }
}
</script>

<template>
  <div class="user_group_details-container">
    <div class="back-bar flex">
      <el-button v-auth="'addSysManagerGroup'" type="primary" @click="handleSave">确认保存</el-button>
      <el-button type="warning" @click="handleBack">返回</el-button>
    </div>

    <el-form ref="formRef" :model="formData" label-width="120px" :rules="rules" class="user_group_details-form">
      <el-form-item label="用户组名称：" prop="managerGroupName">
        <el-input v-model="formData.managerGroupName"></el-input>
      </el-form-item>

      <el-form-item label="选择角色：" prop="roleList">
        <ChooseUserPicker
          v-model="formData.roleList"
          :data="rolesList"
          selection-key="roleId"
          @search="getRolesForList"
        >
          <template #tag="{ item }">
            <span>{{ item.roleName }} </span>
          </template>

          <el-table-column type="selection" />
          <el-table-column label="用户名" prop="roleName" />
        </ChooseUserPicker>
      </el-form-item>

      <el-form-item label="是否启用：" prop="enabledMark">
        <el-switch v-model="formData.enabledMark" active-text="启用后用户组才能被选择"> </el-switch>
      </el-form-item>

      <el-form-item label="系统管理：" prop="isSys">
        <el-switch v-model="formData.isSys" active-text="开启后无法删除"> </el-switch>
      </el-form-item>

      <el-form-item label="排序：" prop="sortCode">
        <el-input-number v-model="formData.sortCode" controls-position="right" :min="0"></el-input-number>
      </el-form-item>

      <el-form-item label="选择用户：" prop="managerList">
        <ChooseUserPicker
          v-model="formData.managerList"
          :data="managerTable"
          selection-key="managerId"
          :total-page="total"
          @search="getManagerForPage"
        >
          <template #tag="{ item }">
            <span>{{ item.account }} </span>
          </template>

          <el-table-column type="selection" />
          <el-table-column label="账号" prop="account" width="120" />
          <el-table-column label="用户名" prop="realName" width="120" />
          <el-table-column label="所属部门" prop="organizationName" />
        </ChooseUserPicker>
      </el-form-item>

      <el-form-item label="备注:">
        <el-input
          v-model="formData.description"
          :autosize="{ minRows: 3, maxRows: 4 }"
          type="textarea"
          class="textareaClass"
          placeholder="请输入"
        ></el-input>
      </el-form-item>
    </el-form>
  </div>
</template>

<style lang="scss" scoped>
.user_group_details-container {
  .user_group_details-form {
    padding: 30px 20px;
  }
}
</style>
