<script lang="ts" setup>
import { usePaginationHook } from '@/hooks/usePagination'
import SearchBar, { type SearchData } from './components/search-bar.vue'
import BasePagination from '@/components/base-pagination'
import type { GetManagerGroupForPageOutput, GetManagerGroupItemModel } from '@/api/dto/system/user-group.dto'
import { deleteSysManagerGroupApi, getManagerGroupForPageApi } from '@/api/system/user-group.api'
import { useLoadingHook } from '@/hooks/useLoading'
import { ROUTER_PATH } from '@/router/router-path'
import { appMessage } from '@/hooks/useNaiveApi'
import { useViewUserGroupDialogHook } from './components/view-user-group-dialog'
import type { OperaRow } from '@/api/dto/index.dto'

const searchData = reactive<SearchData>({ managerGroupName: '', enabledMark: '' })

const { currentPage, pageSize, total } = usePaginationHook()

const userGroupList = ref<GetManagerGroupForPageOutput>([])
const { loading, loadingFunc } = useLoadingHook()
const getManagerGroupForPage = async () => {
  try {
    const { data, recordCount } = await getManagerGroupForPageApi(
      {
        page: currentPage.value,
        rows: pageSize.value,
        ...searchData,
      },
      { loading: loadingFunc, showNoData: false, retonly: false }
    )
    userGroupList.value = data
    total.value = recordCount!
  } catch (error) {
    userGroupList.value = []
    total.value = 1
  }
}

onMounted(() => {
  getManagerGroupForPage()
})

const handleSearch = (data: SearchData) => {
  for (const [k, v] of Object.entries(data)) {
    const key = k as keyof SearchData
    const value = v as any
    searchData[key] = value
  }
  getManagerGroupForPage()
}

watch([currentPage, pageSize], () => {
  getManagerGroupForPage()
})

const router = useRouter()

/**编辑用户组 */
const handleEditUserGroup = (target: OperaRow<GetManagerGroupItemModel>) => {
  router.push({ path: ROUTER_PATH.USER_GROUP_DETAILS, query: { managerGroupId: target.managerGroupId } })
}

const { open, close } = useViewUserGroupDialogHook()
onBeforeUnmount(close)
/**查看用户组 */
const handleViewsUserGroup = (target: OperaRow<GetManagerGroupItemModel>) => {
  open({
    title: `用户组——${target.managerGroupName}`,
    managerGroupId: target.managerGroupId,
  })
}

/**删除用户组 */
const handleDeleteUserGroup = async (target: OperaRow<GetManagerGroupItemModel>) => {
  try {
    await ElMessageBox.confirm('确定删除用户组吗?', '提示', { type: 'warning' })
    await deleteSysManagerGroupApi(
      { managerGroupId: target.managerGroupId },
      { loading: (flag) => (target.loading = flag) }
    )
    appMessage.success('删除用户组成功')
    getManagerGroupForPage()
  } catch (error) {}
}
</script>

<template>
  <div class="user_group-container layout-page-pd">
    <SearchBar @search="handleSearch" />

    <BaseTable v-loading="loading" :data="userGroupList" border height="680">
      <el-table-column prop="managerGroupName" label="用户组名称" />
      <el-table-column prop="enabledMark" label="启用状态">
        <template #default="{ row }: { row: GetManagerGroupItemModel }">
          <el-tag :type="row.enabledMark ? 'success' : 'danger'">{{ row.enabledMark ? '已启用' : '未启用' }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="createTime" label="创建时间" width="200" />
      <el-table-column prop="modifyTime" label="更新时间" width="200" />
      <el-table-column prop="description" label="备注" />
      <el-table-column label="操作" width="250">
        <template #default="{ row }: { row: OperaRow<GetManagerGroupItemModel> }">
          <el-button v-auth.disabled="'buttonClickToJump_updateSysManagerGroup'" @click="handleEditUserGroup(row)">
            编辑
          </el-button>
          <el-button v-auth.disabled="'getRelationForPageById'" type="primary" @click="handleViewsUserGroup(row)">
            查看
          </el-button>
          <el-button
            v-auth.disabled="'deleteSysManagerGroup'"
            :loading="row.loading"
            type="danger"
            @click="handleDeleteUserGroup(row)"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </BaseTable>

    <BasePagination v-model:current-page="currentPage" v-model:page-size="pageSize" :total="total" />
  </div>
</template>
