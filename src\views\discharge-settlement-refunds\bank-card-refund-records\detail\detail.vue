<script setup lang="ts">
/**
 * 出院结算退费 - 银行卡退费记录详情
 */
import { ResBankCardRefundRecordInfo } from '@/api/dto/discharge-settlement-refunds/bank-card-refund-records.dto.ts'
import { useMountDialog } from '@/hooks/useMountDialog'
import { appMessage } from '@/hooks/useNaiveApi.ts'
import { ROUTER_PATH } from '@/router/router-path.ts'
import BankCardRefundAuditDialog from '../components/bank-card-refund-audit-dialog.vue'
import BankCardRefundDetail from './components/bank-card-refund-detail.vue'

const router = useRouter()
const route = useRoute()

const { open: openBankCardRefundAuditDialog } = useMountDialog(BankCardRefundAuditDialog)

// 银行卡退费记录详情
const detailInfo = ref<ResBankCardRefundRecordInfo>({} as ResBankCardRefundRecordInfo)

let _bankCardRefundId = ''

onMounted(() => {
  console.log('银行卡退费记录列表传入 -> 详情：', route.query)
  if (!route.query.bankCardRefundId) {
    console.error('获取银行卡退费记录详情失败：未获取到银行卡退费记录 ID')
    appMessage.error('获取银行卡退费记录详情失败：银行卡退费记录 ID 为空')
    return
  }
  _bankCardRefundId = route.query.bankCardRefundId as string
  requestDetailData()
})

async function requestDetailData() {
  try {
    setTimeout(() => {
      detailInfo.value = {
        id: _bankCardRefundId,
        patientName: '张三',
        patientNo: '**********',
        patientDept: '内科',
        patientInTime: '2021-01-01',
        patientOutTime: '2021-01-01',
        totalAmount: 1000,
        medicalInsuranceAmount: 1000,
        depositAmount: 1000,
        refundAmount: 1000,
        accountName: '张三',
        accountBank: '中国银行',
        accountCardNo: '**********',
        accountPhone: '**********',
        status: 0,
        doctorName: '张三',
        bedNo: '**********',
        selfPayAmount: 1000,
        createTime: '2021-01-01',
        resCirculationInfoDTOList: [
          { status: 0, createTime: '2021-01-01', content: '待审核' },
          { status: 1, createTime: '2021-01-02', content: '审核通过' },
          { status: 2, createTime: '2021-01-03', content: '审核不通过' }
        ]
      }
      console.log('银行卡退费记录详情：', detailInfo.value)
    }, 1000)
  } catch (e) {
    console.error('获取银行卡退费记录详情失败：', e)
  }
}

function handleBack() {
  router.push({
    path: ROUTER_PATH.BANK_CARD_REFUND_RECORDS_LIST
  })
}

// 结算审核 - 银行卡退费记录
function handleAudit() {
  console.log('结算审核')
  openBankCardRefundAuditDialog({
    id: _bankCardRefundId,
    confirmCallback: (data: { auditStatus: number; rejectReason: string; done: () => void }) => {
      console.log('结算审核：', data)
      data.done()
    }
  })
}
</script>

<template>
  <div class="page-container">
    <div class="header">
      <el-button class="header-back-btn" type="warning" @click="handleBack">返回</el-button>
    </div>

    <BankCardRefundDetail :detail-info="detailInfo" @audit="handleAudit" />
  </div>
</template>

<style scoped lang="scss">
.page-container {
  height: calc(100vh - 50px - 34px);
  display: flex;
  flex-direction: column;
}

.header {
  height: 50px;
  background: #f4f4f5;
  display: flex;
  justify-content: right;
  align-items: center;
  padding: 0 20px;
  position: sticky;
  flex: none;
  z-index: 2;

  & .header-back-btn {
    width: 68px;
    height: 36px;
  }
}
</style>
