import {
  ReqGetInsuranceRegistrationByPage,
  ReqInsuranceRegistrationInfo,
  ReqRegistrationFailed,
  ReqRegistrationSuccessful,
  ResInsuranceRegistrationByPage,
  ResInsuranceRegistrationInfo,
  ResRegistrationFailed,
  ResRegistrationSuccessful,
  ResWaitingInsuranceRegistrationByPage
} from '@/api/dto/insurance-registration.dto.ts'
import { request } from '@/utils/axios-utils'
import type { ApiFunc } from 'axios'

/**
 * 收费员端 - 医保登记模块
 */

/** 获取所有医保住院类型 */
export const requestGetInsuranceTypeOptions: ApiFunc<undefined, string[]> = () => {
  return request({
    url: '/foying/web/InsuranceRegistration/getInsuranceTypes',
    method: 'get',
    data: {}
  })
}

/** 获取所有参保类别 */
export const requestGetInsuredPlaceTypes: ApiFunc<undefined, string[]> = () => {
  return request({
    url: '/foying/web/InsuranceRegistration/getInsuredPlaceTypes',
    method: 'get',
    data: {}
  })
}

/** 分页获取 医保登记信息 列表 - 未登记 */
export const requestWaitingInsuranceRegistrationByPage: ApiFunc<
  ReqGetInsuranceRegistrationByPage,
  ResWaitingInsuranceRegistrationByPage
> = (data) => {
  return request({
    url: '/foying/web/InsuranceRegistration/getInsuranceRegistrationByPage',
    method: 'post',
    data,
    retonly: false
  })
}

/** 分页获取 医保登记信息 列表 - 已登记 */
export const requestInsuranceRegistrationByPage: ApiFunc<
  ReqGetInsuranceRegistrationByPage,
  ResInsuranceRegistrationByPage
> = (data) => {
  return request({
    url: '/foying/web/InsuranceRegistration/getInsuranceRegistrationByPage',
    method: 'post',
    data,
    retonly: false
  })
}

/** 获取 医保登记信息 - 详情 */
export const requestInsuranceRegistrationInfo: ApiFunc<ReqInsuranceRegistrationInfo, ResInsuranceRegistrationInfo> = (
  data
) => {
  return request({
    url: '/foying/web/InsuranceRegistration/getInsuranceRegistrationInfo',
    method: 'post',
    data
  })
}

/** 医保登记信息 - 确定登记成功 */
export const requestInsuranceRegistrationSuccessful: ApiFunc<ReqRegistrationSuccessful, ResRegistrationSuccessful> = (
  data
) => {
  return request({
    url: '/foying/web/InsuranceRegistration/registrationSuccessful',
    method: 'post',
    data
  })
}

/** 医保登记信息 - 确定登记失败 */
export const requestInsuranceRegistrationFailed: ApiFunc<ReqRegistrationFailed, ResRegistrationFailed> = (data) => {
  return request({
    url: '/foying/web/InsuranceRegistration/registrationFailed',
    method: 'post',
    data
  })
}
