import type { App } from 'vue'
import type { Router } from 'vue-router'

import { loadGuards } from './router/guards/utils'
import guards from './router/guards/index.guards'
import authority from './directives/authority'
import { loadRoutes } from './utils/router-utils'

export interface RunTimeOptions {
  router: Router
}

function runTime({ router }: RunTimeOptions, app: App<Element>) {
  loadRoutes()

  // 加载路由守卫
  loadGuards(guards, { router })

  // 加载自定义指令
  app.directive('auth', authority)
}

export default runTime
