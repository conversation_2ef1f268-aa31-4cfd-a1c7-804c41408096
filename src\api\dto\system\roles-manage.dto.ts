import type { NavigationMenuItem } from '../navigation.dto'

export enum ROLE_TYPE_EUNM {
  SYSTEM_MANAGE = 'SystemManager',
  SUPER_MANAGE = 'SuperManager',
}

export interface GetSysRoleByPageInput {
  roleName: string
  enabledMark: boolean | ''
  page: number
  rows: number
}
export interface SysRoleByPageItemModel {
  createTime: string
  createUserName: string
  enCode: string
  enabledMark: boolean
  modifyTime: string
  modifyUserName: string
  roleId: string
  roleName: string
  roleType: ROLE_TYPE_EUNM
  roleTypeDes: string
}

export type GetSysRoleByPageOutput = SysRoleByPageItemModel[]

export interface GetSysNavigationForShowInput {
  roleId?: string
}

export type GetSysNavigationForShowOutput = NavigationMenuItem[]

export interface UpdateSysRoleAuthNavListModel {
  navigationId: string
  /**多个操作全选使用“逗号”拼接 */
  functionAuthIdList?: string
}
export interface UpdateSysRolePermissionInput {
  roleId: string
  roleType: ROLE_TYPE_EUNM
  navigationList: UpdateSysRoleAuthNavListModel[]
}

export interface AddSysRoleInput {
  roleType: ROLE_TYPE_EUNM
  enCode: string
  roleName: string
  navigationList: UpdateSysRoleAuthNavListModel[]
  enabledMark: boolean
  isSys: boolean
  sortCode: number
  description: string
}

export interface GetSysRoleForEditInput {
  id: string
}
export interface GetSysRoleForEditOutput {
  description: string
  enCode: string
  enabledMark: boolean
  isSys: boolean
  roleId: string
  roleName: string
  roleType: ROLE_TYPE_EUNM
  sortCode: number
}

export interface UpdateSysRoleInput {
  description: string
  enCode: string
  enabledMark: boolean
  id: string
  isSys: boolean
  roleName: string
  roleType: ROLE_TYPE_EUNM
  sortCode: number
}

export interface DeleteSysRoleInput {
  id: string
}

export interface GetSysManagerPageByRoleIdInput {
  keyword: string
  roleId: string
  page: number
  rows: number
}
export interface SysManagerPageByRoleIdModel {
  account: string
  enabledMark: boolean
  managerId: string
  realName: string
}

export type GetSysManagerPageByRoleIdOutput = SysManagerPageByRoleIdModel[]
