<script lang="ts" setup generic="T extends Record<string|number, any>">
import { ArrowDown, Search, CircleCloseFilled } from '@element-plus/icons-vue'
import type { InputInstance, TableInstance } from 'element-plus'
import BasePagination from '../base-pagination'
import { usePaginationHook } from '@/hooks/usePagination'
import { useLoadingHook, type LoadingFuncType } from '@/hooks/useLoading'
import type BaseTable from '../base-table'

export type SearchPayload = {
  keyword: string
  currentPage: number
  pageSize: number
}

interface Props {
  data: T[]
  selectionKey: string
  totalPage?: number
  tableHeight?: string
  asyncSearch?: boolean
  /**是否多选 */
  checkbox?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  asyncSearch: true,
  checkbox: true,
})

const modelValue = defineModel<T[]>({ required: true, default: [] })

const emit = defineEmits<{
  search: [payload: SearchPayload, loadingFunc: LoadingFuncType]
}>()

const keywordInputRef = ref<null | InputInstance>(null)
const baseTableRef = ref<InstanceType<typeof BaseTable>>()
const tableRef = ref<null | TableInstance>(null)
onMounted(() => {
  nextTick(() => {
    tableRef.value = baseTableRef.value!.tableRef!
  })
})

const keyword = ref('')
const { currentPage, total } = usePaginationHook()
const { loading, loadingFunc } = useLoadingHook()
const keywordChange = () => {
  if (props.asyncSearch) {
    emit('search', { keyword: keyword.value, currentPage: currentPage.value, pageSize: 5 }, loadingFunc)
  } else {
    visibleData.value = props.data.filter((item) => JSON.stringify(item).includes(keyword.value))
  }
}
watch(
  () => props.totalPage,
  (val) => {
    total.value = val || 1
  },
  { immediate: true }
)

const visible = ref(false)
watch(visible, async (flag) => {
  if (flag) {
    await nextTick()
    keywordInputRef.value?.focus()
    keywordChange()
  }
})

const keys = computed(() => modelValue.value.map((item) => item[props.selectionKey]))

/**用于显示的数据 */
const visibleData = ref<any[]>([])
watch(
  () => props.data,
  async (list) => {
    visibleData.value = list
    await nextTick()
    list.forEach((item) => {
      tableRef.value?.toggleRowSelection(item, keys.value.includes(item[props.selectionKey]))
    })
  }
)

const pushModelValue = (arr: T[]) => {
  const { selectionKey } = props
  const rawData = [...modelValue.value]

  arr.forEach((item) => {
    if (!keys.value.includes(item[selectionKey])) {
      rawData.push(item)
    }
  })

  modelValue.value = rawData
}

const popModelValue = (arr: T[]) => {
  const { selectionKey } = props

  arr = arr.map((item) => item[selectionKey])

  const target = modelValue.value.filter((item) => {
    return !arr.includes(item[selectionKey])
  })

  modelValue.value = target
}

const handleTagClose = (target: T) => {
  popModelValue([target])
}

const handleTableSelect = (_: T[], row: T) => {
  const { checkbox, selectionKey } = props
  if (checkbox) {
    if (keys.value.includes(row[selectionKey])) {
      popModelValue([row])
    } else {
      pushModelValue([row])
    }
  } else {
    tableRef.value?.clearSelection()
    tableRef.value?.toggleRowSelection(row, true)
    modelValue.value = [row]
  }
}

const handleTableSelectAll = (selection: T[]) => {
  if (selection.length) {
    pushModelValue(selection)
  } else {
    popModelValue(props.data)
  }
}

watch(currentPage, keywordChange)
</script>

<template>
  <el-popover v-model:visible="visible" width="805" trigger="click" class="choose_user_picker-container">
    <template #reference>
      <div class="tag-group-container" :class="{ active: visible, checkbox }">
        <XyzTransitionGroup class="tag-group" xyz="fade stagger-1">
          <div v-for="item in modelValue" :key="item[selectionKey]" class="tag">
            <slot name="tag" :item="item">{{ item }}</slot>
            <el-icon class="close-icon"><CircleCloseFilled @click.stop="handleTagClose(item)" /></el-icon>
          </div>
        </XyzTransitionGroup>

        <el-icon><ArrowDown class="arrow-icon" /></el-icon>
      </div>
    </template>

    <el-input ref="keywordInputRef" v-model="keyword" placeholder="请输入关键词回车搜索" @change="keywordChange">
      <template #suffix>
        <el-icon><Search /></el-icon>
      </template>
    </el-input>
    <BaseTable
      v-loading="loading"
      ref="baseTableRef"
      :data="visibleData"
      border
      :height="tableHeight || 342"
      :class="{ checkbox }"
      @select="handleTableSelect"
      @select-all="handleTableSelectAll"
    >
      <slot></slot>
    </BaseTable>

    <BasePagination
      v-if="totalPage"
      v-model:current-page="currentPage"
      :page-size="5"
      :total="total"
      layout="prev, pager, next, jumper"
    />
  </el-popover>
</template>

<style lang="scss" scoped>
.el-tooltip__trigger {
  display: flex;
  align-items: center;
  width: 100%;
  min-height: 38px;
  border-radius: 4px;
  padding: 0 8px 4px;
  padding-right: 20px;
  vertical-align: middle;
  border: 1px solid var(--el-border-color-light);
  transition: transform 0.25s;
  cursor: pointer;

  .close-icon {
    margin-left: 8px;
    font-size: 14px;
    color: #bfcbd9;
    cursor: pointer;
  }

  .arrow-icon {
    transition: transform 0.25s;
  }

  &.active {
    border-color: #1890ef;
    .arrow-icon {
      transform: rotateZ(180deg);
    }
  }
}

.tag-group {
  flex-grow: 1;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  .tag {
    flex-shrink: 0;
    margin-left: 5px;
    margin-top: 4px;
    padding: 0 10px;
    display: flex;
    align-items: center;
    background-color: var(--el-color-info-light-9);
    border: 1px solid var(--el-color-info-light-8);
    color: var(--el-color-info);
    font-size: 12px;
    line-height: 24px;
    border-radius: 4px;

    .close-btn {
      margin-left: 8px;
      font-size: 14px;
      color: var(--el-color-info);
      cursor: pointer;
    }
  }
}

.el-table:not(.checkbox) {
  :deep(.el-table__header) {
    th.el-table-column--selection .cell {
      display: none;
    }
  }
}

.tag-group-container:not(.checkbox) {
  .tag.xyz-out {
    position: absolute;
  }
}
</style>
