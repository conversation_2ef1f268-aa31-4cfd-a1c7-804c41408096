import type { ElMessageBoxOptions } from 'element-plus'

export const useMessageBoxBeforeCloseHook = (callback: () => Promise<any>) => {
  const beforeClose: ElMessageBoxOptions['beforeClose'] = async (action, instance, done) => {
    if (action === 'confirm') {
      try {
        instance.confirmButtonLoading = true
        instance.showCancelButton = false

        await callback()

        done()
      } finally {
        instance.confirmButtonLoading = false
        instance.showCancelButton = true
      }
    } else {
      done()
    }
  }

  return beforeClose
}
