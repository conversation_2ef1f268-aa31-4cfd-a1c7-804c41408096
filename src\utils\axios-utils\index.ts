import { useGlobalStore } from '@/stores/global.store'
import axios, {
  type AxiosRequestConfig,
  type AxiosResponse,
  type AxiosResponseTransformer,
  type BaseEntry
} from 'axios'
import { isArray, isFunction, isUndefined } from 'lodash-es'
import { errorRequest, successRequest } from './interceptors-request'
import { errorResponse, successResponse } from './interceptors-response'
import router from '@/router'
// import { useUserStore } from '@/stores/user.store'
import JSONbig from 'json-bigint'

/**请求配置 */
const axiosConfig: AxiosRequestConfig = {
  autoCloseLoading: true,
  retonly: true,
  mute: false,
  showNoData: true,
  loading: true,
  method: 'post',
  injectBaseParams: true,
  responseStructure: 'promise',
  checkAuthToken: true
}

/**创建axios对象 */
const instance = axios.create({
  headers: { 'Content-Type': 'application/json' },
  withCredentials: true
})

instance.interceptors.request.use(successRequest, errorRequest)
instance.interceptors.response.use(successResponse, errorResponse)

const getBaseUrl = () => {
  const baseUrl = useGlobalStore().appConfig.baseURL
  // const baseUrl = import.meta.env.VITE_APP_BASE_API

  return baseUrl
}

/**兼容服务端长数字类型,导致精度丢失的问题 */
const baseTransformResponse: AxiosResponseTransformer = (data) => {
  try {
    return JSONbig.parse(data)
  } catch (error) {
    return data
  }
}

/**处理请求配置 */
const initRequestOption = (options: AxiosRequestConfig): AxiosRequestConfig => {
  if (!options.responseType || options.responseType === 'json') {
    if (isArray(options.transformResponse)) {
      options.transformResponse.push(baseTransformResponse)
    } else if (isFunction(options.transformResponse)) {
      const param = options.transformResponse
      options.transformResponse = function (data, headers, status) {
        baseTransformResponse.call(this, data, headers, status)
        param.call(this, data, headers, status)
      }
    } else {
      options.transformResponse = [baseTransformResponse]
    }
  }

  return {
    ...options,
    loading: getValue('loading', options),
    autoCloseLoading: getValue('autoCloseLoading', options),
    retonly: getValue('retonly', options),
    mute: getValue('mute', options),
    showNoData: getValue('showNoData', options),
    responseStructure: getValue('responseStructure', options),
    checkAuthToken: getValue('checkAuthToken', options)
  }
}

/**获取配置 */
const getValue = (key: keyof AxiosRequestConfig, options: AxiosRequestConfig) => {
  const target = options[key]
  return isUndefined(target) ? axiosConfig[key] : target
}

/**处理请求入参 */
const handleRequestDataOrParams = (options: AxiosRequestConfig, type: 'data' | 'params') => {
  // Extract the relevant data or params based on the type
  let relevantDataOrParams = options[type] || {}

  // Merge with base entry if injectBaseParams is true
  if (options.injectBaseParams) {
    relevantDataOrParams = { ...baseEntry(), ...relevantDataOrParams }
  }

  return relevantDataOrParams
}

/**统一入参 */
const baseEntry = (): BaseEntry => {
  // const userStore = useUserStore()
  return {}
}

const requestFunc = (options: AxiosRequestConfig) =>
  instance.request({
    baseURL: options.baseURL || getBaseUrl(),
    url: options.url,
    timeout: options.timeout || 30000,
    method: options.method,
    responseType: options.responseType || 'json',
    triggerRoute: router.currentRoute.value.path,
    loadingOptions: options.loadingOptions,
    ...initRequestOption(options),
    data: handleRequestDataOrParams(options, 'data'),
    params: handleRequestDataOrParams(options, 'params')
  })

/**请求入口 */
export const request = async (options: AxiosRequestConfig): Promise<any> => {
  options = { ...axiosConfig, ...options }

  if (options.responseStructure === 'array') {
    let data: AxiosResponse<any, any> | null = null,
      errorMsg: Error | string | null = null
    try {
      data = await requestFunc(options)
    } catch (error: any) {
      errorMsg = error
    }
    return [data, errorMsg]
  } else {
    return requestFunc(options)
  }
}
