import { useUserStore } from '@/stores/user.store'
import { ROUTER_PATH } from '../router-path'
import type { AppBeforeEach } from './guards'
import { appMessage } from '@/hooks/useNaiveApi'
import router from '..'

/**跳过检验白名单 */
const whileList = [ROUTER_PATH.LOGIN]

export const checkLoginStatus: AppBeforeEach = async (to, from, next) => {
  const userStore = useUserStore()
  const { userInfo } = userStore

  if (userInfo.authToken) {
    if (to.path === ROUTER_PATH.LOGIN) {
      next({ ...from, replace: true })
    } else if (userStore.userRouter) {
      next()
    } else {
      try {
        await userStore.getAsyncRouter()

        const target = router.getRoutes().find((route) => route.path === to.path)

        if (target) {
          next({ ...to, replace: true })
        } else {
          next(ROUTER_PATH.HOME)
        }
      } catch (error: any) {
        console.debug(`router guards check-login-status debug:${error}`)

        appMessage.error(error.resultMsg || '抱歉！获取用户权限失败。请先登录。')
        userStore.initUserInfo()
        next(ROUTER_PATH.LOGIN)
      }
    }
  } else {
    if (whileList.indexOf(to.path) !== -1) {
      next()
    } else {
      appMessage.error('抱歉！请先登录。')
      next(ROUTER_PATH.LOGIN)
    }
  }
}
