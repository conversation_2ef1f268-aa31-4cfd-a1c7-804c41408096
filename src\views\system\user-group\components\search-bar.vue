<script lang="ts" setup>
import type { GetManagerGroupForPageInput } from '@/api/dto/system/user-group.dto'
import { useFormHook } from '@/hooks/useForm'
import { ROUTER_PATH } from '@/router/router-path'

export type SearchData = Omit<GetManagerGroupForPageInput, 'page' | 'rows'>

const { formRef, resetForm } = useFormHook()

const enabledMarkOption = [
  { value: '', label: '全部' },
  { value: true, label: '已启用' },
  { value: false, label: '未启用' },
]

const searchData = reactive<SearchData>({
  managerGroupName: '',
  enabledMark: '',
})

const emit = defineEmits<{
  search: [data: SearchData]
}>()

const handleSearch = () => {
  emit('search', searchData)
}

const handleReset = () => {
  resetForm()
  emit('search', searchData)
}

const router = useRouter()
/**新增用户组 */
const handleNewUserGroup = () => {
  router.push(ROUTER_PATH.USER_GROUP_DETAILS)
}
</script>

<template>
  <el-form ref="formRef" :model="searchData" inline class="search_bar-container">
    <el-form-item prop="managerGroupName" label="用户组名称：" class="form-item">
      <el-input v-model="searchData.managerGroupName" placeholder="请输入"></el-input>
    </el-form-item>

    <el-form-item prop="enabledMark" label="是否启用：" class="form-item">
      <el-select v-model="searchData.enabledMark" placeholder="请输入">
        <el-option
          v-for="(item, index) in enabledMarkOption"
          :key="index"
          :label="item.label"
          :value="item.value"
        ></el-option>
      </el-select>
    </el-form-item>

    <el-form-item prop="searchKey" class="form-item">
      <el-button type="primary" @click="handleSearch">搜索</el-button>
      <el-button @click="handleReset">重置</el-button>
      <el-button type="success" @click="handleNewUserGroup">新增用户组</el-button>
    </el-form-item>
  </el-form>
</template>
