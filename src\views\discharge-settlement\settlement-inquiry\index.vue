<script setup lang="ts">
import type { PatientAccountsBaseInfo, PatientInfo } from '@/api/dto/discharge-settlement/patient-accounts.dto'
import type { SettlementItem } from '@/api/dto/discharge-settlement/settlement-table.dto'
import { useMountDialog } from '@/hooks/useMountDialog.ts'
import { ref } from 'vue'
import InpatientInputDialog from '../components/inpatient-input-dialog.vue'
import PatientAccountsBaseInfoPanel from '../components/patient-accounts-base-info-panel.vue'
import PatientDataDialog from '../components/patient-data-dialog.vue'
import SettlementTable from './components/settlement-table.vue'
const { open: openInpatientInputDialog } = useMountDialog(InpatientInputDialog)
const { open: openPatientDataDialog } = useMountDialog(PatientDataDialog)

/**
 * 出院结算查询
 */

const patientAccountsBaseInfo = ref<PatientAccountsBaseInfo>({
  hospitalizationNo: 'ZY202312001',
  billingAccountNo: 'JZ202312001',
  patientName: '张三',
  gender: '男',
  age: '45',
  selfPayRatio: '30%',
  settlementType: '医保',
  coordinatedFundAmount: 5000,
  medicalInsuranceInfo: '城镇职工医保',
  wardArea: '内科病区',
  frequencyCount: 1,
  bedNo: 'A-301',
  priceCategory: '普通',
  totalDays: 7,
  unaccountedDays: 0,
  diagnosis: '高血压',
  admissionDate: '2023-12-01',
  dischargeDate: '2023-12-08',
  requestedDate: '2023-12-07',
  settlementDate: '2023-12-08',
  specialSelfPayDate: '',
  totalCost: 8500,
  pendingSettlementAmount: 2500,
  directSettlementPayment: 6000,
  clearedAmount: 6000,
  pendingClearanceAmount: 2500,
  selfPayPercentage: 30,
  prepayment: 10000,
  amountOwed: 256.36,
  accountClearingDate: '2023-12-08',
  promptMessage: '超支不限制  出院社保差病例'
})

const patientInfo = ref<PatientInfo>({
  hospitalizationNo: 'ZY202312001',
  billingAccountNo: 'JZ202312001',
  wardArea: '内科病区',
  attendingDoctor: '张三',
  patientName: '张三',
  gender: '男',
  birthDate: '1990-01-01',
  age: '45',
  maritalStatus: '已婚',
  region: '北京',
  ethnicity: '汉族',
  nationality: '中国',
  occupation: '医生',
  birthPlace: '北京',
  phone: '***********',
  householdAddress: '北京市海淀区',
  contactPersonName: '张三',
  contactRelationship: '父子',
  contactAddress: '北京市海淀区',
  contactPhone: '***********',
  contactPostalCode: '100000',
  admissionDepartment: '内科',
  admissionDate: '2023-12-01',
  admissionCondition: '正常',
  admissionSource: '门诊',
  outpatientDoctor: '张三',
  outpatientDiagnosis: '高血压',
  admissionDiagnosis: '高血压',
  settlementMethod: '医保',
  medicalCertificateNo: '**********',
  selfPayPercentage: '30%',
  medicareRegistrationNo: '**********',
  medicareNo: '**********',
  coordinatedFundPaid: '1000',
  deductibleStandard: '1000',
  medicareHospitalizationCount: '1',
  medicareFixedQuota: '1000',
  overStandardNormal: '1000',
  admissionBedNo: 'A-301',
  hospitalizationIdentifier: '1',
  dischargeDate: '2023-12-08',
  dischargeDiagnosis: '高血压',
  hospitalName: '北京医院',
  registrarId: '**********',
  registrarName: '张三',
  registrationDateTime: '2023-12-01',
  specialNotes: '超支不限制  出院社保差病例',
  totalPrepaidRMB: '10000',
  remainingPrepaidAmount: '1000',
  amountOwed: '256.36',
  contactPersonPhone: '***********',
  contactPersonPostalCode: '100000',
  workUnit: '北京医院',
  unitAddress: '北京市海淀区',
  postalCode: '100000',
  idCardNo: '**********',
  bedStandard: '1000'
})

/* 表格明细 */
const settlementData = ref<SettlementItem[]>([
  {
    period: '1',
    settlementDate: '2024-01-01',
    totalCost: 100,
    totalSelfPayAmount: 100,
    currentPeriodCost: 100,
    currentSelfPayAmount: 100,
    selfPayClearingDate: '2024-01-01',
    totalCoordinatedFundAmount: 100,
    currentCoordinatedFundAmount: 100,
    coordinatedFundClearingDate: '2024-01-01'
  },
  {
    period: '2',
    settlementDate: '2024-01-02',
    totalCost: 200,
    totalSelfPayAmount: 200,
    currentPeriodCost: 200,
    currentSelfPayAmount: 200,
    selfPayClearingDate: '2024-01-02',
    totalCoordinatedFundAmount: 200,
    currentCoordinatedFundAmount: 200,
    coordinatedFundClearingDate: '2024-01-02'
  },
  {
    period: '3',
    settlementDate: '2024-01-03',
    totalCost: 300,
    totalSelfPayAmount: 300,
    currentPeriodCost: 300,
    currentSelfPayAmount: 300,
    selfPayClearingDate: '2024-01-03',
    totalCoordinatedFundAmount: 300,
    currentCoordinatedFundAmount: 300,
    coordinatedFundClearingDate: '2024-01-03'
  }
])

onMounted(() => {
  handleInputHospitalizationNo()
})

// 输入住院号
function handleInputHospitalizationNo() {
  openInpatientInputDialog({
    confirmCallback: async (data: any) => {
      console.log('输入住院号信息：', data)
    }
  })
}

// 查看病人资料
function handleViewPatientInfo() {
  console.log('查看病人资料')
  openPatientDataDialog({
    patientInfo: patientInfo.value
  })
}

// 切换病人
function handleSwitchPatient() {
  console.log('切换病人')
  handleInputHospitalizationNo()
}
</script>

<template>
  <div class="page-container">
    <PatientAccountsBaseInfoPanel
      :base-info="patientAccountsBaseInfo"
      @view-patient-info="handleViewPatientInfo"
      @switch-patient="handleSwitchPatient"
    />

    <el-divider border-style="dashed" />

    <SettlementTable :settlement-data="settlementData" />
  </div>
</template>

<style scoped lang="scss">
.page-container {
  padding: 20px;
}
</style>
