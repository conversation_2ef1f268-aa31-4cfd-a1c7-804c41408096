/**
 * 住院预交金管理模块
 */

/**
 * 搜索数据类型
 */
export interface ReqHospitalizationDepositOrderByPage {
  /* 平台单号 */
  platformOrderNo?: string
  /* 医院单号 */
  hisOrderNo?: string
  /** 住院人姓名 */
  patientName?: string
  /** 住院号 */
  admissionNo?: string
  /** 状态 */
  orderStatus?: DepositStatus
  /* 当前页 */
  page: number
  /* 每页的记录数 */
  rows: number
}

// 分页获取列表响应
export interface ResHospitalizationDepositOrderByPage {
  data: HospitalizationDepositDataItem[] // 实体列表
  pageIndex: number // 当前页
  pageSize: number // 每页记录数
  recordCount: number // 总记录数
  pageCount: number // 总页数
}

/**
 * 押金订单表格 item 数据
 */
export interface HospitalizationDepositDataItem {
  /* 住院押金订单号 */
  hospitalizationDepositOrderId: string
  /* 平台单号 */
  platformOrderNo: string
  /* 医院单号 */
  hisOrderNo: string
  /** 住院人姓名 */
  patientName: string
  /** 住院号 */
  admissionNo: string
  /** 住院科室 */
  departmentName: string
  /* 支付金额（元） */
  payFee: number
  /** 状态 */
  orderStatus: string
  /* 支付时间 */
  payTime: string
}

export interface ReqHospitalizationDepositOrderForDetail {
  /* 住院押金订单号 */
  hospitalizationDepositOrderId: string
}

/**
 * 押金信息详情
 */
export interface ResHospitalizationDepositOrderForDetail extends HospitalizationDepositDataItem {
  /** 主治医生 */
  doctorName: string
  /** 住院床号 */
  bedNo: string
  /** 入院日期 */
  inDate: string
  /** 押金余额（元） */
  balance: number
  /** 支付流水号 */
  payTradeNo: string
  /** 下单时间 */
  createTime: string
  /* 业务类型 */
  orderReason: string
}

/**
 * 押金订单状态
 */
export enum DepositStatus {
  /* 缴纳成功 */
  SUCCESS = 0,
  /* 缴纳失败 */
  FAILURE = 1,
  /* 缴纳异常 */
  EXCEPTION = 2,
  /* 处理中 */
  PROCESSING = 3,
  /* 取消 */
  CANCEL = 4
}

// 状态映射配置
export const DepositStatusConfig = {
  [DepositStatus.SUCCESS]: { label: '缴纳成功', tagType: 'success' },
  [DepositStatus.FAILURE]: { label: '缴纳失败', tagType: 'danger' },
  [DepositStatus.EXCEPTION]: { label: '缴纳异常', tagType: 'warning' },
  [DepositStatus.PROCESSING]: { label: '缴纳处理中', tagType: 'success' },
  [DepositStatus.CANCEL]: { label: '缴纳取消', tagType: 'info' },
  // 默认配置
  default: { label: '-', tagType: 'info' }
}

/**
 * 押金订单状态可选项
 */
export const DepositStatusOptions = Object.entries(DepositStatusConfig)
  .filter(([key]) => key !== 'default')
  .map(([value, config]) => ({
    label: config.label,
    value: Number(value)
  }))
