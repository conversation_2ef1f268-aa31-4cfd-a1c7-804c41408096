import { merge } from 'lodash-es'
import type { RouteRecordNormalized, RouteRecordRaw } from 'vue-router'
import router, { asyncRoutes, constantRoutes } from '@/router'
import { useUserStore } from '@/stores/user.store'
import type { GetNavigationMenuTreeListOutput, NavigationMenuItemExtraData } from '@/api/dto/navigation.dto'

import routerMap from '@/router/router-map'
import { useLayoutStore } from '@/stores/layout.store'
import type { SvgName } from '~virtual/svg-component'
import { string2boolean } from '.'
import { usePermissionStore, type PermissionItem } from '@/stores/permission.store'

const modules = import.meta.glob('../views/**/*.ts')

export const loadView = (view: string) => {
  if (view.includes('/')) {
    view = view.replace(/[A-Z]/g, (res) => `-${res.toLowerCase()}`)
    if (modules[`../views${view}/index.ts`]) {
      return modules[`../views${view}/index.ts`]
    }
    return modules[`../views${view}${view}.vue`]
  } else {
    return routerMap[view]
  }
}

/**加载组件 */
const onloadComponent = (target: string) => {
  if (target == '#') {
    return () => import('@/layout')
  } else {
    return loadView(target)
  }
}

const setupPermission = (id: string, permisson: NavigationMenuItemExtraData[]) => {
  const target: PermissionItem = {}
  permisson.forEach((item) => {
    target[item.encode] = item
  })

  usePermissionStore().setPermission(id, target)
}

/**初始化路由表——java */
function parseRoutes_Java(ajaxRouter: GetNavigationMenuTreeListOutput) {
  ajaxRouter = ajaxRouter.sort((a, b) => {
    return a.classLayer - b.classLayer
  })

  const tempObj: { [k: number]: RouteRecordRaw } = {}
  const routers: RouteRecordRaw[] = []

  for (const item of ajaxRouter) {
    const uniqueRouteName = getUniqueRouteName(item.linkUrl)

    const router: RouteRecordRaw = {
      path: string2boolean(item.isLeaf) ? item.linkUrl : `/${item.linkUrl}`,
      name: uniqueRouteName,
      component: string2boolean(item.isLeaf) ? onloadComponent(item.linkUrl) : onloadComponent('#'),
      children: [],
      meta: {
        title: item.navigationName,
        mainId: item.mainId,
        navigationId: item.navigationId,
        icon: item.iconUrl ? (`${item.iconUrl}` as SvgName) : ('' as SvgName),
        noCache: false,
        useTab: true,
        extraData: item.authList,
        noVisit: !item.authList?.length,
        sortCode: item.sortCode,
        parentId: item.parentId,
        isParent: !string2boolean(item.isLeaf)
      }
    }

    if (!string2boolean(item.isLeaf)) {
      tempObj[item.mainId] = router
    }

    if (item.parentId) {
      if (tempObj[item.parentId]) {
        tempObj[item.parentId].children!.push(router)
      }
    } else {
      routers.push(router)
    }
    if (item.authList && item.authList.length) {
      setupPermission(item.navigationId, item.authList)
    }
  }

  routers.forEach((route) => {
    route.path = Boolean(route.children!.length) ? route.path : `/${route.path}`
    route.meta!.useBreadcrumb = Boolean(route.children!.length)
  })

  return routers
}

/**加载路由 */
export function loadRoutes() {
  const userStore = useUserStore()
  const { userRouter } = userStore

  const baseAsyncRouter = parseRoutes_Java(userRouter || [])

  const finalRoutes = deepMergeRoutes(constantRoutes, [...baseAsyncRouter, ...asyncRoutes])

  finalRoutes.forEach((item) => router.addRoute(item))

  const routes = router.getRoutes()

  handleFinalRoutes(finalRoutes, routes)

  useLayoutStore().$patch((state) => {
    state.menuList = finalRoutes
  })
}

/**处理最终路由*/
function handleFinalRoutes(finalRoutes: RouteRecordRaw[], routes: RouteRecordNormalized[]) {
  finalRoutes = finalRoutes.sort((a, b) => (a.meta?.sortCode || 0) - (b.meta?.sortCode || 0))

  finalRoutes.forEach((item) => {
    item.path = getRoutePath(item, routes)
    if (item.children?.length) {
      handleFinalRoutes(item.children, routes)
    }
  })
}

/**获取路由实际路径 */
function getRoutePath(target: RouteRecordRaw, routes: RouteRecordNormalized[]) {
  return routes.find((route) => route.name === target.name)!.path
}

/**获取路由名字,用于keepalive */
export const getRouterName = (target: string | symbol) => {
  if (typeof target === 'symbol') {
    return target.description!
  } else {
    // 路由名称在创建时已经保证是唯一的，此处直接返回即可
    return target
  }
}

/**合并路由 */
function deepMergeRoutes(target: RouteRecordRaw[], source: RouteRecordRaw[]) {
  const tarMap = mapRoutes(target)
  const srcMap = mapRoutes(source)

  // 合并路由,传入空对象防止污染原始路由
  const mergeResult = merge({}, tarMap, srcMap)

  return parseRoutesMap(mergeResult)
}

/**映射路由数组 */
const mapRoutes = (routes: RouteRecordRaw[] | readonly RouteRecordRaw[]) => {
  const routesMap: any = {}
  routes.forEach((item) => {
    routesMap[item.path] = {
      ...item,
      children: item.children ? mapRoutes(item.children) : undefined
    }
  })
  return routesMap
}

/**转换为 routes 数组 */
const parseRoutesMap = (routesMap: any): RouteRecordRaw[] => {
  return Object.values(routesMap).map((item: any) => {
    if (item.children) {
      item.children = parseRoutesMap(item.children)
    } else {
      delete item.children
    }
    return item
  })
}

/**
 * 根据 linkUrl 生成全局唯一的路由名称，替换斜杠为中横线
 * 例如: 'declaration-data-management/list' -> 'declaration-data-management-list'
 * 移除开头的斜杠(如果存在)，然后将剩余斜杠替换为中横线
 * 用于生成路由名称，确保路由名称唯一，用于 keep-alive 组件缓存
 */
export const getUniqueRouteName = (linkUrl: string) => {
  return linkUrl.startsWith('/')
    ? linkUrl.substring(1).replace(/\//g, '-')
    : linkUrl.replace(/\//g, '-')
}

export default {
  loadRoutes,
  getRouterName
}
