<script setup lang="ts">
import type { ResExpenseInfoOfAllOrClassificationDTO } from '@/api/dto/discharge-settlement/patient-accounts.dto'
import { requestPatientExpenseInfoOfAll } from '@/api/discharge-settlement.api'

/** 全部明细抽屉 */

const props = defineProps<{
  /** 记帐号 */
  inpno: string | undefined
}>()

const allDetailedBilling = ref<ResExpenseInfoOfAllOrClassificationDTO[]>([] as ResExpenseInfoOfAllOrClassificationDTO[])

const drawerVisible = defineModel<boolean>({ required: true })

watch(
  drawerVisible,
  (newVal) => {
    if (newVal) {
      allDetailedBilling.value = []
      handleGetAllDetailedBilling()
    }
  },
  { immediate: true }
)

// 获取全部明细
async function handleGetAllDetailedBilling() {
  if (!props.inpno) {
    console.error('住院号不能为空')
    return
  }
  const res = await requestPatientExpenseInfoOfAll({ inpno: props.inpno })
  allDetailedBilling.value = res.resExpenseInfoList
}
</script>

<template>
  <el-drawer class="patient-all-detail-drawer" v-model="drawerVisible" title="全部明细" size="70%">
    <CommonHighlightTable :data="allDetailedBilling" style="height: 100%">
      <el-table-column prop="excutedate" label="执行日期" width="200" />
      <el-table-column prop="advtype" label="医嘱分类" width="160" />
      <el-table-column prop="advno" label="方号" width="80" align="center" />
      <el-table-column prop="itemcode" label="项目代码" width="180" />
      <el-table-column prop="itemname" label="项目名称" min-width="240" />
      <el-table-column prop="quantity" label="数量" width="80" align="center" />
      <el-table-column prop="totalamount" label="金额" width="100" align="right" class-name="amount-column" />
      <el-table-column prop="price" label="价格" width="100" align="right" />
      <el-table-column prop="rate" label="比例" width="80" align="right">
        <template #default="{ row }"> {{ row.rate }}% </template>
      </el-table-column>
      <el-table-column prop="selfamount" label="自付" width="80" align="right" />
    </CommonHighlightTable>
  </el-drawer>
</template>

<style lang="scss">
.patient-all-detail-drawer {
  .el-drawer__header {
    margin-bottom: 0 !important;
    background: #f5f5f5 !important;
    padding: 17px 20px !important;
    font-size: 16px !important;
    color: #303133 !important;
  }

  /* 表格高度自适应 */
  .el-table--fit {
    height: 100% !important;
  }

  .el-table__body .amount-column {
    color: #0007db !important;
  }
}
</style>
