<script lang="ts" setup>
import ElConfig from '@/components/el-config'
import type { DialogBeforeCloseFn } from 'element-plus'
import { useElDialogHook } from '@/hooks/useDialog'
import BaseEchart from '../base-echart'

export type EchartDialogData = {
  title: string
  echartTitle: string
  echartOptions: any
}

const { dialogVisible, __open, __close } = useElDialogHook()

const echartRef = ref<InstanceType<typeof BaseEchart> | null>(null)

const echartDialogData = reactive<EchartDialogData>({
  title: '',
  echartTitle: '',
  echartOptions: {},
})

const handleOpen = async (baseOptions: EchartDialogData) => {
  setTimeout(() => {
    echartDialogData.title = baseOptions.title
    echartDialogData.echartTitle = baseOptions.echartTitle
    echartDialogData.echartOptions = baseOptions.echartOptions
  })
  return await __open()
}

const handleOpened = () => {
  echartRef.value?.getEchart()?.setOption(echartDialogData.echartOptions)
}

const handleBeforeClose: DialogBeforeCloseFn = async (done) => {
  echartRef.value?.getEchart()?.clear()
  try {
    done()
  } catch (error) {}
}

defineExpose({
  __open: handleOpen,
  __close,
})
</script>

<template>
  <ElConfig>
    <el-dialog
      v-model="dialogVisible"
      :before-close="handleBeforeClose"
      width="1460"
      align-center
      class="echart_dialog-container"
      @opened="handleOpened"
    >
      <template #header>
        <div class="title">{{ echartDialogData.title }}</div>
      </template>

      <BaseEchart ref="echartRef" shadow="never" class="echart-nav">
        <div class="echart-title">{{ echartDialogData.echartTitle }}</div>
      </BaseEchart>
    </el-dialog>
  </ElConfig>
</template>

<style lang="scss" scoped>
.echart_dialog-container {
  .title {
    height: 44px;
    padding: 0 16px;
    font-size: 18px;
    line-height: 44px;
    color: #303133;
  }

  .echart-nav {
    height: 584px;
    padding: 40px;
    border: none;
  }
  .echart-title {
    position: absolute;
    top: 0;
    font-size: 18px;
    font-weight: bold;
    color: #303133;
  }
}
</style>

<style lang="scss">
.echart_dialog-container {
  .el-dialog__body {
    padding: 0 !important;
  }
}
</style>
