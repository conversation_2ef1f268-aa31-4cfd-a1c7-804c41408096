import { RunTimeOptions } from "@/runTime";
import { ComponentPublicInstance } from "@vue/runtime-core";
import {
  _Awaitable,
  NavigationGuardNext,
  RouteLocationNormalized,
  RouteLocationNormalizedLoaded,
  NavigationGuardReturn
} from "vue-router";
import type { ROUTER_PATH } from "../router-path";

export interface AppBeforeEachRoute extends RouteLocationNormalized {
  active?: boolean;
}
export interface AppBeforeFromEachRoute extends RouteLocationNormalizedLoaded {
  active?: boolean;
}

interface AppBeforeEach {
  (to: AppBeforeEachRoute, from: AppBeforeFromEachRoute, next: NavigationGuardNext, options: RunTimeOptions):
    | _Awaitable<NavigationGuardReturn>
}

interface AppAfterEach {
  (to: RouteLocationNormalized, from: RouteLocationNormalizedLoaded, options?: RunTimeOptions): any;
}

export interface RouterGuards {
  beforeEach?: AppBeforeEach[];
  afterEach?: AppAfterEach[];
}
