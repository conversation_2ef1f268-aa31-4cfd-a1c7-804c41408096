import type { ApiFunc } from 'axios'
import { authRequest } from '../utils.api'
import type {
  AddSysManagerInput,
  DeleteSysManagerInput,
  GetManagerGroupsForAddOrUpdateOutput,
  GetOrgsForAddOrUpdateOutput,
  GetRolesByManagerIdByPageInput,
  GetRolesByManagerIdByPageOutput,
  GetRolesForAddOrUpdateOutput,
  GetSysManagerByPageInput,
  GetSysManagerByPageOutput,
  GetSysManagerGroupsByManagerIdByPageInput,
  GetSysManagerGroupsByManagerIdByPageOutput,
  GetSysManagerForEditInput,
  GetSysManagerForEditOutput,
  GetSysOrganizationsByManagerIdByPageInput,
  GetSysOrganizationsByManagerIdByPageOutput,
  ResetPasswordInput,
  UpdateSysManagerInput,
} from '../dto/system/admin-manage.dto'

/**获取组织机构数据 */
export const getOrgsForAddOrUpdateApi: ApiFunc<undefined, GetOrgsForAddOrUpdateOutput> = (options) => {
  return authRequest({ url: 'getOrgsForAddOrUpdate', ...options })
}

/**获取账号列表 */
export const getSysManagerByPageApi: ApiFunc<GetSysManagerByPageInput, GetSysManagerByPageOutput> = (data, options) => {
  return authRequest({ url: 'getSysManagerByPage', data, ...options })
}

/**查看账号关联的部门 */
export const getSysOrganizationsByManagerIdByPageApi: ApiFunc<
  GetSysOrganizationsByManagerIdByPageInput,
  GetSysOrganizationsByManagerIdByPageOutput
> = (data, options) => {
  return authRequest({ url: 'getSysOrganizationsByManagerIdByPage', data, ...options })
}

/**查看账号关联的角色 */
export const getRolesByManagerIdByPageApi: ApiFunc<GetRolesByManagerIdByPageInput, GetRolesByManagerIdByPageOutput> = (
  data,
  options
) => {
  return authRequest({ url: 'getRolesByManagerIdByPage', data, ...options })
}

/**查看账号关联的用户组 */
export const getSysManagerGroupsByManagerIdByPageApi: ApiFunc<
  GetSysManagerGroupsByManagerIdByPageInput,
  GetSysManagerGroupsByManagerIdByPageOutput
> = (data, options) => {
  return authRequest({ url: 'getSysManagerGroupsByManagerIdByPage', data, ...options })
}

/**修改账号密码 */
export const resetPasswordApi: ApiFunc<ResetPasswordInput, undefined> = (data, options) => {
  return authRequest({ url: 'resetPassword', data, ...options })
}

/**删除账号 */
export const deleteSysManagerApi: ApiFunc<DeleteSysManagerInput, undefined> = (data, options) => {
  return authRequest({ url: 'deleteSysManager', data, ...options })
}

/**获取用户组数据 */
export const getManagerGroupsForAddOrUpdateApi: ApiFunc<undefined, GetManagerGroupsForAddOrUpdateOutput> = (
  options
) => {
  return authRequest({ url: 'getManagerGroupsForAddOrUpdate', ...options })
}

/**获取角色数据 */
export const getRolesForAddOrUpdateApi: ApiFunc<undefined, GetRolesForAddOrUpdateOutput> = (options) => {
  return authRequest({ url: 'getRolesForAddOrUpdate', ...options })
}

/**添加账号 */
export const addSysManagerApi: ApiFunc<AddSysManagerInput, undefined> = (data, options) => {
  return authRequest({ url: 'addSysManager', data, ...options })
}

/**获取帐号详情 */
export const getSysManagerForEditApi: ApiFunc<GetSysManagerForEditInput, GetSysManagerForEditOutput> = (
  data,
  options
) => {
  return authRequest({ url: 'getSysManagerForEdit', data, ...options })
}

/**修改账号信息 */
export const updateSysManagerApi: ApiFunc<UpdateSysManagerInput, undefined> = (data, options) => {
  return authRequest({ url: 'updateSysManager', data, ...options })
}
