<script setup lang="ts">
import {
  InsuredRegistrationStatus,
  InsuredRegistrationStatusConfig,
  ResInsuranceRegistrationInfo
} from '@/api/dto/insurance-registration.dto.ts'
import CirculationInfo from '@/views/insurance-registration/detail/components/circulation-info.vue'
import { appMessage } from '@/hooks/useNaiveApi'
import { useIdCardPrinter } from '@/views/inpatient-registration/utils/inpatient-printer-helper.ts'

const { printIdCard, idCardPrintError } = useIdCardPrinter()

const props = defineProps<{
  detailInfo: ResInsuranceRegistrationInfo
}>()

const emits = defineEmits<{
  (e: 'confirm'): void
  (e: 'reject'): void
}>()

// 点击打印身份证按钮 loading
const isIdCardPrintingLoading = ref(false)

const idCardImages = computed(() => {
  return [props.detailInfo.idcardFrontImgurl ?? '', props.detailInfo.idcardBackImgurl ?? '']
})

// 登记成功
function handleRegistrationSuccessful() {
  ElMessageBox.confirm('确认登记成功？').then(() => {
    emits('confirm')
  })
}

// 登记失败
function handleRegistrationFailed() {
  emits('reject')
}

// 打印身份证
async function handlePrintIdCard() {
  if (!props.detailInfo.idcardFrontImgurl && !props.detailInfo.idcardBackImgurl) {
    appMessage.error('未上传身份证图片，无法打印')
    return
  }

  isIdCardPrintingLoading.value = true
  await printIdCard({
    idCardImgurlFront: props.detailInfo.idcardFrontImgurl || '',
    idCardImgurlBack: props.detailInfo.idcardBackImgurl || ''
  })
  isIdCardPrintingLoading.value = false

  if (idCardPrintError.value) {
    console.error(idCardPrintError.value)
    appMessage.error(idCardPrintError.value)
    return
  }

  console.log('身份证打印成功')
  appMessage.success('身份证打印成功')
}
</script>

<template>
  <div class="content-wrapper">
    <div class="content">
      <div class="title">
        <span>登记信息</span>
        <div>
          <template v-if="detailInfo.registrationStatus === InsuredRegistrationStatus.NOT_REGISTERED">
            <el-button
              v-auth="'insuranceRegistration_registrationSuccessful'"
              type="success"
              size="large"
              @click="handleRegistrationSuccessful"
            >
              登记成功
            </el-button>
            <el-button
              v-auth="'insuranceRegistration_registrationFailed'"
              type="danger"
              size="large"
              @click="handleRegistrationFailed"
            >
              登记失败
            </el-button>
          </template>
          <el-button
            style="color: white"
            color="#2EC6C9"
            :loading="isIdCardPrintingLoading"
            size="large"
            @click="handlePrintIdCard"
          >
            打印身份证
          </el-button>
        </div>
      </div>
      <el-descriptions border :column="2">
        <el-descriptions-item label="住院人姓名">{{ detailInfo.admissionName }}</el-descriptions-item>
        <el-descriptions-item label="住院号">{{ detailInfo.admissionNo }}</el-descriptions-item>
        <el-descriptions-item label="住院科室">{{ detailInfo.hospitalizationDepartmentName }}</el-descriptions-item>
        <el-descriptions-item label="身份证图片">
          <div class="id-card">
            <div>
              <el-image
                v-for="(item, index) in idCardImages"
                class="id-card-image"
                fit="contain"
                :key="index"
                :src="item"
                :initial-index="index"
                :preview-src-list="idCardImages"
              />
            </div>
            <span class="id-card-tips"> 点击图片可放大查看 </span>
          </div>
        </el-descriptions-item>
      </el-descriptions>
    </div>

    <div class="content">
      <div class="title">
        <span>登记信息</span>
      </div>
      <el-descriptions border :column="2">
        <el-descriptions-item label="参保类别">
          {{ detailInfo.insuredPlaceType }}
        </el-descriptions-item>
        <el-descriptions-item label="医保住院类型">
          {{ detailInfo.insuranceType || '-' }}
        </el-descriptions-item>
        <el-descriptions-item label="医保参保类型">
          {{ detailInfo.insuranceName || '-' }}
        </el-descriptions-item>
        <el-descriptions-item label="意外伤害有无第三方责任">
          {{
            detailInfo.isThirdpartyLiability === '1'
              ? '有第三方责任'
              : detailInfo.isThirdpartyLiability === '0'
                ? '无第三方责任'
                : '-'
          }}
        </el-descriptions-item>

        <el-descriptions-item label="登记情况">
          <el-tag
            :type="
              InsuredRegistrationStatusConfig[detailInfo.registrationStatus]?.tagType ||
              InsuredRegistrationStatusConfig.default.tagType
            "
          >
            {{
              InsuredRegistrationStatusConfig[detailInfo.registrationStatus]?.label ||
              InsuredRegistrationStatusConfig.default.label
            }}
          </el-tag>
        </el-descriptions-item>

        <el-descriptions-item label="提交时间">{{ detailInfo.submissionTime }}</el-descriptions-item>

        <el-descriptions-item
          v-if="detailInfo.registrationStatus != InsuredRegistrationStatus.NOT_REGISTERED"
          label="登记时间"
          >{{ detailInfo.registrationTime }}</el-descriptions-item
        >

        <el-descriptions-item
          v-if="detailInfo.registrationStatus === InsuredRegistrationStatus.FAILED"
          label="失败原因"
        >
          {{ detailInfo.failureReason }}
        </el-descriptions-item>
      </el-descriptions>
    </div>

    <div class="content">
      <div class="title">
        <span>流转信息</span>
      </div>
      <CirculationInfo :circulation-info="detailInfo.resCirculationInfoDTOList ?? []" />
    </div>
  </div>
</template>

<style scoped lang="scss">
.content {
  & .title {
    margin: 30px 0 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 16px;
  }

  &-wrapper {
    padding: 0 20px 30px;
    display: flex;
    flex-direction: column;
    flex: 1;
    overflow: auto;
  }

  .id-card {
    display: flex;
    align-items: center;
    justify-content: space-between;

    &-image {
      width: 57px;
      height: 36px;
      border-radius: 2px;
      vertical-align: middle;
    }

    &-tips {
      color: #c0c4cc;
    }
  }

  .signature-box {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
}
</style>
