import { ROUTER_PATH } from '@/router/router-path.ts'
import type { RouteRecordRaw } from 'vue-router'

const Layout = () => import('@/layout')

export const depositManagementRouter: RouteRecordRaw = {
  path: ROUTER_PATH.DEPOSIT_MANAGEMENT,
  name: 'DepositManagement',
  redirect: ROUTER_PATH.DEPOSIT_MANAGEMENT_LIST,
  component: Layout,
  meta: { title: '住院预交金管理', icon: 'el-icon-wallet' },
  children: [
    // 根级路由从动态路由接口获取
    // {
    //   path: ROUTER_PATH.DEPOSIT_MANAGEMENT_LIST,
    //   name: 'DepositManagementList',
    //   component: () => import('@/views/deposit-management/list'),
    //   meta: { title: '住院预交金信息' }
    // },
    {
      path: ROUTER_PATH.DEPOSIT_MANAGEMENT_DETAIL,
      name: 'DepositManagementDetail',
      component: () => import('@/views/deposit-management/detail'),
      hidden: true,
      meta: {
        title: '住院预交金详情',
        leaveOff: true,
        activeMenu: ROUTER_PATH.DEPOSIT_MANAGEMENT_LIST,
        noCache: true,
        permissionFrom: ROUTER_PATH.DEPOSIT_MANAGEMENT_LIST,
        useTab: false
      }
    }
  ]
}
