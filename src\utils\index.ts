import type { Tree } from '@/types/tree'
import type { RuleItem } from 'async-validator'
import type { UploadRawFile } from 'element-plus'

/**获取当月第一天 */
export const getTimeFirstDay = (dateFormat: string) => {
  const date = new Date(dateFormat)
  const year = date.getFullYear()
  const month = date.getMonth()
  return parseTime(`{Y}-{M}-{D}`, new Date(year, month, 1))
}

/**获取下个月第一天 */
export const getTimeLastDay = (dateFormat: string) => {
  const date = new Date(dateFormat)
  const year = date.getFullYear()
  const month = date.getMonth()
  return parseTime(`{Y}-{M}-{D}`, new Date(year, month + 1, 1))
}

/**字符串('true', 'false'')转为Boolean */
export const string2boolean = (str: string) => {
  return str === 'false' ? false : Boolean(str)
}

/**监听动画结束 */
export function listerTransition(dom: HTMLElement, type: 'start' | 'run' | 'end' | 'cancel', callback: Function) {
  dom.addEventListener(
    `transition${type}`,
    (event) => {
      callback(event)
    },
    false
  )
}

/**等待延迟结束执行 */
export function delay(ms: number) {
  return new Promise<void>((resolve) => setTimeout(resolve, ms))
}

/**判断文字是否超出容器 */
export function textRange(el: Element) {
  const textContent = el
  const targetW = textContent.getBoundingClientRect().width
  const range = document.createRange()
  range.setStart(textContent, 0)
  range.setEnd(textContent, textContent.childNodes.length)
  const rangeWidth = range.getBoundingClientRect().width
  return rangeWidth > targetW
}

/**获取对应的时间格式 */
export const parseTime = (
  cFormat = '{Y}-{M}-{D} {h}:{m}:{s}',
  time: string | number | Date = Date.now(),
  options?: { hourlyStart?: boolean; hourlyEnd?: boolean }
) => {
  const format = cFormat
  let date
  if (typeof time === 'object') {
    date = time
  } else {
    if (typeof time === 'string') {
      if (/^[0-9]+$/.test(time)) {
        time = parseInt(time)
      } else {
        time = time.replace(/-/gm, '/')
      }
    }
    if (typeof time === 'number' && time.toString().length === 10) {
      time = time * 1000
    }
    date = new Date(time)
  }

  if (options?.hourlyStart) {
    date.setHours(0, 0, 0, 0)
  }
  if (options?.hourlyEnd) {
    date.setHours(23, 59, 59, 999)
  }

  const formatObj = {
    Y: date.getFullYear(), //年
    M: date.getMonth() + 1, //月
    D: date.getDate(), //日
    h: date.getHours(), //时
    m: date.getMinutes(), //分
    s: date.getSeconds(), //秒
    d: date.getDay() //星期
  }
  const time_str = format.replace(/{([YMDhmsd])+}/g, (_, key: keyof typeof formatObj) => {
    const value = formatObj[key]
    if (key === 'd') {
      return ['日', '一', '二', '三', '四', '五', '六'][value]
    }
    return value.toString().padStart(2, '0')
  })
  return time_str
}

/**检验路径是否为网络地址 */
export function isExternal(path: string) {
  return /^(https?:|mailto:|tel:)/.test(path)
}

export function isException(path: string) {
  return /(401|403|404|500)/.test(path)
}

/**处理tree数据 */
export const formatTreeData = <T = any>(arr: any[], labelKey: keyof T, nodeKey: keyof T): Tree<T>[] => {
  arr.sort((a, b) => {
    return a.classLayer - b.classLayer
  })

  const tempObj: Record<string, any> = {}
  const res: Tree<T>[] = []

  arr.forEach((item) => {
    if (!string2boolean(item.isLeaf)) {
      item.children = []
      tempObj[item.mainId] = item
    }

    if (item.parentId) {
      if (tempObj[item.parentId]) {
        const target = { extData: item, label: item[labelKey], nodeKey: item[nodeKey], children: item.children }
        if (!target.children) delete target.children
        tempObj[item.parentId].children.push(target)
      }
    } else {
      res.push({ nodeKey: item[nodeKey], extData: item, label: item[labelKey], children: item.children })
    }
  })

  return res
}
export const toKebabCase = (str: string) => {
  return str.replace(/([a-z0-9]|(?=[A-Z]))([A-Z])/g, '$1-$2').toLowerCase()
}

/**文件转base64 */
export const fileToBase64 = (file: UploadRawFile) => {
  return new Promise<string>((resolve, reject) => {
    const reader = new FileReader()
    let imgResult = ''
    reader.readAsDataURL(file)
    reader.onerror = (error) => {
      reject(error)
    }
    reader.onloadend = () => {
      imgResult = reader.result as string
      imgResult = imgResult.replace(/^data:image\/\w+;base64,/, '')
      resolve(imgResult)
    }
  })
}

/**导出excel */
export const exportExcel = (data: Blob, projectName: string, ext = 'xlsx') => {
  return new Promise<void>((res, rej) => {
    try {
      const link = document.createElement('a')
      const blob = new Blob([data], {
        type: 'application/xlsx' //MIME类型
      })
      const reader = new FileReader()
      reader.readAsText(blob, 'utf-8')
      reader.onload = () => {
        link.href = URL.createObjectURL(blob) // 创建URL
        link.setAttribute('download', `${projectName}（${parseTime('{Y}-{M}-{D} {h}.{m}.{s}')}）.${ext}`) // 设置下载文件名称
        link.click() // 下载文件
        document.body.appendChild(link)
        URL.revokeObjectURL(link.href) // 释放内存
        document.body.removeChild(link)
        res()
      }
    } catch (error) {
      rej(error)
    }
  })
}

/**等保三级密码校验 */
export const validatorPassword: RuleItem['validator'] = (_, value: string, callback) => {
  const reg = /^(?=.*[a-z])(?=.*[A-Z])(?=.*[0-9])(?=.*[^a-zA-Z0-9\u4e00-\u9fa5_]).{8,}$/
  if (!reg.test(value)) {
    callback(
      new Error('密码必须是包含大写字母、小写字母、数字、特殊符号（不是字母，数字，下划线，汉字的字符）的8位以上组合')
    )
  } else {
    callback()
  }
}

export const validatorIdCard: RuleItem['validator'] = (_, value: string, callback) => {
  // 地区
  const aCity = {
    11: '北京',
    12: '天津',
    13: '河北',
    14: '山西',
    15: '内蒙古',
    21: '辽宁',
    22: '吉林',
    23: '黑龙江',
    31: '上海',
    32: '江苏',
    33: '浙江',
    34: '安徽',
    35: '福建',
    36: '江西',
    37: '山东',
    41: '河南',
    42: '湖北',
    43: '湖南',
    44: '广东',
    45: '广西',
    46: '海南',
    50: '重庆',
    51: '四川',
    52: '贵州',
    53: '云南',
    54: '西藏',
    61: '陕西',
    62: '甘肃',
    63: '青海',
    64: '宁夏',
    65: '新疆',
    71: '台湾',
    81: '香港',
    82: '澳门',
    91: '国外'
  }
  // 验证长度
  if (!/^\d{17}(\d|x)$/i.test(value)) {
    callback(new Error('身份证号长度或格式错误'))
    return
  }
  // 验证前两位是否为省份代码
  value = value.replace(/x$/i, 'a')

  if (aCity[parseInt(value.substring(0, 2)) as keyof typeof aCity] == null) {
    callback(new Error('身份证号长度或格式错误'))
    return
  }
  // 身份证上的出生年月校验
  const sBirthday =
    value.substring(6, 10) + '-' + Number(value.substring(10, 12)) + '-' + Number(value.substring(12, 14))
  const d = new Date(sBirthday.replace(/-/g, '/'))
  if (sBirthday != d.getFullYear() + '-' + (d.getMonth() + 1) + '-' + d.getDate()) {
    callback(new Error('身份证号不合法'))
    return
  }
  // 身份证校验位判断
  let iSum = 0
  for (let i = 17; i >= 0; i--) {
    iSum += (Math.pow(2, i) % 11) * parseInt(value.charAt(17 - i), 11)
  }
  if (iSum % 11 != 1) {
    callback(new Error('身份证号不合法'))
    return
  }
  callback()
}

/**
 * 手机号加密
 * @param phoneNumber
 * @param maskChar
 * @param start
 * @param length
 */
export function encodePhoneNumber(
  phoneNumber: string,
  maskChar: string = '*',
  start: number = 3,
  length: number = 4
): string {
  if (!phoneNumber || phoneNumber.length < start + length) {
    return phoneNumber
  }

  const prefix = phoneNumber.slice(0, start)
  const maskedSection = maskChar.repeat(length)
  const suffix = phoneNumber.slice(start + length)

  return prefix + maskedSection + suffix
}

/**
 * 格式化价格
 * @param price 价格数值
 * @param decimals 小数位数，默认为2
 * @param separator 千位分隔符，默认为逗号(,)
 * @returns 格式化后的价格字符串，例如：26,402.90
 */
export function formatPrice(price: number | undefined | null, decimals: number = 2, separator: string = ',') {
  if (price === undefined || price === null || isNaN(Number(price))) {
    return ''
  }

  // 确保 price 是数字类型
  const numPrice = Number(price)

  // 格式化为固定小数位数的字符串
  const fixedPrice = numPrice.toFixed(decimals)

  // 分离整数部分和小数部分
  const [integerPart, decimalPart] = fixedPrice.split('.')

  // 对整数部分添加千位分隔符
  const formattedIntegerPart = integerPart.replace(/\B(?=(\d{3})+(?!\d))/g, separator)

  // 重新组合整数部分和小数部分
  return decimalPart ? `${formattedIntegerPart}.${decimalPart}` : formattedIntegerPart
}


/* 身份证号码脱敏 */
export function maskIdCard(idCard: string) {
  if (!idCard) return ''
  if (idCard.length >= 9) {
    return idCard.replace(/(?<=^\d{4})\d+(?=\d{4}$)/, '*'.repeat(idCard.length - 8))
  }
  return idCard
}
