<script setup lang="ts">
import {
  ChangeEscortInfo,
  OrderModifyInfo,
  RentalOrderStatus,
  ResRentalOrderDetail,
  ConfirmLeaseOfCaregiverPerson
} from '@/api/dto/rental-management/rental-order-management.dto.ts'
import { useMountDialog } from '@/hooks/useMountDialog.ts'
import RentalConfirmDialog from '@/views/rental-management/rental-order-management/components/rental-confirm-dialog.vue'
import RentalModifyDialog from '@/views/rental-management/rental-order-management/components/rental-modify-dialog.vue'
import RentalReplacementDialog from '@/views/rental-management/rental-order-management/components/rental-replacement-dialog.vue'
import RentalChangeEscortInfo from '@/views/rental-management/rental-order-management/order-detail/components/rental-change-escort-info.vue'
import RentalCommentInfo from '@/views/rental-management/rental-order-management/order-detail/components/rental-comment-info.vue'
import RentalEscortInfo from '@/views/rental-management/rental-order-management/order-detail/components/rental-escort-info.vue'
import RentalInfo from '@/views/rental-management/rental-order-management/order-detail/components/rental-info.vue'
import RentalInpatientInfo from '@/views/rental-management/rental-order-management/order-detail/components/rental-inpatient-info.vue'
import RentalOrderModifyInfo from '@/views/rental-management/rental-order-management/order-detail/components/rental-order-modify-info.vue'
import RentalPaymentInfoDialog from '@/views/rental-management/rental-order-management/order-detail/components/rental-payment-info-dialog.vue'
import { requestSupplementaryPaymentInformation } from '@/api/rental-management.api.ts'

const { open: openConfirmRentalDialog } = useMountDialog(RentalConfirmDialog)
const { open: openModifyRentalDialog } = useMountDialog(RentalModifyDialog)
const { open: openReplacementRentalDialog } = useMountDialog(RentalReplacementDialog)
const { open: openPaymentInfoDialog } = useMountDialog(RentalPaymentInfoDialog)

enum ItemActionType {
  /* 确定租赁 */
  CONFIRM = 'confirm',
  /* 服务完成 */
  FINISHED = 'finished',
  /* 取消订单 */
  CANCEL = 'cancel',
  /* 补缴支付信息 */
  SHOW_PAYMENT_INFO = 'showPaymentInfo'
}

const props = defineProps<{
  detailInfo: ResRentalOrderDetail
}>()

const emits = defineEmits<{
  (
    e: 'confirm',
    data: {
      /* ID */
      inpatientCaregiverId: string
      /* 陪护人ID */
      inpatientCaregiverPersonId: string
    },
    resolve: () => void,
    reject: () => void
  ): void
  (
    e: 'replacement',
    data: {
      inpatientCaregiverId: string
      inpatientCaregiverPersonId: string
      reviewOperate: boolean
    },
    resolve: () => void,
    reject: () => void
  ): void
  (e: 'finished', data: { inpatientCaregiverId: string }): void
  (
    e: 'modify',
    data: {
      inpatientCaregiverUpdateId: string
      inpatientCaregiverId: string
      reviewOperate: boolean
    },
    resolve: () => void,
    reject: () => void
  ): void
  (e: 'cancel', data: { inpatientCaregiverId: string }): void
}>()

async function handleItemAction(itemAction: ItemActionType) {
  switch (itemAction) {
    /* 确定租赁 */
    case ItemActionType.CONFIRM:
      openConfirmRentalDialog({
        rentalData: props.detailInfo,
        confirmCallback: (selectedPerson: ConfirmLeaseOfCaregiverPerson) => {
          return new Promise<void>((resolve, reject) => {
            // 父组件可能是异步操作，把 resolve 和 reject 传给子组件，子组件可以调用
            emits(
              'confirm',
              {
                inpatientCaregiverId: props.detailInfo.inpatientCaregiverId,
                inpatientCaregiverPersonId: selectedPerson.inpatientCaregiverPersonId
              },
              resolve,
              reject
            )
          })
        }
      })
      break
    /* 服务完成 */
    case ItemActionType.FINISHED:
      emits('finished', {
        inpatientCaregiverId: props.detailInfo.inpatientCaregiverId
      })
      break
    /* 取消订单 */
    case ItemActionType.CANCEL:
      emits('cancel', {
        inpatientCaregiverId: props.detailInfo.inpatientCaregiverId
      })
      break
    /* 补缴支付信息 */
    case ItemActionType.SHOW_PAYMENT_INFO:
      const paymentInfo = await getPaymentInfo(props.detailInfo.inpatientCaregiverId)
      if (paymentInfo) {
        openPaymentInfoDialog({
          paymentInfo
        })
      }
      break
    default:
      break
  }
}

/* 获取补缴支付信息 */
async function getPaymentInfo(inpatientCaregiverId: string) {
  try {
    const res = await requestSupplementaryPaymentInformation({ inpatientCaregiverId })
    return res
  } catch (error) {
    console.error('获取补缴支付信息失败：', error)
    ElMessage.error('获取补缴支付信息失败')
    return null
  }
}

/* 修改订单 */
function handleModifyOrder(data: { orderItem: OrderModifyInfo }) {
  console.log('修改审核：', data.orderItem)
  openModifyRentalDialog({
    inpatientCaregiverId: data.orderItem.inpatientCaregiverId,
    confirmCallback: (inpatientCaregiverUpdateId: string, inpatientCaregiverId: string, reviewOperate: boolean) => {
      return new Promise<void>((resolve, reject) => {
        // 父组件可能是异步操作，把 resolve 和 reject 传给子组件，子组件可以调用
        emits('modify', { inpatientCaregiverUpdateId, inpatientCaregiverId, reviewOperate }, resolve, reject)
      })
    }
  })
}

/* 换人申请 */
function handleChangeEscort(data: { orderItem: ChangeEscortInfo }) {
  console.log('换人审核：', data.orderItem)
  openReplacementRentalDialog({
    inpatientCaregiverId: props.detailInfo.inpatientCaregiverId,
    confirmCallback: (inpatientCaregiverId: string, inpatientCaregiverPersonId: string, reviewOperate: boolean) => {
      return new Promise<void>((resolve, reject) => {
        // 父组件可能是异步操作，把 resolve 和 reject 传给子组件，子组件可以调用
        emits(
          'replacement',
          {
            inpatientCaregiverId,
            inpatientCaregiverPersonId,
            reviewOperate
          },
          resolve,
          reject
        )
      })
    }
  })
}
</script>

<template>
  <div class="content-wrapper">
    <!-- 待补缴提示 -->
    <div
      v-if="detailInfo.orderStatus === RentalOrderStatus.PENDING_PAYMENT && detailInfo.pendingPayment"
      class="header-tips"
    >
      订单待补缴：因修改订单信息审核通过，需等待用户补缴金额：{{ detailInfo.pendingPayment }}元
    </div>
    <!-- 待退费提示 -->
    <div v-if="detailInfo.orderStatus === RentalOrderStatus.RESERVED && detailInfo.pendingRefund" class="header-tips">
      核算退费：因修改订单信息审核通过，等待核算退费金额：{{ detailInfo.pendingRefund }}元
    </div>

    <!-- 住院信息 -->
    <RentalInpatientInfo
      :detail-info="detailInfo"
      @confirm="handleItemAction(ItemActionType.CONFIRM)"
      @finished="handleItemAction(ItemActionType.FINISHED)"
      @cancel="handleItemAction(ItemActionType.CANCEL)"
      @showPaymentInfo="handleItemAction(ItemActionType.SHOW_PAYMENT_INFO)"
    />

    <!-- 陪护人信息，使用相关的一两字段来判断是否显示 -->
    <RentalEscortInfo
      v-if="detailInfo.caregiverPersonName || detailInfo.caregiverPersonIdCard"
      :escort-info="detailInfo"
    />

    <!-- 租赁信息 -->
    <RentalInfo :detail-info="detailInfo" />

    <!-- 评价信息，使用相关的一两字段来判断是否显示 -->
    <RentalCommentInfo v-if="detailInfo.evaluateTime" :comment-info="detailInfo" />

    <!-- 订单修改 -->
    <RentalOrderModifyInfo
      v-if="detailInfo.orderUpdateInfoDTOList"
      :order-modify-info="detailInfo.orderUpdateInfoDTOList"
      @modify="handleModifyOrder"
    />

    <!-- 换人申请 -->
    <RentalChangeEscortInfo
      v-if="detailInfo.substitutionInfoDTOList"
      :change-escort-info="detailInfo.substitutionInfoDTOList"
      @replacement="handleChangeEscort"
    />
  </div>
</template>

<style scoped lang="scss">
.header-tips {
  padding: 10px;
  border-radius: 4px;
  background: #fdf6ec;
  border: 1px solid #fbf0e1;
  font-size: 14px;
  color: #e6a23c;
  margin-top: 20px;
}

.content-wrapper {
  padding: 0 20px 30px;
  display: flex;
  flex-direction: column;
  flex: 1;
  overflow: auto;
}

:deep(.common-panel-title) {
  margin: 30px 0 20px;
}
</style>
