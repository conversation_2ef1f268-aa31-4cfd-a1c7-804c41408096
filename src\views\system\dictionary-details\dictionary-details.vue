<script lang="ts" setup>
import type { GetSysDictionaryDetailForPageOutput, SysDictionaryDetailItemModel } from '@/api/dto/system/dictionary.dto'
import { deleteSysDictionaryDetailApi, getSysDictionaryDetailForPageApi } from '@/api/system/dictionary.api'
import BasePagination from '@/components/base-pagination'
import { useDictionaryDetailsDialogHook } from '@/components/dictionary-details-dialog'
import { useLoadingHook } from '@/hooks/useLoading'
import { appMessage } from '@/hooks/useNaiveApi'
import { usePaginationHook } from '@/hooks/usePagination'
import { PAN_GU_RESPONSE_CODE } from '@/types'

const route = useRoute()

const router = useRouter()

const query = computed(() => route.query as { dictionaryInfoId: string; dictionaryInfoName: string })

const { currentPage, pageSize, total } = usePaginationHook()

const handleBack = () => {
  router.go(-1)
}

const enabledMark = ref<'' | boolean>('')

const { loading, loadingFunc } = useLoadingHook()
const tableData = ref<GetSysDictionaryDetailForPageOutput>([])

const getSysDictionaryDetailForPage = async () => {
  try {
    const { data, recordCount } = await getSysDictionaryDetailForPageApi(
      {
        dictionaryInfoId: query.value.dictionaryInfoId,
        enabledMark: enabledMark.value,
        page: currentPage.value,
        rows: pageSize.value,
      },
      { retonly: false, loading: loadingFunc, showNoData: false }
    )
    tableData.value = data
    total.value = recordCount!
  } catch (error: any) {
    if (error.resultCode === PAN_GU_RESPONSE_CODE.NORECORD) {
      tableData.value = []
      total.value = 1
      return
    }
    handleBack()
  }
}
onMounted(getSysDictionaryDetailForPage)

/**搜索 */
const handleSearch = () => {
  currentPage.value = 1
  getSysDictionaryDetailForPage()
}

const { open, close } = useDictionaryDetailsDialogHook()
onBeforeUnmount(close)
const handleOpenDictionaryDetailsDialog = async (target?: SysDictionaryDetailItemModel) => {
  try {
    await open({ dictionaryInfoId: query.value.dictionaryInfoId, dictionaryDetailId: target?.dictionaryDetailId || '' })
    getSysDictionaryDetailForPage()
  } catch (error) {}
}

const handleAdd = () => handleOpenDictionaryDetailsDialog()

const handleEdit = (target: SysDictionaryDetailItemModel) => {
  handleOpenDictionaryDetailsDialog(target)
}

const { loading: delLoading, loadingFunc: delLoadingFunc } = useLoadingHook()
const handleDelete = async (target: SysDictionaryDetailItemModel) => {
  try {
    await ElMessageBox.confirm(`确定要删除字典【${target.dictionaryDetailName}】吗?`, '提示', { type: 'warning' })
    await deleteSysDictionaryDetailApi(
      { dictionaryDetailId: target.dictionaryDetailId },
      { retonly: false, loading: delLoadingFunc }
    )
    appMessage.success('删除成功')
    getSysDictionaryDetailForPage()
  } catch (error) {}
}
</script>

<template>
  <div class="dictionary_details-container">
    <div class="back-bar flex">
      <el-button type="success" @click="handleAdd">新增值</el-button>
      <el-button type="warning" @click="handleBack">返回</el-button>
    </div>

    <div class="main layout-page-pd">
      <div class="tag">{{ query.dictionaryInfoName }}</div>

      <BaseFormItem label="是否启用" class="mt-10" style="justify-content: start">
        <el-select v-model="enabledMark">
          <el-option label="全部" value=""></el-option>
          <el-option label="启用" :value="true"></el-option>
          <el-option label="停用" :value="false"></el-option>
        </el-select>

        <el-button type="primary" class="ml-8" @click="handleSearch">搜索</el-button>
      </BaseFormItem>

      <BaseTable v-loading="loading" :data="tableData" border height="580" class="mt-10">
        <el-table-column prop="dictionaryDetailName" label="值名称"></el-table-column>
        <el-table-column prop="encode" label="编号标识"></el-table-column>
        <el-table-column prop="sortCode" label="排序"></el-table-column>
        <el-table-column label="是否启用">
          <template #default="{ row }: { row: SysDictionaryDetailItemModel }">
            <el-tag :type="row.enabledMark ? 'success' : 'danger'">{{ row.enabledMark ? '启用' : '停用' }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="description" label="备注"></el-table-column>
        <el-table-column label="操作">
          <template #default="{ row }: { row: SysDictionaryDetailItemModel }">
            <el-button @click="handleEdit(row)">编辑</el-button>
            <el-button :loading="delLoading" type="danger" @click="handleDelete(row)">删除</el-button>
          </template>
        </el-table-column>
      </BaseTable>

      <BasePagination v-model:currentPage="currentPage" v-model:pageSize="pageSize" :total="total" />
    </div>
  </div>
</template>

<style lang="scss" scoped>
.main {
  .tag {
    width: 100%;
    padding: 8px 16px;
    background-color: var(--el-color-primary-light-9);
    font-size: 18px;
    letter-spacing: 0.1em;
    color: var(--el-color-primary);
    border-radius: 4px;
  }
}
</style>
