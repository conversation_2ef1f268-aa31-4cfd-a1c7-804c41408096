<script lang="ts" setup>
import type { GetSysNavigationForUpdateInput, AddSysNavigationInput } from '@/api/dto/system/navigation.dto'
import {
  navigationTypeOptions,
  getSysNavigationForUpdateApi,
  updateSysNavigationApi,
  addSysNavigationApi,
} from '@/api/system/navigation.api'
import ElConfig from '@/components/el-config'
import { useElDialogHook } from '@/hooks/useDialog'
import { useFormHook } from '@/hooks/useForm'
import { useLoadingHook } from '@/hooks/useLoading'
import { appMessage } from '@/hooks/useNaiveApi'
import type { DialogBeforeCloseFn, FormRules } from 'element-plus'
import ChooseIconDailog from './choose-icon-dialog.vue'
import { NAVIGATION_TYPE_ENUM } from '@/api/dto/navigation.dto'

export interface OpenParams extends GetSysNavigationForUpdateInput {
  parentId: number
}
type FormData = AddSysNavigationInput

const { dialogVisible, __open, __close, promiseFunc } = useElDialogHook<boolean>()

const { formRef, validate, resetForm } = useFormHook()

const { loading, loadingFunc } = useLoadingHook()

const displayChooseIncoDialog = ref(false)

const formData = reactive<FormData>({
  description: '',
  enabledMark: true,
  iconUrl: '',
  isSys: false,
  linkUrl: '',
  navigationId: '',
  navigationName: '',
  navigationType: NAVIGATION_TYPE_ENUM.DIR,
  outLinkMark: false,
  sortCode: 1,
  parentId: 0,
  layer: 0,
  title: '',
})
/**表单验证规则 */
const rules = reactive<FormRules<FormData>>({
  navigationType: [{ required: true, message: '请选择菜单类型', trigger: 'blur' }],
  navigationName: [{ required: true, message: '请输入菜单名字', trigger: 'blur' }],
  title: [{ required: true, message: '请输入菜单编码', trigger: 'blur' }],
  linkUrl: [{ required: true, message: '请输入菜单地址', trigger: 'blur' }],
})

const formLinkOption = computed(() => {
  if (formData.outLinkMark) {
    return {
      lable: '外部链接：',
      placeholder: '请输入外部链接',
    }
  } else {
    return {
      label: '页面地址：',
      placeholder: '请输入页面地址',
    }
  }
})

/**重置dialog数据 */
const resetFormData = () => {
  formData.navigationId = ''
  formData.parentId = 0
  formData.layer = 0
  formData.iconUrl = ''
}

/**获取菜单信息 */
const getSysNavigationForUpdate = async () => {
  try {
    const result = await getSysNavigationForUpdateApi({ navigationId: formData.navigationId })

    for (const [k] of Object.entries(formData)) {
      const key = k as keyof FormData
      formData[key] = result[key] as never
    }
  } catch (error) {
    resetForm()
    resetFormData()
    __close()
    promiseFunc.reject?.('获取字典详情失败，请稍后重试')
  }
}

/**选择菜单icon */
const handleIconChoosed = (iconName: string) => {
  formData.iconUrl = iconName
}

/**修改菜单信息 */
const updateSysNavigation = async () => {
  await updateSysNavigationApi(formData, { loading: loadingFunc })
}

/**新增菜单 */
const addSysNavigation = async () => {
  const param = { ...formData }
  if (!param.parentId) {
    delete param.parentId
  }

  await addSysNavigationApi(param, { loading: loadingFunc })
}

/**确认按钮 */
const confrim = async () => {
  try {
    await validate()

    if (formData.navigationId) {
      // 修改
      await updateSysNavigation()
      appMessage.success('修改成功')
    } else {
      // 新增
      await addSysNavigation()
      appMessage.success('新增成功')
    }
    promiseFunc.resolve?.(true)
    __close()
  } catch (error) {}
}

const handleBeforeClose: DialogBeforeCloseFn = async (done) => {
  try {
    await validate()
    if (loading.value) {
      appMessage.warning('正在提交中，请稍后')
      return
    }
    done()
  } catch (error) {}
}

const handleOpenDialog = async (openParams: OpenParams) => {
  setTimeout(() => {
    formData.parentId = openParams.parentId
    if (openParams.navigationId) {
      formData.navigationId = openParams.navigationId
      getSysNavigationForUpdate()
    }
  })

  return await __open()
}

const handleCloseDialog = () => {
  resetForm()
  resetFormData()
  __close()
  promiseFunc.reject?.('取消')
}

const handleClosed = () => {
  resetForm()
  resetFormData()
}

defineExpose({
  __open: handleOpenDialog,
  __close: handleCloseDialog,
})
</script>

<template>
  <ElConfig>
    <el-dialog
      v-model="dialogVisible"
      :before-close="handleBeforeClose"
      width="420"
      class="navigation_dialog-container"
      @closed="handleClosed"
    >
      <template #header>
        <div class="title">{{ formData.navigationId ? '修改菜单' : '新增菜单' }}</div>
      </template>

      <el-form ref="formRef" :model="formData" :rules="rules" label-width="120">
        <el-form-item v-if="formData.layer" label="层级："> {{ `${formData.layer}级` }} </el-form-item>

        <el-form-item prop="navigationType" label="类型：">
          <el-select v-model="formData.navigationType">
            <el-option
              v-for="item in navigationTypeOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item prop="navigationName" label="名称：">
          <el-input v-model="formData.navigationName" placeholder="请输入名称"></el-input>
        </el-form-item>

        <el-form-item prop="title" label="唯一编号：">
          <el-input v-model="formData.title"></el-input>
        </el-form-item>

        <el-form-item :label="formLinkOption.label" prop="linkUrl">
          <el-input v-model="formData.linkUrl" :placeholder="formLinkOption.placeholder"></el-input>
        </el-form-item>

        <el-form-item prop="outLinkMark" label="是否外部链接：">
          <el-radio v-model="formData.outLinkMark" :label="true">是</el-radio>
          <el-radio v-model="formData.outLinkMark" :label="false">否</el-radio>
        </el-form-item>

        <el-form-item label="是否启用：" prop="enabledMark">
          <el-switch v-model="formData.enabledMark" active-text="启用后才能显示"> </el-switch>
        </el-form-item>

        <el-form-item label="系统管理：" prop="isSys">
          <el-switch v-model="formData.isSys" active-text="开启后无法删除"> </el-switch>
        </el-form-item>

        <el-form-item prop="iconUrl" label="图标：">
          <div class="flex">
            <el-input v-model="formData.iconUrl"></el-input>
            <el-button type="info" class="ml-6" style="flex-shrink: 0" @click="displayChooseIncoDialog = true">
              选择图标
            </el-button>
          </div>
        </el-form-item>

        <el-form-item prop="sortCode" label="排序：">
          <el-input-number v-model="formData.sortCode" :min="0"></el-input-number>
        </el-form-item>

        <el-form-item prop="description" label="备注：">
          <el-input v-model="formData.description" type="textarea" placeholder="请输入备注"></el-input>
        </el-form-item>
      </el-form>

      <ChooseIconDailog v-model="displayChooseIncoDialog" @choose="handleIconChoosed" />

      <template #footer>
        <el-button :disabled="loading" @click="handleCloseDialog">取 消</el-button>
        <el-button :loading="loading" type="primary" @click="confrim">确 定</el-button>
      </template>
    </el-dialog>
  </ElConfig>
</template>

<style lang="scss" scoped>
.navigation_dialog-container {
  .title {
    height: 44px;
    padding: 10px 16px;
    font-size: 18px;
    color: var(--el-text-color-regular);
  }
}
</style>
