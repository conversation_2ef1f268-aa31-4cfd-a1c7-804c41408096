<script setup lang="ts">
import { ref } from 'vue'
import type { PatientAccountsBaseInfo } from '@/api/dto/discharge-settlement/patient-accounts.dto'
import PatientAccountsBaseInfoPanel from '../components/patient-accounts-base-info-panel.vue'
import type { PatientBillList } from '@/api/dto/discharge-settlement/bill-clearing.dto'
import InpatientInputDialog from '../components/inpatient-input-dialog.vue'
import { useMountDialog } from '@/hooks/useMountDialog.ts'
import PatientDataDialog from '../components/patient-data-dialog.vue'
import PatientBillInfoPanel from './components/patient-bill-info-panel.vue'
import { requestPatientBaseInfo, requestPatientBillList } from '@/api/discharge-settlement.api'

const { open: openInpatientInputDialog } = useMountDialog(InpatientInputDialog)
const { open: openPatientDataDialog } = useMountDialog(PatientDataDialog)

/**
 * 出院清账结算
 */

const patientAccountsBaseInfo = ref<PatientAccountsBaseInfo>({} as PatientAccountsBaseInfo)

/* 结算明细 */
const allDetailedBilling = ref<PatientBillList>({} as PatientBillList)

onMounted(() => {
  handleInputHospitalizationNo()
})

// 输入住院号
function handleInputHospitalizationNo() {
  openInpatientInputDialog({
    confirmCallback: async (data: any) => {
      console.log('输入住院号信息：', data)
      patientAccountsBaseInfo.value = {} as PatientAccountsBaseInfo
      allDetailedBilling.value = {} as PatientBillList
      await handleGetPatientBaseInfo(data.hospitalizationNo)
      await handleGetPatientBillList(patientAccountsBaseInfo.value.inpno)
    }
  })
}

// 获取病人基本信息
async function handleGetPatientBaseInfo(patno: string) {
  const res = await requestPatientBaseInfo({
    patno
  })
  patientAccountsBaseInfo.value = res
  console.log('病人基本信息', patientAccountsBaseInfo.value)
}

// 获取病人期账信息
async function handleGetPatientBillList(inpno: string) {
  const res = await requestPatientBillList({
    inpno
  })
  allDetailedBilling.value = res
}

// 查看病人资料
function handleViewPatientInfo() {
  console.log('查看病人资料')
  openPatientDataDialog({
    patientInfo: patientAccountsBaseInfo.value
  })
}

// 切换病人
function handleSwitchPatient() {
  console.log('切换病人')
  handleInputHospitalizationNo()
}
</script>

<template>
  <div class="page-container">
    <PatientAccountsBaseInfoPanel
      :base-info="patientAccountsBaseInfo"
      @view-patient-info="handleViewPatientInfo"
      @switch-patient="handleSwitchPatient"
    />

    <el-divider border-style="dashed" />

    <PatientBillInfoPanel
      :inpno="patientAccountsBaseInfo.inpno ?? ''"
      :patno="patientAccountsBaseInfo.patno ?? ''"
      :patient-bill-info="allDetailedBilling"
    />
  </div>
</template>

<style scoped lang="scss">
.page-container {
  padding: 20px;
}
</style>
