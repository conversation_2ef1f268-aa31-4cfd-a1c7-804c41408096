import {
  ReqSaveInpatientCaregiverIntroduceInfo,
  ResInpatientCaregiverIntroduceInfo,
  ResSaveInpatientCaregiverIntroduceInfo
} from '@/api/dto/rental-management/caregiver-intro-management.dto.ts'
import {
  ReqAddInpatientCaregiverPerson,
  ReqGetInpatientCaregiverPersonByPage,
  ReqInpatientCaregiverPersonId,
  ReqUpdateInpatientCaregiverPerson,
  ResGetInpatientCaregiverPersonByPage,
  ResInpatientCaregiverPersonMsg
} from '@/api/dto/rental-management/caregiver-management.dto.ts'
import { request } from '@/utils/axios-utils'
import type { ApiFunc } from 'axios'
import {
  ReqAddInpatientCaregiverType,
  ReqGetInpatientCaregiverTypeByPage,
  ReqInpatientCaregiverTypeId,
  ReqUpdateInpatientCaregiverType,
  ResGetInpatientCaregiverTypeByPage,
  ResInpatientCaregiverTypeMsg
} from '@/api/dto/rental-management/rental-type.dto.ts'
import {
  CreateRentalOrderInfo,
  ReqConfirmLease,
  ReqConfirmLeaseOfCaregiverPerson,
  ReqModifyReview,
  ReqPersonnelReplacementReview,
  ReqRentalOrderDetail,
  ReqRentalOrdersByPage,
  ResConfirmLeaseOfCaregiverPerson,
  ResModifyReviewInfo,
  ResPersonnelReplacementReviewInfo,
  ResRentalOrderDetail,
  ResRentalOrdersByPage,
  RentalPaymentInfo,
  ServiceTypeInfo,
  ReqSelectInpatientByPage,
  ResSelectInpatientByPage,
  ResRentalQuerydeptDTO
} from '@/api/dto/rental-management/rental-order-management.dto.ts'

// === 陪护介绍管理 ===
/**
 * 获取住院陪护介绍信息
 */
export const requestInpatientCaregiverIntroduceInfo: ApiFunc<void, ResInpatientCaregiverIntroduceInfo> = () => {
  return request({
    url: '/foying/web/InpatientCaregiverIntroduce/inpatientCaregiverIntroduceInfo',
    method: 'post'
  })
}

/**
 * 保存住院陪护介绍信息
 */
export const requestSaveInpatientCaregiverIntroduceInfo: ApiFunc<
  ReqSaveInpatientCaregiverIntroduceInfo,
  ResSaveInpatientCaregiverIntroduceInfo
> = (data) => {
  return request({
    url: '/foying/web/InpatientCaregiverIntroduce/saveInpatientCaregiverIntroduceInfo',
    method: 'post',
    data
  })
}

// === 陪护人管理 ===
/**
 * 陪护人管理 - 分页获取列表
 */
export const requestGetInpatientCaregiverPersonByPage: ApiFunc<
  ReqGetInpatientCaregiverPersonByPage,
  ResGetInpatientCaregiverPersonByPage
> = (data) => {
  return request({
    url: '/foying/web/InpatientCaregiverPerson/getInpatientCaregiverPersonByPage',
    method: 'post',
    data,
    retonly: false
  })
}

/**
 * 陪护人管理 - 新增
 */
export const requestAddInpatientCaregiverPerson: ApiFunc<
  ReqAddInpatientCaregiverPerson,
  ResInpatientCaregiverPersonMsg
> = (data) => {
  return request({
    url: '/foying/web/InpatientCaregiverPerson/addInpatientCaregiverPerson',
    method: 'post',
    data
  })
}

/**
 * 陪护人管理 - 修改
 */
export const requestUpdateInpatientCaregiverPerson: ApiFunc<
  ReqUpdateInpatientCaregiverPerson,
  ResInpatientCaregiverPersonMsg
> = (data) => {
  return request({
    url: '/foying/web/InpatientCaregiverPerson/updateInpatientCaregiverPerson',
    method: 'post',
    data
  })
}

/**
 * 陪护人管理 - 启用
 */
export const requestEnabledInpatientCaregiverPerson: ApiFunc<
  ReqInpatientCaregiverPersonId,
  ResInpatientCaregiverPersonMsg
> = (data) => {
  return request({
    url: '/foying/web/InpatientCaregiverPerson/enabledInpatientCaregiverPerson',
    method: 'post',
    data
  })
}

/**
 * 陪护人管理 - 禁用
 */
export const requestDisabledInpatientCaregiverPerson: ApiFunc<
  ReqInpatientCaregiverPersonId,
  ResInpatientCaregiverPersonMsg
> = (data) => {
  return request({
    url: '/foying/web/InpatientCaregiverPerson/disabledInpatientCaregiverPerson',
    method: 'post',
    data
  })
}

/**
 * 陪护人管理 - 删除
 */
export const requestDeleteInpatientCaregiverPerson: ApiFunc<
  ReqInpatientCaregiverPersonId,
  ResInpatientCaregiverPersonMsg
> = (data) => {
  return request({
    url: '/foying/web/InpatientCaregiverPerson/deleteInpatientCaregiverPerson',
    method: 'post',
    data
  })
}

// === 陪护类型管理 ===
/**
 * 陪护类型管理 - 分页获取列表
 */
export const requestGetInpatientCaregiverTypeByPage: ApiFunc<
  ReqGetInpatientCaregiverTypeByPage,
  ResGetInpatientCaregiverTypeByPage
> = (data) => {
  return request({
    url: '/foying/web/InpatientCaregiverType/getInpatientCaregiverTypeByPage',
    method: 'post',
    data,
    retonly: false
  })
}

/**
 * 陪护类型管理 - 新增
 */
export const requestAddInpatientCaregiverType: ApiFunc<ReqAddInpatientCaregiverType, ResInpatientCaregiverTypeMsg> = (
  data
) => {
  return request({
    url: '/foying/web/InpatientCaregiverType/addInpatientCaregiverType',
    method: 'post',
    data
  })
}

/**
 * 陪护类型管理 - 修改
 */
export const requestUpdateInpatientCaregiverType: ApiFunc<
  ReqUpdateInpatientCaregiverType,
  ResInpatientCaregiverTypeMsg
> = (data) => {
  return request({
    url: '/foying/web/InpatientCaregiverType/updateInpatientCaregiverType',
    method: 'post',
    data
  })
}

/**
 * 陪护类型管理 - 启用
 */
export const requestEnableInpatientCaregiverType: ApiFunc<ReqInpatientCaregiverTypeId, ResInpatientCaregiverTypeMsg> = (
  data
) => {
  return request({
    url: '/foying/web/InpatientCaregiverType/enableInpatientCaregiverType',
    method: 'post',
    data
  })
}

/**
 * 陪护类型管理 - 禁用
 */
export const requestDisableInpatientCaregiverType: ApiFunc<
  ReqInpatientCaregiverTypeId,
  ResInpatientCaregiverTypeMsg
> = (data) => {
  return request({
    url: '/foying/web/InpatientCaregiverType/disableInpatientCaregiverType',
    method: 'post',
    data
  })
}

/**
 * 陪护类型管理 - 删除
 */
export const requestDeleteInpatientCaregiverType: ApiFunc<ReqInpatientCaregiverTypeId, ResInpatientCaregiverTypeMsg> = (
  data
) => {
  return request({
    url: '/foying/web/InpatientCaregiverType/deleteInpatientCaregiverType',
    method: 'post',
    data
  })
}

// === 租赁订单管理 ===
/**
 * 租赁订单管理 - 分页获取列表
 */
export const requestGetInpatientCaregiverByPage: ApiFunc<ReqRentalOrdersByPage, ResRentalOrdersByPage> = (data) => {
  return request({
    url: '/foying/web/InpatientCaregiver/getInpatientCaregiverByPage',
    method: 'post',
    data,
    retonly: false
  })
}

/**
 * 租赁订单管理 - 获取订单详情
 */
export const requestInpatientCaregiverOrderInfo: ApiFunc<ReqRentalOrderDetail, ResRentalOrderDetail> = (data) => {
  return request({
    url: '/foying/web/InpatientCaregiver/inpatientCaregiverOrderInfo',
    method: 'post',
    data
  })
}

/** 
 * 租赁订单管理 - 选择科室列表
 */
export const requestRentalSelectDepartmentList: ApiFunc<void, ResRentalQuerydeptDTO[]> = () => {
  return request({
    url: '/foying/web/InpatientCaregiver/selectDepartmentList',
    method: 'post'
  })
}

/**
 * 创建订单 - 获取住院人列表
 */
export const requestSelectInpatientByPage: ApiFunc<ReqSelectInpatientByPage, ResSelectInpatientByPage> = (data) => {
  return request({
    url: '/foying/web/InpatientCaregiver/selectPatientList',
    method: 'post',
    data,
    retonly: false
  })
}

/**
 * 创建订单 - 获取服务类型
 */
export const requestGetServiceType: ApiFunc<void, ServiceTypeInfo[]> = () => {
  return request({
    url: '/foying/web/InpatientCaregiver/getServiceType',
    method: 'post'
  })
}

/**
 * 创建订单
 */
export const requestCreateInpatientCaregiverOrder: ApiFunc<CreateRentalOrderInfo, { msg: string }> = (data) => {
  return request({
    url: '/foying/web/InpatientCaregiver/createInpatientCaregiverOrder',
    method: 'post',
    data
  })
}

/**
 * 确定租赁 - 选择陪护人列表
 */
export const requestConfirmLeaseOfCaregiverPerson: ApiFunc<
  ReqConfirmLeaseOfCaregiverPerson,
  ResConfirmLeaseOfCaregiverPerson
> = (data) => {
  return request({
    url: '/foying/web/InpatientCaregiver/confirmLeaseOfCaregiverPerson',
    method: 'post',
    data,
    retonly: false,
    loading: false
  })
}

/**
 * 确定租赁
 */
export const requestConfirmLease: ApiFunc<ReqConfirmLease, { msg: string }> = (data) => {
  return request({
    url: '/foying/web/InpatientCaregiver/confirmLease',
    method: 'post',
    data
  })
}

/**
 * 修改审核信息
 */
export const requestModifyReviewInfo: ApiFunc<{ inpatientCaregiverId: string }, ResModifyReviewInfo> = (data) => {
  return request({
    url: '/foying/web/InpatientCaregiver/modifyReviewInfo',
    method: 'post',
    data
  })
}

/**
 * 修改审核
 */
export const requestModifyReview: ApiFunc<ReqModifyReview, { msg: string }> = (data) => {
  return request({
    url: '/foying/web/InpatientCaregiver/modifyReview',
    method: 'post',
    data
  })
}

/**
 * 换人审核信息
 */
export const requestPersonnelReplacementReviewInfo: ApiFunc<
  { inpatientCaregiverId: string },
  ResPersonnelReplacementReviewInfo
> = (data) => {
  return request({
    url: '/foying/web/InpatientCaregiver/personnelReplacementReviewInfo',
    method: 'post',
    data
  })
}

/**
 * 换人审核
 */
export const requestPersonnelReplacementReview: ApiFunc<ReqPersonnelReplacementReview, { msg: string }> = (data) => {
  return request({
    url: '/foying/web/InpatientCaregiver/personnelReplacementReview',
    method: 'post',
    data
  })
}

/**
 * 服务完成
 */
export const requestServiceCompleted: ApiFunc<{ inpatientCaregiverId: string }, { msg: string }> = (data) => {
  return request({
    url: '/foying/web/InpatientCaregiver/serviceCompleted',
    method: 'post',
    data
  })
}

/**
 * 补缴支付信息
 */
export const requestSupplementaryPaymentInformation: ApiFunc<{ inpatientCaregiverId: string }, RentalPaymentInfo> = (
  data
) => {
  return request({
    url: '/foying/web/InpatientCaregiver/supplementaryPaymentInformation',
    method: 'post',
    data
  })
}

/**
 * 取消订单
 */
export const requestInpatientCaregiverCancelOrder: ApiFunc<{ inpatientCaregiverId: string }, { msg: string }> = (
  data
) => {
  return request({
    url: '/foying/web/InpatientCaregiver/cancelOrder',
    method: 'post',
    data
  })
}
