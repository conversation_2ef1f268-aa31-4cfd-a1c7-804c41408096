import type { ApiFunc } from 'axios'
import { authRequest } from '../utils.api'
import type { GetSysOperationLogForPageInput, GetSysOperationLogForPageOutput } from '../dto/system/operation-log.dto'

/**操作日志分页 */
export const getSysOperationLogForPageApi: ApiFunc<GetSysOperationLogForPageInput, GetSysOperationLogForPageOutput> = (
  data,
  options
) => {
  return authRequest({ url: 'getSysOperationLogForPage', data, ...options })
}
