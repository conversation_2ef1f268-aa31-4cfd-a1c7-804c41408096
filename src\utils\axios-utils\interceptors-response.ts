import { isBoolean, isFunction, isString } from 'lodash-es'

import { parseTime } from '..'
import router from '@/router'
import type { AxiosError, AxiosResponse, InternalAxiosRequestConfig } from 'axios'
import { useAxiosStore } from '@/stores/axios.store'
import { closeLoading } from './axios-loading'
import { request } from '.'
import { appMessage } from '@/hooks/useNaiveApi'
import { PAN_GU_RESPONSE_CODE, type PanGuResponse } from '@/types'
import { useUserStore } from '@/stores/user.store'
import { ROUTER_PATH } from '@/router/router-path'
import { ElMessageBox } from 'element-plus'

/**前端异常收集日志函数（埋点） */
export const handleLocalLog = (resultMsg: string) => {
  const { fullPath: routerFullPath, name: routerName } = router.currentRoute.value
  const content = `时间：${parseTime(`{Y}-{M}-{D} 星期{d} {h}:{m}:{s}`)}
触发页面路由：${routerFullPath}(${routerName?.toString()})
触发页面路由名字：${routerName?.toString()}
返回信息：${resultMsg || '未知错误'}`
  if (import.meta.env.MODE == 'production') {
    console.log(content)
  } else {
    console.log(content)
  }
}

/**处理arraybuffer数据流 */
const handleArrayBuffer = (data: ArrayBuffer) => {
  return btoa(new Uint8Array(data).reduce((value, item) => value + String.fromCharCode(item), ''))
}

/**关闭loading */
const handleCloseLoading = (config: InternalAxiosRequestConfig) => {
  const { loading, autoCloseLoading } = config
  if (autoCloseLoading) {
    isBoolean(loading) && loading && closeLoading()
    isFunction(loading) && loading(false)
  }
}

/**处理请求返回数据 */
const handleResponse = (config: InternalAxiosRequestConfig, res: PanGuResponse) => {
  const { retonly, mute, showNoData } = config

  if (!res.data) {
    res = { resultCode: res.resultCode, resultMsg: res.resultMsg || res.resultMessage || '', data: res }
  }

  try {
    const { resultCode, data } = res
    let { resultMsg } = res
    if (resultCode === PAN_GU_RESPONSE_CODE.SUCCESS) {
      if (retonly) {
        return data
      } else {
        return res
      }
    } else if (!mute) {
      resultMsg = resultMsg || `请稍后再试(${resultCode})`
      if (resultCode === PAN_GU_RESPONSE_CODE.NORECORD) {
        showNoData && appMessage.warning(resultMsg)
      } else if (!/403|cancel/.test(resultCode)) {
        appMessage.error(resultMsg)
      }
    }
    return Promise.reject(res)
  } finally {
    handleCloseLoading(config)
  }
}

/**响应拦截器成功回调 */
export function successResponse(config: AxiosResponse) {
  //移除请求记录
  useAxiosStore().removeAxiosPending(config.config)
  useUserStore().captchaimagetoken = config.headers.captchaimagetoken

  let res = config.data

  if (config.config.responseType !== 'json') {
    const { status, data } = config
    res = {
      resultCode: status === 200 ? PAN_GU_RESPONSE_CODE.SUCCESS : PAN_GU_RESPONSE_CODE.FAIL,
      resultMsg: status === 200 ? '获取成功' : '获取失败',
      data
    }
    if (config.config.responseType === 'arraybuffer') {
      res.data = handleArrayBuffer(data)
    }
  }

  return handleResponse(config.config, res)
}

/**响应拦截器错误回调 */
export function errorResponse(error: AxiosError<PanGuResponse>) {
  console.dir(error)
  //取消请求
  if (error.message === 'canceled') {
    closeLoading()
    return Promise.reject(error)
  } else {
    const { response, message, config } = error

    useAxiosStore().removeAxiosPending(config)

    /**请求错误提示 */
    let resultMsg = ''

    switch (response?.status) {
      case 401:
        return handle401(config)
      case 403:
        return handle403(config)
      case 404:
        resultMsg = `${response?.statusText}! 请确认请求路径`
        break
      case 500:
        if (response.data && response.data.resultMsg) {
          resultMsg = response.data.resultMsg
        } else {
          resultMsg = '系统繁忙'
          config?.jump500 && router.push(ROUTER_PATH.SERVER_ERROR)
        }
        break
    }

    if (message) {
      if (message.includes('Network Error')) {
        resultMsg = '网络异常！请检查网络连接'
      } else if (message.includes('timeout')) {
        resultMsg = '请求超时，请稍后重试'
      }
    }

    if (config) {
      return handleResponse(config!, { resultCode: PAN_GU_RESPONSE_CODE.FAIL, resultMsg, data: null })
    } else {
      const userStore = useUserStore()
      userStore.initUserInfo()
      userStore.initUserRouter()
      router.replace(ROUTER_PATH.LOGIN)
      return Promise.reject(message)
    }
  }
}

/**重置token锁🔒 */
let resetTokenLock = false
/**401无感刷新token逻辑 */
const handle401 = async (config: AxiosError<PanGuResponse>['config']) => {
  console.log('401')

  if (!config) return

  console.log('401-config')
  config.data = isString(config.data) ? JSON.parse(config.data) : config.data

  // 可能存在并发接口的场景，只有请求属于当前路由才发起重发，否则为其他路由请求，不进重发
  if (config.triggerRoute !== router.currentRoute.value.path) {
    console.log('401-canceld')
    closeLoading()
    return Promise.reject('canceled')
  }

  // 防止重复刷新token
  if (resetTokenLock) {
    console.log('401-settimeout')
    await new Promise<void>((resolve) => {
      const timer = setTimeout(() => {
        if (!resetTokenLock) {
          clearTimeout(timer)
          resolve()
        }
      }, 1000)
    })

    if (config.triggerRoute !== router.currentRoute.value.path) {
      console.log('401-settimeout-canceled')
      closeLoading()
      return Promise.reject('canceled')
    } else {
      console.log('401-settimeout-requset')
      return await request(config)
    }
  }

  resetTokenLock = true
  const userStore = useUserStore()
  try {
    console.log('401-resettoken')
    await userStore.resetToken()
    resetTokenLock = false
    console.log('401-resettoken-request')
    return await request(config)
  } catch (error) {
    console.log('401-resettoken-error')
    router.replace({ path: ROUTER_PATH.LOGIN, query: { redirect: router.currentRoute.value.fullPath } })

    config && handleCloseLoading(config)
  }
}

/**403提示锁🔒 */
let showToast = false

const handle403 = async (config: AxiosError<PanGuResponse>['config']) => {
  const cleanup = () => {
    if (config) {
      useAxiosStore().removeAxiosPending(config)
      handleCloseLoading(config)
    }
  }

  if (showToast) {
    cleanup() // 即使被跳过，也执行清理
    return Promise.reject('canceled_due_to_lock') // 返回一个更明确的原因
  }

  try {
    showToast = true
    await ElMessageBox.alert('请求失败！没有该功能的操作权限！', '提示')
  } catch (error) {
    console.error('Error displaying ElMessageBox alert for 403:', error)
    return Promise.reject('canceled_alert_error') // 返回一个明确的原因
  } finally {
    showToast = false // 释放锁
    cleanup() // 执行统一清理
  }

  return Promise.reject('canceled_handled_403')
}
